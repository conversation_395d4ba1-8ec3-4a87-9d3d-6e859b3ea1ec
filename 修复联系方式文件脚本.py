#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复联系方式文件脚本
修复元标签格式并重命名文件
"""

import os
import re
from pathlib import Path

def fix_contact_file(file_path):
    """修复单个联系方式文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取省份信息
        if '北京市' in str(file_path):
            province = '北京'
        elif '天津市' in str(file_path):
            province = '天津'
        elif '上海市' in str(file_path):
            province = '上海'
        elif '重庆市' in str(file_path):
            province = '重庆'
        elif '河北省' in str(file_path):
            province = '河北'
        elif '河南省' in str(file_path):
            province = '河南'
        elif '浙江省' in str(file_path):
            province = '浙江'
        elif '江苏省' in str(file_path):
            province = '江苏'
        elif '福建省' in str(file_path):
            province = '福建'
        elif '安徽省' in str(file_path):
            province = '安徽'
        elif '贵州省' in str(file_path):
            province = '贵州'
        elif '山东省' in str(file_path):
            province = '山东'
        elif '广东省' in str(file_path):
            province = '广东'
        elif '陕西省' in str(file_path):
            province = '陕西'
        elif '甘肃省' in str(file_path):
            province = '甘肃'
        elif '山西省' in str(file_path):
            province = '山西'
        elif '江西省' in str(file_path):
            province = '江西'
        elif '云南省' in str(file_path):
            province = '云南'
        elif '湖北省' in str(file_path):
            province = '湖北'
        elif '湖南省' in str(file_path):
            province = '湖南'
        elif '青海省' in str(file_path):
            province = '青海'
        elif '辽宁省' in str(file_path):
            province = '辽宁'
        elif '吉林省' in str(file_path):
            province = '吉林'
        elif '黑龙江省' in str(file_path):
            province = '黑龙江'
        elif '海南省' in str(file_path):
            province = '海南'
        elif '四川省' in str(file_path):
            province = '四川'
        elif '广西' in str(file_path):
            province = '广西'
        elif '宁夏' in str(file_path):
            province = '宁夏'
        elif '西藏' in str(file_path):
            province = '西藏'
        elif '内蒙古' in str(file_path):
            province = '内蒙古'
        elif '新疆维吾尔自治区' in str(file_path):
            province = '新疆'
        elif '新疆生产建设兵团' in str(file_path):
            province = '新疆兵团'
        else:
            province = '未知'
        
        # 修复元标签格式
        # 找到标题
        title_match = re.search(r'^# (.+)', content, re.MULTILINE)
        if not title_match:
            return None, None
        
        title = title_match.group(1)
        
        # 提取主要内容（去掉元标签部分）
        content_parts = content.split('---')
        if len(content_parts) >= 3:
            main_content = '---'.join(content_parts[2:])
        else:
            main_content = content
        
        # 生成新的元标签
        new_meta = f"""---
tags: [{province}, 网信办, 数据出境, 联系方式]
---"""
        
        # 重新组合内容
        new_content = f"# {title}\n\n{new_meta}\n\n{main_content.strip()}"
        
        # 生成新文件名
        new_filename = f"{province}网信办.md"
        
        return new_content, new_filename
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None, None

def main():
    """主函数"""
    print("开始修复联系方式文件...")
    
    # 查找所有联系方式文件
    output_dir = Path('数据出境合规实务50问汇总')
    contact_files = []
    
    for file_path in output_dir.glob('联系方式_*网信办.md'):
        contact_files.append(file_path)
    
    print(f"找到 {len(contact_files)} 个网信办联系方式文件")
    
    # 修复每个文件
    for file_path in contact_files:
        print(f"处理文件: {file_path.name}")
        
        new_content, new_filename = fix_contact_file(file_path)
        
        if new_content and new_filename:
            # 写入新文件
            new_file_path = output_dir / new_filename
            with open(new_file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            # 删除旧文件
            file_path.unlink()
            
            print(f"  -> 重命名为: {new_filename}")
        else:
            print(f"  -> 处理失败")
    
    print("\n修复完成！")

if __name__ == "__main__":
    main()
