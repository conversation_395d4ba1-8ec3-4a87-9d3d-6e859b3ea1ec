#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复元标签格式脚本
修复YAML front matter中的属性格式，不填充内容
"""

import os
from pathlib import Path

# 网信办数据（只包含基本信息，其他字段留空）
contact_data = {
    "国家网信办.md": {
        "省份": "国家级",
        "级别": "国家级",
        "地址": "北京市西城区车公庄大街11号",
        "电话": "数据出境安全评估申报: 010-55627135; 个人信息出境标准合同备案: 010-55627565; 个人信息保护认证申请: 010-82261100",
        "名称": "国家互联网信息办公室"
    },
    "北京网信办.md": {
        "省份": "北京",
        "级别": "直辖市",
        "地址": "北京市朝阳区华威南路弘善家园413号",
        "电话": "010-67676912",
        "名称": "北京市互联网信息办公室"
    },
    "天津网信办.md": {
        "省份": "天津",
        "级别": "直辖市",
        "地址": "天津市河西区梅江道20号",
        "电话": "022-88355322",
        "名称": "天津市互联网信息办公室"
    },
    "上海网信办.md": {
        "省份": "上海",
        "级别": "直辖市",
        "地址": "上海市徐汇区宛平路315号",
        "电话": "021-64743030-2711",
        "名称": "上海市互联网信息办公室"
    },
    "重庆网信办.md": {
        "省份": "重庆",
        "级别": "直辖市",
        "地址": "重庆市渝北区青竹东路6号",
        "电话": "023-63151805",
        "名称": "重庆市互联网信息办公室"
    },
    "河北网信办.md": {
        "省份": "河北",
        "级别": "省级",
        "地址": "河北省石家庄市桥西区维明南大街79号",
        "电话": "0311-87909716",
        "名称": "河北省互联网信息办公室"
    },
    "河南网信办.md": {
        "省份": "河南",
        "级别": "省级",
        "地址": "河南省郑州市金水区金水路16号",
        "电话": "0371-65901067",
        "名称": "河南省互联网信息办公室"
    },
    "浙江网信办.md": {
        "省份": "浙江",
        "级别": "省级",
        "地址": "浙江省杭州市西湖区省府路29号",
        "电话": "0571-81051250",
        "名称": "浙江省互联网信息办公室"
    },
    "江苏网信办.md": {
        "省份": "江苏",
        "级别": "省级",
        "地址": "江苏省南京市建邺区白龙江东街8号",
        "电话": "025-63090194",
        "名称": "江苏省互联网信息办公室"
    },
    "福建网信办.md": {
        "省份": "福建",
        "级别": "省级",
        "地址": "福建省福州市鼓楼区北大路133号",
        "电话": "0591-86300613",
        "名称": "福建省互联网信息办公室"
    },
    "安徽网信办.md": {
        "省份": "安徽",
        "级别": "省级",
        "地址": "安徽省合肥市包河区中山路1号",
        "电话": "0551-62606014",
        "名称": "安徽省互联网信息办公室"
    },
    "贵州网信办.md": {
        "省份": "贵州",
        "级别": "省级",
        "地址": "贵州省贵阳市云岩区宝山北路39号",
        "电话": "0851-82995001 / 0851-82995061",
        "名称": "贵州省互联网信息办公室"
    },
    "山东网信办.md": {
        "省份": "山东",
        "级别": "省级",
        "地址": "山东省济南市市中区经十路20637号",
        "电话": "0531-51773249 / 0531-51771297",
        "名称": "山东省互联网信息办公室"
    },
    "广东网信办.md": {
        "省份": "广东",
        "级别": "省级",
        "地址": "广东省广州市越秀区中山一路104号",
        "电话": "020-87100794 / 020-87100793",
        "名称": "广东省互联网信息办公室"
    },
    "陕西网信办.md": {
        "省份": "陕西",
        "级别": "省级",
        "地址": "陕西省西安市雁塔区雁塔路南段10号",
        "电话": "029-63907136",
        "名称": "陕西省互联网信息办公室"
    },
    "甘肃网信办.md": {
        "省份": "甘肃",
        "级别": "省级",
        "地址": "甘肃省兰州市城关区南昌路1648号",
        "电话": "0931-8928721",
        "名称": "甘肃省互联网信息办公室"
    },
    "山西网信办.md": {
        "省份": "山西",
        "级别": "省级",
        "地址": "山西省太原市迎泽区五一路36号",
        "电话": "0351-5236020",
        "名称": "山西省互联网信息办公室"
    },
    "江西网信办.md": {
        "省份": "江西",
        "级别": "省级",
        "地址": "江西省南昌市红谷滩区卧龙路999号",
        "电话": "0791-88912737",
        "名称": "江西省互联网信息办公室"
    },
    "云南网信办.md": {
        "省份": "云南",
        "级别": "省级",
        "地址": "云南省昆明市西山区日新中路516号",
        "电话": "0871-63902424",
        "名称": "云南省互联网信息办公室"
    },
    "湖北网信办.md": {
        "省份": "湖北",
        "级别": "省级",
        "地址": "湖北省武汉市武昌区水果湖路268号",
        "电话": "027-87231397",
        "名称": "湖北省互联网信息办公室"
    },
    "湖南网信办.md": {
        "省份": "湖南",
        "级别": "省级",
        "地址": "湖南省长沙市芙蓉区韶山北路1号",
        "电话": "0731-81121089",
        "名称": "湖南省互联网信息办公室"
    },
    "青海网信办.md": {
        "省份": "青海",
        "级别": "省级",
        "地址": "青海省西宁市海湖新区文景街32号",
        "电话": "0971-8485510",
        "名称": "青海省互联网信息办公室"
    },
    "辽宁网信办.md": {
        "省份": "辽宁",
        "级别": "省级",
        "地址": "辽宁省沈阳市和平区光荣街26号甲",
        "电话": "024-81680082",
        "名称": "辽宁省互联网信息办公室"
    },
    "吉林网信办.md": {
        "省份": "吉林",
        "级别": "省级",
        "地址": "吉林省长春市朝阳区新发路666号",
        "电话": "0431-82761087",
        "名称": "吉林省互联网信息办公室"
    },
    "黑龙江网信办.md": {
        "省份": "黑龙江",
        "级别": "省级",
        "地址": "黑龙江省哈尔滨市南岗区华山路12号",
        "电话": "0451-58685723",
        "名称": "黑龙江省互联网信息办公室"
    },
    "海南网信办.md": {
        "省份": "海南",
        "级别": "省级",
        "地址": "海南省海口市国兴大道69号",
        "电话": "0898-65380723",
        "名称": "海南省互联网信息办公室"
    },
    "四川网信办.md": {
        "省份": "四川",
        "级别": "省级",
        "地址": "四川省成都市青羊区桂花巷21号",
        "电话": "028-86601862",
        "名称": "四川省互联网信息办公室"
    },
    "广西网信办.md": {
        "省份": "广西",
        "级别": "自治区",
        "地址": "广西壮族自治区南宁市青秀区民族大道112号",
        "电话": "0771-2093017 / 0771-2093049",
        "名称": "广西壮族自治区互联网信息办公室"
    },
    "宁夏网信办.md": {
        "省份": "宁夏",
        "级别": "自治区",
        "地址": "宁夏回族自治区银川市金风区康平路1号",
        "电话": "0951-6668938",
        "名称": "宁夏回族自治区互联网信息办公室"
    },
    "西藏网信办.md": {
        "省份": "西藏",
        "级别": "自治区",
        "地址": "西藏自治区拉萨市城关区农科路7号",
        "电话": "0891-6591509",
        "名称": "西藏自治区互联网信息办公室"
    },
    "内蒙古网信办.md": {
        "省份": "内蒙古",
        "级别": "自治区",
        "地址": "内蒙古自治区呼和浩特市赛罕区银河南街8号",
        "电话": "0471-4821277",
        "名称": "内蒙古自治区互联网信息办公室"
    },
    "新疆网信办.md": {
        "省份": "新疆",
        "级别": "自治区",
        "地址": "新疆维吾尔自治区乌鲁木齐市新市区西环北路2221号",
        "电话": "0991-2384855",
        "名称": "新疆维吾尔自治区互联网信息办公室"
    },
    "新疆兵团网信办.md": {
        "省份": "新疆兵团",
        "级别": "兵团",
        "地址": "新疆维吾尔自治区乌鲁木齐市天山区中山路462号",
        "电话": "0991-2899091",
        "名称": "新疆生产建设兵团互联网信息办公室"
    }
}

def generate_correct_format(filename, data):
    """生成正确格式的文件内容"""
    content = f"""---
省份: {data['省份']}
级别: {data['级别']}
地址: {data['地址']}
电话: {data['电话']}
名称: {data['名称']}
负责人:
负责人联系方式:
地区政策:
注意点:
---

"""
    return content

def main():
    """主函数"""
    print("开始修复元标签格式...")
    
    netinfo_dir = Path('数据出境合规实务50问汇总/网信办联系方式')
    
    # 修复每个网信办文件
    for filename, data in contact_data.items():
        file_path = netinfo_dir / filename
        
        if file_path.exists():
            content = generate_correct_format(filename, data)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"修复文件: {filename}")
        else:
            print(f"文件不存在: {filename}")
    
    print(f"\n修复完成！共处理 {len(contact_data)} 个文件")

if __name__ == "__main__":
    main()
