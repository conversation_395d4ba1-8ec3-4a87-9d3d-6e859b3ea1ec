{"resource/components/area": {"path": "/apps/obsidian/德州扑克/resource/components/area", "isdir": true, "fsid": 375733867223683, "ctime": 1754304436, "mtime": 1754304436, "size": 0}, "resource/attachments": {"path": "/apps/obsidian/德州扑克/resource/attachments", "isdir": true, "fsid": 769830793746567, "ctime": 1754304423, "mtime": 1754304423, "size": 0}, "resource/components": {"path": "/apps/obsidian/德州扑克/resource/components", "isdir": true, "fsid": 142181485269503, "ctime": 1754304436, "mtime": 1754304436, "size": 0}, "工作室项目/免责条款/extracted_disclaimers": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/extracted_disclaimers", "isdir": true, "fsid": 802602547997592, "ctime": 1754304918, "mtime": 1754304918, "size": 0}, "resource/images": {"path": "/apps/obsidian/德州扑克/resource/images", "isdir": true, "fsid": 124948428027219, "ctime": 1754304491, "mtime": 1754304491, "size": 0}, "笔记/inbox笔记": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记", "isdir": true, "fsid": 717033703114217, "ctime": 1754305595, "mtime": 1754305595, "size": 0}, "resource": {"path": "/apps/obsidian/德州扑克/resource", "isdir": true, "fsid": 107857977612122, "ctime": 1754304423, "mtime": 1754304423, "size": 0}, "Tags/Script": {"path": "/apps/obsidian/德州扑克/Tags/Script", "isdir": true, "fsid": 303165652920972, "ctime": 1754304513, "mtime": 1754304513, "size": 0}, "resource/components/scripts": {"path": "/apps/obsidian/德州扑克/resource/components/scripts", "isdir": true, "fsid": 476466502987850, "ctime": 1754304447, "mtime": 1754304447, "size": 0}, "工作室项目/卢曼卡片/script和表单": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单", "isdir": true, "fsid": 391619793975258, "ctime": 1754305115, "mtime": 1754305115, "size": 0}, "Tags": {"path": "/apps/obsidian/德州扑克/Tags", "isdir": true, "fsid": 223174113340661, "ctime": 1754304513, "mtime": 1754304513, "size": 0}, "resource/components/views": {"path": "/apps/obsidian/德州扑克/resource/components/views", "isdir": true, "fsid": 1105868655950716, "ctime": 1754304452, "mtime": 1754304452, "size": 0}, "笔记": {"path": "/apps/obsidian/德州扑克/笔记", "isdir": true, "fsid": 1120507230998859, "ctime": 1754305595, "mtime": 1754305595, "size": 0}, "工作室项目/卢曼卡片/script和表单/表单模板": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板", "isdir": true, "fsid": 81679294529126, "ctime": 1754305470, "mtime": 1754305470, "size": 0}, "德州笔记同步": {"path": "/apps/obsidian/德州扑克/德州笔记同步", "isdir": true, "fsid": 555260570358797, "ctime": 1754305528, "mtime": 1754305528, "size": 0}, "口语训练/杂七杂八/动图": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图", "isdir": true, "fsid": 445988318702011, "ctime": 1754304711, "mtime": 1754304711, "size": 0}, "概率": {"path": "/apps/obsidian/德州扑克/概率", "isdir": true, "fsid": 819409861263006, "ctime": 1754305591, "mtime": 1754305591, "size": 0}, "工作室": {"path": "/apps/obsidian/德州扑克/工作室", "isdir": true, "fsid": 727175951559075, "ctime": 1754305519, "mtime": 1754305519, "size": 0}, "工作室项目": {"path": "/apps/obsidian/德州扑克/工作室项目", "isdir": true, "fsid": 498337781022138, "ctime": 1754304918, "mtime": 1754304918, "size": 0}, "工作室/肌肉": {"path": "/apps/obsidian/德州扑克/工作室/肌肉", "isdir": true, "fsid": 102375348071778, "ctime": 1754305519, "mtime": 1754305519, "size": 0}, "resource/脚本工具": {"path": "/apps/obsidian/德州扑克/resource/脚本工具", "isdir": true, "fsid": 759736122146621, "ctime": 1754304504, "mtime": 1754304504, "size": 0}, "德州笔记同步/教学法": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法", "isdir": true, "fsid": 857619634437717, "ctime": 1754305530, "mtime": 1754305530, "size": 0}, "英语/口语训练/具体练习": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习", "isdir": true, "fsid": 163468056007522, "ctime": 1754305617, "mtime": 1754305617, "size": 0}, "英语/口语训练/口语练习": {"path": "/apps/obsidian/德州扑克/英语/口语训练/口语练习", "isdir": true, "fsid": 911378418701901, "ctime": 1754305631, "mtime": 1754305631, "size": 0}, "口语训练": {"path": "/apps/obsidian/德州扑克/口语训练", "isdir": true, "fsid": 851327952388561, "ctime": 1754304709, "mtime": 1754304709, "size": 0}, "英语/口语训练": {"path": "/apps/obsidian/德州扑克/英语/口语训练", "isdir": true, "fsid": 578310143897440, "ctime": 1754305617, "mtime": 1754305617, "size": 0}, "工作室/肌肉/生成笔记/跨境数据传输": {"path": "/apps/obsidian/德州扑克/工作室/肌肉/生成笔记/跨境数据传输", "isdir": true, "fsid": 620407679424497, "ctime": 1754305524, "mtime": 1754305524, "size": 0}, "口语训练/练习场景": {"path": "/apps/obsidian/德州扑克/口语训练/练习场景", "isdir": true, "fsid": 31433844208790, "ctime": 1754304906, "mtime": 1754304906, "size": 0}, "工作室项目/卢曼卡片/script和表单/卢曼表单": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单", "isdir": true, "fsid": 74429216778937, "ctime": 1754305262, "mtime": 1754305262, "size": 0}, "工作室项目/卢曼卡片": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片", "isdir": true, "fsid": 1038189804294680, "ctime": 1754305115, "mtime": 1754305115, "size": 0}, "工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板", "isdir": true, "fsid": 597088835367080, "ctime": 1754305286, "mtime": 1754305286, "size": 0}, "英语/口语训练/录音": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音", "isdir": true, "fsid": 83469336221969, "ctime": 1754305635, "mtime": 1754305635, "size": 0}, "工作室/肌肉/生成笔记/每日合规思考": {"path": "/apps/obsidian/德州扑克/工作室/肌肉/生成笔记/每日合规思考", "isdir": true, "fsid": 1079114079444793, "ctime": 1754305519, "mtime": 1754305519, "size": 0}, "工作室项目/免责条款/免责": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责", "isdir": true, "fsid": 1079073319149648, "ctime": 1754304943, "mtime": 1754304943, "size": 0}, "工作室项目/免责条款": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款", "isdir": true, "fsid": 827378780482875, "ctime": 1754304918, "mtime": 1754304918, "size": 0}, "resource/模板": {"path": "/apps/obsidian/德州扑克/resource/模板", "isdir": true, "fsid": 526308308781469, "ctime": 1754304489, "mtime": 1754304489, "size": 0}, "切分笔记": {"path": "/apps/obsidian/德州扑克/切分笔记", "isdir": true, "fsid": 1040658995021721, "ctime": 1754304567, "mtime": 1754304567, "size": 0}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单", "isdir": true, "fsid": 265525534797748, "ctime": 1754305425, "mtime": 1754305425, "size": 0}, "工作室项目/卢曼卡片/script和表单/人味表单": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单", "isdir": true, "fsid": 504988213060589, "ctime": 1754305342, "mtime": 1754305342, "size": 0}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板", "isdir": true, "fsid": 431593372851104, "ctime": 1754305460, "mtime": 1754305460, "size": 0}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板", "isdir": true, "fsid": 607261236559589, "ctime": 1754305344, "mtime": 1754305344, "size": 0}, "工作室/肌肉/生成笔记": {"path": "/apps/obsidian/德州扑克/工作室/肌肉/生成笔记", "isdir": true, "fsid": 555361615259632, "ctime": 1754305519, "mtime": 1754305519, "size": 0}, "工作室项目/免责条款/实用免责声明文件": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件", "isdir": true, "fsid": 949582424859858, "ctime": 1754304929, "mtime": 1754304929, "size": 0}, "resource/图片素材": {"path": "/apps/obsidian/德州扑克/resource/图片素材", "isdir": true, "fsid": 165462089934245, "ctime": 1754304472, "mtime": 1754304472, "size": 0}, "德州笔记同步/学习笔记": {"path": "/apps/obsidian/德州扑克/德州笔记同步/学习笔记", "isdir": true, "fsid": 238382961327613, "ctime": 1754305528, "mtime": 1754305528, "size": 0}, "德州笔记同步/训练笔记": {"path": "/apps/obsidian/德州扑克/德州笔记同步/训练笔记", "isdir": true, "fsid": 446725762511473, "ctime": 1754305586, "mtime": 1754305586, "size": 0}, "英语": {"path": "/apps/obsidian/德州扑克/英语", "isdir": true, "fsid": 302695400151442, "ctime": 1754305617, "mtime": 1754305617, "size": 0}, "口语训练/杂七杂八/英语练习插件": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件", "isdir": true, "fsid": 1028379498152207, "ctime": 1754304879, "mtime": 1754304879, "size": 0}, "英语/口语训练/语料卡": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡", "isdir": true, "fsid": 907778493579811, "ctime": 1754305651, "mtime": 1754305651, "size": 0}, "口语训练/杂七杂八": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八", "isdir": true, "fsid": 485832106749912, "ctime": 1754304709, "mtime": 1754304709, "size": 0}, "工作室项目/免责条款/正式免责声明文件": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件", "isdir": true, "fsid": 201454380193704, "ctime": 1754305099, "mtime": 1754305099, "size": 0}, "工作室项目/免责条款/实用免责声明文件/00-实用免责声明使用指南.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件/00-实用免责声明使用指南.md", "isdir": false, "fsid": 1005607817995356, "ctime": 1754216532, "mtime": 1754216568, "size": 7494}, "工作室项目/免责条款/正式免责声明文件/00-使用指南.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/00-使用指南.md", "isdir": false, "fsid": 622489607584552, "ctime": 1754215998, "mtime": 1754216445, "size": 5697}, "工作室项目/免责条款/正式免责声明文件/01-数据合规咨询服务协议.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/01-数据合规咨询服务协议.md", "isdir": false, "fsid": 1012072751957155, "ctime": 1754215673, "mtime": 1754215737, "size": 4868}, "工作室项目/免责条款/实用免责声明文件/01-隐私合规工作室服务协议.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件/01-隐私合规工作室服务协议.md", "isdir": false, "fsid": 1034648575048857, "ctime": 1754216159, "mtime": 1754216222, "size": 6273}, "工作室项目/免责条款/正式免责声明文件/02-合规评估报告免责声明.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/02-合规评估报告免责声明.md", "isdir": false, "fsid": 1080352103735177, "ctime": 1754215710, "mtime": 1754215753, "size": 4792}, "工作室项目/免责条款/实用免责声明文件/02-合规评估报告专用免责声明.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件/02-合规评估报告专用免责声明.md", "isdir": false, "fsid": 704072283434744, "ctime": 1754216204, "mtime": 1754216230, "size": 5672}, "工作室项目/免责条款/实用免责声明文件/03-跨境电商爬虫公司专用免责条款.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件/03-跨境电商爬虫公司专用免责条款.md", "isdir": false, "fsid": 13205391647191, "ctime": 1754216265, "mtime": 1754216270, "size": 7018}, "工作室项目/免责条款/正式免责声明文件/03-隐私政策审查服务条款.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/03-隐私政策审查服务条款.md", "isdir": false, "fsid": 1061020539272903, "ctime": 1754215753, "mtime": 1754215773, "size": 5523}, "工作室项目/免责条款/实用免责声明文件/04-工作室起步期避坑指南.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件/04-工作室起步期避坑指南.md", "isdir": false, "fsid": 356814623625950, "ctime": 1754216341, "mtime": 1754216396, "size": 7698}, "工作室项目/免责条款/正式免责声明文件/04-数据安全风险评估免责声明.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/04-数据安全风险评估免责声明.md", "isdir": false, "fsid": 274465603856861, "ctime": 1754215800, "mtime": 1754215810, "size": 6195}, "工作室项目/免责条款/实用免责声明文件/05-AI辅助合规服务免责声明.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件/05-AI辅助合规服务免责声明.md", "isdir": false, "fsid": 450298904483151, "ctime": 1754216399, "mtime": 1754216413, "size": 6978}, "工作室项目/免责条款/正式免责声明文件/05-数据跨境传输合规咨询免责条款.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/05-数据跨境传输合规咨询免责条款.md", "isdir": false, "fsid": 59455364095115, "ctime": 1754215855, "mtime": 1754215857, "size": 6163}, "工作室项目/免责条款/实用免责声明文件/06-客户风险确认与责任分担书.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/实用免责声明文件/06-客户风险确认与责任分担书.md", "isdir": false, "fsid": 600864795966405, "ctime": 1754216466, "mtime": 1754216557, "size": 8803}, "工作室项目/免责条款/正式免责声明文件/06-通用免责声明模板.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/06-通用免责声明模板.md", "isdir": false, "fsid": 91178485466338, "ctime": 1754215904, "mtime": 1754215929, "size": 6615}, "工作室项目/免责条款/正式免责声明文件/07-客户风险确认书.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/正式免责声明文件/07-客户风险确认书.md", "isdir": false, "fsid": 1123141666807763, "ctime": 1754215952, "mtime": 1754216014, "size": 6747}, "英语/口语训练/口语练习/080209.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/口语练习/080209.md", "isdir": false, "fsid": 1008448472538939, "ctime": **********, "mtime": 1754166993, "size": 1515}, "英语/口语训练/口语练习/080301.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/口语练习/080301.md", "isdir": false, "fsid": 498680735784766, "ctime": **********, "mtime": 1754171881, "size": 3949}, "resource/图片素材/0c5b0198185be7c7874b56fdb90df141.jpg": {"path": "/apps/obsidian/德州扑克/resource/图片素材/0c5b0198185be7c7874b56fdb90df141.jpg", "isdir": false, "fsid": 487988996729953, "ctime": **********, "mtime": 1739241879, "size": 20506}, "工作室项目/免责条款/免责/1.你的“高分师兄师姐”是谁.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/1.你的“高分师兄师姐”是谁.md", "isdir": false, "fsid": 619188134689654, "ctime": 1754214533, "mtime": 1754216440, "size": 10476}, "工作室项目/免责条款/免责/2.预设应急预案和流程对于突发事件或工作压力大的情况.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/2.预设应急预案和流程对于突发事件或工作压力大的情况.md", "isdir": false, "fsid": 1082067347188087, "ctime": 1754214533, "mtime": 1754216440, "size": 8197}, "工作室项目/免责条款/免责/20250405124148_61.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/20250405124148_61.md", "isdir": false, "fsid": 203216758888313, "ctime": 1754214533, "mtime": 1754216440, "size": 383}, "工作室项目/免责条款/免责/20250406092707_59.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/20250406092707_59.md", "isdir": false, "fsid": 825012485433798, "ctime": 1754214533, "mtime": 1754216440, "size": 16950}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/20250415105107_14.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/20250415105107_14.md", "isdir": false, "fsid": 868876772103362, "ctime": 1754185147, "mtime": 1754185443, "size": 291}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/20250416100758_10.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/20250416100758_10.md", "isdir": false, "fsid": 822143574922887, "ctime": 1754185147, "mtime": 1754185446, "size": 361}, "工作室项目/免责条款/免责/20250416150342_43.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/20250416150342_43.md", "isdir": false, "fsid": 923731579549906, "ctime": 1754214533, "mtime": 1754216440, "size": 4559}, "resource/components/views/20250502132503.components": {"path": "/apps/obsidian/德州扑克/resource/components/views/20250502132503.components", "isdir": false, "fsid": 1081002976772041, "ctime": **********, "mtime": 1746163504, "size": 497}, "工作室项目/免责条款/免责/20250524161848_05.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/20250524161848_05.md", "isdir": false, "fsid": 905086831952203, "ctime": 1754214533, "mtime": 1754216440, "size": 272}, "口语训练/杂七杂八/动图/2Reeg02CpUnydJ5GFldgmSYpKJmpdbdg.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/2Reeg02CpUnydJ5GFldgmSYpKJmpdbdg.gif", "isdir": false, "fsid": 1004311303514645, "ctime": **********, "mtime": 1754092043, "size": 241571}, "resource/图片素材/3254002f58d97566b5ceb0e54f99aadd.jpg": {"path": "/apps/obsidian/德州扑克/resource/图片素材/3254002f58d97566b5ceb0e54f99aadd.jpg", "isdir": false, "fsid": 422648156433568, "ctime": **********, "mtime": 1739242293, "size": 8974}, "切分笔记/3bet.md": {"path": "/apps/obsidian/德州扑克/切分笔记/3bet.md", "isdir": false, "fsid": 380158532004882, "ctime": 1754176556, "mtime": 1754000221, "size": 1308}, "切分笔记/3bet范围-玩家在3bet时可能使用的牌的范围.md": {"path": "/apps/obsidian/德州扑克/切分笔记/3bet范围-玩家在3bet时可能使用的牌的范围.md", "isdir": false, "fsid": 88923744980050, "ctime": 1754176556, "mtime": 1754000616, "size": 1364}, "切分笔记/4bet诈唬-在对手3bet后，玩家进行再次加注以吓唬对手.md": {"path": "/apps/obsidian/德州扑克/切分笔记/4bet诈唬-在对手3bet后，玩家进行再次加注以吓唬对手.md", "isdir": false, "fsid": 832174785588669, "ctime": 1754176556, "mtime": 1754000610, "size": 1318}, "resource/图片素材/50f1b13b70b37f02d5a7578acdc3e606.jpg": {"path": "/apps/obsidian/德州扑克/resource/图片素材/50f1b13b70b37f02d5a7578acdc3e606.jpg", "isdir": false, "fsid": 1113606868165477, "ctime": **********, "mtime": 1739242397, "size": 39661}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/5分钟每日写作行动表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/5分钟每日写作行动表单.cform", "isdir": false, "fsid": 501437600273587, "ctime": 1754188097, "mtime": 1754188097, "size": 9568}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板/5分钟人味模板集合.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板/5分钟人味模板集合.md", "isdir": false, "fsid": 920204912501009, "ctime": 1754187339, "mtime": 1754187339, "size": 5168}, "⛄概念关系.components": {"path": "/apps/obsidian/德州扑克/⛄概念关系.components", "isdir": false, "fsid": 1099834770198502, "ctime": 1754175941, "mtime": 1754100312, "size": 30829}, "工作室项目/免责条款/免责/@.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/@.md", "isdir": false, "fsid": 670777673702635, "ctime": 1754214533, "mtime": 1754216440, "size": 3612}, "口语训练/杂七杂八/a4a9d544d9051fb0cecbf7e6b06a687.jpg": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/a4a9d544d9051fb0cecbf7e6b06a687.jpg", "isdir": false, "fsid": 1035099921094024, "ctime": **********, "mtime": 1753995477, "size": 484795}, "Tags/Script/addBulletPoints.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/addBulletPoints.js", "isdir": false, "fsid": 625323506965895, "ctime": **********, "mtime": 1753067290, "size": 2338}, "工作室项目/卢曼卡片/script和表单/aifront(1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/aifront(1).js", "isdir": false, "fsid": 675105691284351, "ctime": **********, "mtime": **********, "size": 5340}, "Tags/Script/aifront(1).js": {"path": "/apps/obsidian/德州扑克/Tags/Script/aifront(1).js", "isdir": false, "fsid": 445470792541454, "ctime": **********, "mtime": 1752707735, "size": 5335}, "resource/components/scripts/aifront.js": {"path": "/apps/obsidian/德州扑克/resource/components/scripts/aifront.js", "isdir": false, "fsid": 1065155156047113, "ctime": **********, "mtime": **********, "size": 5340}, "Tags/Script/aifront.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/aifront.js", "isdir": false, "fsid": 196289809427293, "ctime": **********, "mtime": **********, "size": 5340}, "resource/components/scripts/aiSummary (1).js": {"path": "/apps/obsidian/德州扑克/resource/components/scripts/aiSummary (1).js", "isdir": false, "fsid": 861151536585935, "ctime": **********, "mtime": 1749277202, "size": 2305}, "Tags/Script/aiSummary (1).js": {"path": "/apps/obsidian/德州扑克/Tags/Script/aiSummary (1).js", "isdir": false, "fsid": 69677423049699, "ctime": **********, "mtime": 1749277202, "size": 2305}, "工作室项目/卢曼卡片/script和表单/aiSummary (1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/aiSummary (1).js", "isdir": false, "fsid": 88970774148059, "ctime": **********, "mtime": 1752714078, "size": 2364}, "工作室项目/卢曼卡片/script和表单/aiSummary_deepseek.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/aiSummary_deepseek.js", "isdir": false, "fsid": 331701460427936, "ctime": **********, "mtime": 1752714078, "size": 2454}, "工作室项目/卢曼卡片/script和表单/AI标题生成.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI标题生成.js", "isdir": false, "fsid": 951889811735536, "ctime": **********, "mtime": **********, "size": 24103}, "工作室项目/卢曼卡片/script和表单/AI大纲生成.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI大纲生成.js", "isdir": false, "fsid": 853843464808892, "ctime": **********, "mtime": **********, "size": 14540}, "工作室项目/卢曼卡片/script和表单/AI翻译脚本.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI翻译脚本.js", "isdir": false, "fsid": 658189886720874, "ctime": **********, "mtime": **********, "size": 8392}, "工作室项目/卢曼卡片/script和表单/AI改写润色.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI改写润色.js", "isdir": false, "fsid": 853001177066238, "ctime": **********, "mtime": **********, "size": 20521}, "工作室项目/卢曼卡片/script和表单/AI关键词提取.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI关键词提取.js", "isdir": false, "fsid": 63363964826051, "ctime": **********, "mtime": **********, "size": 10797}, "工作室项目/卢曼卡片/script和表单/AI模板生成.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI模板生成.js", "isdir": false, "fsid": 1063315524991689, "ctime": **********, "mtime": **********, "size": 8635}, "工作室项目/卢曼卡片/script和表单/AI批量处理.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI批量处理.js", "isdir": false, "fsid": 707577875249207, "ctime": **********, "mtime": **********, "size": 7161}, "工作室项目/卢曼卡片/script和表单/AI情感分析.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI情感分析.js", "isdir": false, "fsid": 926741713777991, "ctime": **********, "mtime": **********, "size": 25065}, "工作室项目/卢曼卡片/script和表单/表单模板/AI日记助手V2.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/AI日记助手V2.cform", "isdir": false, "fsid": 1102637689461159, "ctime": **********, "mtime": 1752722842, "size": 10208}, "工作室项目/卢曼卡片/script和表单/AI问答生成.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI问答生成.js", "isdir": false, "fsid": 984162865723214, "ctime": **********, "mtime": **********, "size": 13245}, "工作室项目/卢曼卡片/script和表单/AI续写助手.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/AI续写助手.js", "isdir": false, "fsid": 126780264195793, "ctime": **********, "mtime": **********, "size": 18947}, "resource/图片素材/anders-jilden-cYrMQA7a3Wc-unsplash.jpg": {"path": "/apps/obsidian/德州扑克/resource/图片素材/anders-jilden-cYrMQA7a3Wc-unsplash.jpg", "isdir": false, "fsid": 419304589795308, "ctime": **********, "mtime": **********, "size": 808153}, "resource/components/views/Area 导航.md": {"path": "/apps/obsidian/德州扑克/resource/components/views/Area 导航.md", "isdir": false, "fsid": 693365587199211, "ctime": **********, "mtime": 1746320293, "size": 331}, "resource/components/views/area.components": {"path": "/apps/obsidian/德州扑克/resource/components/views/area.components", "isdir": false, "fsid": 791015979012319, "ctime": **********, "mtime": 1735297689, "size": 8194}, "resource/components/scripts/atomicNoteSplitter.js": {"path": "/apps/obsidian/德州扑克/resource/components/scripts/atomicNoteSplitter.js", "isdir": false, "fsid": 669123121298134, "ctime": **********, "mtime": 1753957417, "size": 8100}, "Tags/Script/atomicNoteSplitter.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/atomicNoteSplitter.js", "isdir": false, "fsid": 713177477605113, "ctime": **********, "mtime": 1753957417, "size": 8100}, "Tags/Script/batchAI.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/batchAI.js", "isdir": false, "fsid": 469674597561094, "ctime": **********, "mtime": 1754019616, "size": 10478}, "Tags/Script/batchCreateCorpus.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/batchCreateCorpus.js", "isdir": false, "fsid": 117177767221795, "ctime": **********, "mtime": 1754169292, "size": 12377}, "英语/口语训练/语料卡/because_someone_could_attack_it.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/because_someone_could_attack_it.md", "isdir": false, "fsid": 644718730943116, "ctime": **********, "mtime": 1754168858, "size": 1267}, "resource/图片素材/braden-collum-75XHJzEIeUc-unsplash.jpg": {"path": "/apps/obsidian/德州扑克/resource/图片素材/braden-collum-75XHJzEIeUc-unsplash.jpg", "isdir": false, "fsid": 203837004469761, "ctime": **********, "mtime": 1721576672, "size": 2713115}, "Tags/Script/calculatePracticeProgress.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/calculatePracticeProgress.js", "isdir": false, "fsid": 948127201963730, "ctime": **********, "mtime": 1754093002, "size": 5893}, "Tags/Script/calculateSleepQuality.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/calculateSleepQuality.js", "isdir": false, "fsid": 907386055261840, "ctime": **********, "mtime": 1753079685, "size": 3135}, "Tags/Script/calculateSleepQualityPro.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/calculateSleepQualityPro.js", "isdir": false, "fsid": 782026278892051, "ctime": **********, "mtime": 1753079601, "size": 3248}, "Tags/Script/calculatesSleepTime.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/calculatesSleepTime.js", "isdir": false, "fsid": 266973962819667, "ctime": **********, "mtime": 1753079572, "size": 2536}, "resource/图片素材/carolina-nichitin-5OY83OiKlNQ-unsplash.jpg": {"path": "/apps/obsidian/德州扑克/resource/图片素材/carolina-nichitin-5OY83OiKlNQ-unsplash.jpg", "isdir": false, "fsid": 281679768310965, "ctime": **********, "mtime": **********, "size": 3842929}, "英语/口语训练/具体练习/CEO应对监管重罚咨询.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习/CEO应对监管重罚咨询.md", "isdir": false, "fsid": ***************, "ctime": **********, "mtime": **********, "size": 1240}, "口语训练/杂七杂八/cf0e7fffa0040882cd8677b6d75a451.jpg": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/cf0e7fffa0040882cd8677b6d75a451.jpg", "isdir": false, "fsid": ***************, "ctime": **********, "mtime": **********, "size": 572049}, "resource/图片素材/clay-banks-QPivxTs7QVU-unsplash.jpg": {"path": "/apps/obsidian/德州扑克/resource/图片素材/clay-banks-QPivxTs7QVU-unsplash.jpg", "isdir": false, "fsid": **************, "ctime": **********, "mtime": **********, "size": 1032833}, "英语/口语训练/语料卡/compliance_framework.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/compliance_framework.md", "isdir": false, "fsid": ***************, "ctime": **********, "mtime": **********, "size": 847}, "英语/口语训练/语料卡/configure配置、设置.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/configure配置、设置.md", "isdir": false, "fsid": ***************, "ctime": **********, "mtime": **********, "size": 657}, "英语/口语训练/语料卡/could_lead_to_data_breaches.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/could_lead_to_data_breaches.md", "isdir": false, "fsid": ***************, "ctime": **********, "mtime": **********, "size": 1215}, "英语/口语训练/语料卡/could_lead_to_non-compliance_with_the_company's_da.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/could_lead_to_non-compliance_with_the_company's_da.md", "isdir": false, "fsid": 790851068363650, "ctime": **********, "mtime": 1754172019, "size": 1815}, "Tags/Script/createCorpusNote.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/createCorpusNote.js", "isdir": false, "fsid": 497950043013742, "ctime": **********, "mtime": 1754166938, "size": 15514}, "Tags/Script/createPracticeNote.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/createPracticeNote.js", "isdir": false, "fsid": 831750840349775, "ctime": **********, "mtime": 1754094170, "size": 5354}, "英语/口语训练/语料卡/critical_issue.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/critical_issue.md", "isdir": false, "fsid": 95415641635765, "ctime": **********, "mtime": 1754166694, "size": 751}, "Tags/Script/customAI.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/customAI.js", "isdir": false, "fsid": 407078831745737, "ctime": **********, "mtime": 1753960219, "size": 7896}, "口语训练/杂七杂八/daec787382755c4c8cbff5dd9a9fc12.jpg": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/daec787382755c4c8cbff5dd9a9fc12.jpg", "isdir": false, "fsid": 489493913337623, "ctime": **********, "mtime": 1753995439, "size": 587195}, "英语/口语训练/语料卡/data_breach.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/data_breach.md", "isdir": false, "fsid": 642169406172261, "ctime": **********, "mtime": 1754166650, "size": 772}, "英语/口语训练/语料卡/data_breaches.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/data_breaches.md", "isdir": false, "fsid": 667669812770893, "ctime": **********, "mtime": 1754166558, "size": 769}, "工作室项目/免责条款/extracted_disclaimers/disclaimer_extraction_20250803_175312.json": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/extracted_disclaimers/disclaimer_extraction_20250803_175312.json", "isdir": false, "fsid": 815932305410587, "ctime": 1754214792, "mtime": 1754214792, "size": 259266}, "工作室项目/免责条款/extracted_disclaimers/disclaimer_report_20250803_175312.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/extracted_disclaimers/disclaimer_report_20250803_175312.md", "isdir": false, "fsid": 391752902661367, "ctime": 1754214792, "mtime": 1754215451, "size": 228845}, "工作室项目/免责条款/extracted_disclaimers/disclaimer_summary_20250803_175441.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/extracted_disclaimers/disclaimer_summary_20250803_175441.md", "isdir": false, "fsid": 282672355322467, "ctime": 1754214881, "mtime": 1754215472, "size": 122958}, "工作室项目/免责条款/extracted_disclaimers/disclaimer_templates_20250803_175441.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/extracted_disclaimers/disclaimer_templates_20250803_175441.md", "isdir": false, "fsid": 634932816691101, "ctime": 1754214881, "mtime": 1754215496, "size": 9601}, "resource/attachments/DVJS-多条件复合检索V2 1.components": {"path": "/apps/obsidian/德州扑克/resource/attachments/DVJS-多条件复合检索V2 1.components", "isdir": false, "fsid": 964038477324309, "ctime": **********, "mtime": 1746163427, "size": 8710}, "resource/components/DVJS-多条件复合检索V2.components": {"path": "/apps/obsidian/德州扑克/resource/components/DVJS-多条件复合检索V2.components", "isdir": false, "fsid": 1057650163746989, "ctime": **********, "mtime": 1746163529, "size": 8710}, "resource/attachments/DVJS-多条件复合检索V2.components": {"path": "/apps/obsidian/德州扑克/resource/attachments/DVJS-多条件复合检索V2.components", "isdir": false, "fsid": 263277387784318, "ctime": **********, "mtime": 1746163424, "size": 8710}, "英语/口语训练/语料卡/enhance_our_network_security_measures.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/enhance_our_network_security_measures.md", "isdir": false, "fsid": 289085105953626, "ctime": **********, "mtime": 1754172026, "size": 1604}, "Tags/Script/extractCorpus.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/extractCorpus.js", "isdir": false, "fsid": 297479663592634, "ctime": **********, "mtime": 1754166345, "size": 13726}, "工作室项目/卢曼卡片/script和表单/extractHeadingContent(1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/extractHeadingContent(1).js", "isdir": false, "fsid": 943570601468496, "ctime": **********, "mtime": **********, "size": 3965}, "工作室项目/卢曼卡片/script和表单/extractHeadingContent.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/extractHeadingContent.js", "isdir": false, "fsid": 69462350439999, "ctime": **********, "mtime": **********, "size": 3965}, "Tags/Script/extractHeadingContent.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/extractHeadingContent.js", "isdir": false, "fsid": 679680115945457, "ctime": **********, "mtime": 1753067289, "size": 2854}, "extract_disclaimer_clauses.py": {"path": "/apps/obsidian/德州扑克/extract_disclaimer_clauses.py", "isdir": false, "fsid": 261202714863506, "ctime": 1754214768, "mtime": 1754214768, "size": 12233}, "工作室项目/卢曼卡片/script和表单/fetch-today-stats(1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/fetch-today-stats(1).js", "isdir": false, "fsid": 1005644335273176, "ctime": **********, "mtime": **********, "size": 11865}, "工作室项目/卢曼卡片/script和表单/fetch-today-stats.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/fetch-today-stats.js", "isdir": false, "fsid": 834636618876144, "ctime": **********, "mtime": **********, "size": 11865}, "工作室项目/卢曼卡片/script和表单/表单模板/GDPR合规清单生成器.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/GDPR合规清单生成器.cform", "isdir": false, "fsid": 177376981936825, "ctime": **********, "mtime": 1753922185, "size": 5222}, "Tags/Script/generateSpecificScenario.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/generateSpecificScenario.js", "isdir": false, "fsid": 398884308919199, "ctime": **********, "mtime": 1754084424, "size": 18305}, "generate_disclaimer_summary.py": {"path": "/apps/obsidian/德州扑克/generate_disclaimer_summary.py", "isdir": false, "fsid": 982172059907425, "ctime": 1754214857, "mtime": 1754214857, "size": 10323}, "工作室项目/卢曼卡片/script和表单/getSubfolder(1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/getSubfolder(1).js", "isdir": false, "fsid": 96469878740239, "ctime": **********, "mtime": **********, "size": 2581}, "工作室项目/卢曼卡片/script和表单/getSubfolder.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/getSubfolder.js", "isdir": false, "fsid": 922716178355863, "ctime": **********, "mtime": **********, "size": 2581}, "口语训练/杂七杂八/动图/GIF-动漫_唯美花瓣飘落_爱给网_aigei_com.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/GIF-动漫_唯美花瓣飘落_爱给网_aigei_com.gif", "isdir": false, "fsid": 490938895141930, "ctime": **********, "mtime": 1754088676, "size": 982799}, "口语训练/杂七杂八/动图/GIF_动漫_流星_爱给网_aigei_com.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/GIF_动漫_流星_爱给网_aigei_com.gif", "isdir": false, "fsid": 731472239264001, "ctime": **********, "mtime": 1754088672, "size": 3792337}, "英语/口语训练/语料卡/hackers_might_target_it.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/hackers_might_target_it.md", "isdir": false, "fsid": 250065918451906, "ctime": **********, "mtime": 1754168964, "size": 1418}, "Home 1.components": {"path": "/apps/obsidian/德州扑克/Home 1.components", "isdir": false, "fsid": 77355763424175, "ctime": 1754176734, "mtime": 1753952504, "size": 54796}, "resource/components/views/Home-B.components": {"path": "/apps/obsidian/德州扑克/resource/components/views/Home-B.components", "isdir": false, "fsid": 213638773567791, "ctime": **********, "mtime": 1739240524, "size": 3214}, "Home.components": {"path": "/apps/obsidian/德州扑克/Home.components", "isdir": false, "fsid": 291604986749885, "ctime": 1754176734, "mtime": 1753952498, "size": 42973}, "Tags/Script/insertTaskToProject.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/insertTaskToProject.js", "isdir": false, "fsid": 165463824063020, "ctime": **********, "mtime": 1753067290, "size": 1569}, "Tags/Script/insertTaskToProject2.0.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/insertTaskToProject2.0.js", "isdir": false, "fsid": 457717805668782, "ctime": **********, "mtime": 1753067290, "size": 2603}, "Tags/Script/insertWeek.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/insertWeek.js", "isdir": false, "fsid": 263981740210209, "ctime": **********, "mtime": 1753067290, "size": 3867}, "英语/口语训练/语料卡/in_this_system_that_we_need_to_investigate.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/in_this_system_that_we_need_to_investigate.md", "isdir": false, "fsid": 437659747606322, "ctime": **********, "mtime": **********, "size": 1053}, "英语/口语训练/语料卡/I’ve_noticed.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/I’ve_noticed.md", "isdir": false, "fsid": 117962915852211, "ctime": **********, "mtime": **********, "size": 1019}, "Tags/Script/keywordFlowchartGenerator.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/keywordFlowchartGenerator.js", "isdir": false, "fsid": 1050788431748888, "ctime": **********, "mtime": **********, "size": 7746}, "resource/图片素材/LICENSE": {"path": "/apps/obsidian/德州扑克/resource/图片素材/LICENSE", "isdir": false, "fsid": 7287422455275, "ctime": **********, "mtime": **********, "size": 11357}, "工作室项目/卢曼卡片/script和表单/migrateFile(1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/migrateFile(1).js", "isdir": false, "fsid": 1038931810388656, "ctime": **********, "mtime": **********, "size": 5221}, "工作室项目/卢曼卡片/script和表单/migrateFile.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/migrateFile.js", "isdir": false, "fsid": 994100436563585, "ctime": **********, "mtime": **********, "size": 5221}, "德州笔记同步/训练笔记/Obsidian看视频插件.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/训练笔记/Obsidian看视频插件.md", "isdir": false, "fsid": 375994086290528, "ctime": **********, "mtime": **********, "size": 13359}, "口语训练/杂七杂八/动图/OIP.jpg": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/OIP.jpg", "isdir": false, "fsid": 1081043765633843, "ctime": **********, "mtime": 1754091818, "size": 6857}, "口语训练/杂七杂八/动图/OIP.webp": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/OIP.webp", "isdir": false, "fsid": 567536722802241, "ctime": **********, "mtime": 1754091815, "size": 4094}, "工作室项目/免责条款/免责/ollama+anythingLLM部署服务.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/ollama+anythingLLM部署服务.md", "isdir": false, "fsid": 23801520202414, "ctime": 1754214533, "mtime": 1754216440, "size": 1799}, "Tags/Script/originalIntentSimple.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/originalIntentSimple.js", "isdir": false, "fsid": 73912009143512, "ctime": **********, "mtime": 1754170002, "size": 8743}, "resource/attachments/Pasted image 20240513163145.png": {"path": "/apps/obsidian/德州扑克/resource/attachments/Pasted image 20240513163145.png", "isdir": false, "fsid": 973014095823116, "ctime": **********, "mtime": 1715593437, "size": 1564267}, "resource/attachments/Pasted image 20240513172142.png": {"path": "/apps/obsidian/德州扑克/resource/attachments/Pasted image 20240513172142.png", "isdir": false, "fsid": 185796719115438, "ctime": **********, "mtime": 1715593437, "size": 1586547}, "resource/components/area/pkm.components": {"path": "/apps/obsidian/德州扑克/resource/components/area/pkm.components", "isdir": false, "fsid": 1109774835671555, "ctime": **********, "mtime": 1746260023, "size": 3652}, "英语/口语训练/语料卡/pose_a_major_challenge.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/pose_a_major_challenge.md", "isdir": false, "fsid": 41137831573254, "ctime": **********, "mtime": 1754166580, "size": 801}, "英语/口语训练/语料卡/potential_threats.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/potential_threats.md", "isdir": false, "fsid": 937272832718850, "ctime": **********, "mtime": 1754168702, "size": 775}, "Tags/Script/practiceEvaluation.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/practiceEvaluation.js", "isdir": false, "fsid": 623499661459305, "ctime": **********, "mtime": 1754095339, "size": 9184}, "Tags/Script/quickAI2.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/quickAI2.js", "isdir": false, "fsid": 269506367491857, "ctime": **********, "mtime": 1754042241, "size": 13050}, "Tags/Script/quickAI_MultiAPI.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/quickAI_MultiAPI.js", "isdir": false, "fsid": 898849380109125, "ctime": **********, "mtime": 1754074879, "size": 13025}, "口语训练/杂七杂八/英语练习插件/README.md": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/README.md", "isdir": false, "fsid": 946955025299471, "ctime": **********, "mtime": 1754088372, "size": 2777}, "英语/口语训练/语料卡/reassess_the_risks.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/reassess_the_risks.md", "isdir": false, "fsid": 607258470543337, "ctime": **********, "mtime": 1754168702, "size": 874}, "英语/口语训练/语料卡/reconsider_this_issue.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/reconsider_this_issue.md", "isdir": false, "fsid": 984774210999406, "ctime": **********, "mtime": 1754168702, "size": 856}, "工作室项目/卢曼卡片/script和表单/remove-today-stats(1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/remove-today-stats(1).js", "isdir": false, "fsid": 538468584317219, "ctime": **********, "mtime": **********, "size": 1078}, "工作室项目/卢曼卡片/script和表单/remove-today-stats.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/remove-today-stats.js", "isdir": false, "fsid": 525760031351889, "ctime": **********, "mtime": **********, "size": 1078}, "英语/口语训练/语料卡/rethink_our_approach.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/rethink_our_approach.md", "isdir": false, "fsid": 1117293965351870, "ctime": **********, "mtime": 1754168996, "size": 1655}, "英语/口语训练/语料卡/Since it’s vulnerable_to_attacks.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/Since it’s vulnerable_to_attacks.md", "isdir": false, "fsid": 603729647239875, "ctime": **********, "mtime": 1754168796, "size": 781}, "Tags/Script/sleepCalculator-1.0.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/sleepCalculator-1.0.js", "isdir": false, "fsid": 778153518601374, "ctime": **********, "mtime": 1753067290, "size": 2297}, "Tags/Script/smartHeadingExtractor.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/smartHeadingExtractor.js", "isdir": false, "fsid": 533660414511974, "ctime": **********, "mtime": 1753067290, "size": 3435}, "英语/口语训练/语料卡/some_security_risks.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/some_security_risks.md", "isdir": false, "fsid": 114608274548720, "ctime": **********, "mtime": 1754168910, "size": 1078}, "英语/口语训练/语料卡/Some_technical_vulnerabilities.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/Some_technical_vulnerabilities.md", "isdir": false, "fsid": 194212598567886, "ctime": **********, "mtime": 1754172007, "size": 1287}, "工作室项目/卢曼卡片/script和表单/switch-view-mode(1).js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/switch-view-mode(1).js", "isdir": false, "fsid": 942601672816666, "ctime": **********, "mtime": **********, "size": 1206}, "工作室项目/卢曼卡片/script和表单/switch-view-mode.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/switch-view-mode.js", "isdir": false, "fsid": 877858328077177, "ctime": **********, "mtime": **********, "size": 1206}, "Tags/Script/switchLightDark.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/switchLightDark.js", "isdir": false, "fsid": 228837500228166, "ctime": **********, "mtime": 1731453377, "size": 504}, "resource/components/scripts/switchLightDark.js": {"path": "/apps/obsidian/德州扑克/resource/components/scripts/switchLightDark.js", "isdir": false, "fsid": 1003872248505778, "ctime": **********, "mtime": 1731453377, "size": 504}, "德州笔记同步/学习笔记/SyncToy_6729af83-560a-418e-affc-28276e26f7c8.dat": {"path": "/apps/obsidian/德州扑克/德州笔记同步/学习笔记/SyncToy_6729af83-560a-418e-affc-28276e26f7c8.dat", "isdir": false, "fsid": 237627832001647, "ctime": 1754178629, "mtime": 1754178629, "size": 16}, "切分笔记/T♠9♠.md": {"path": "/apps/obsidian/德州扑克/切分笔记/T♠9♠.md", "isdir": false, "fsid": 1109862300562207, "ctime": 1754176556, "mtime": 1754000225, "size": 1268}, "英语/口语训练/语料卡/There_are_some_vulnerabilities.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/There_are_some_vulnerabilities.md", "isdir": false, "fsid": 570740394413868, "ctime": **********, "mtime": 1754172069, "size": 1505}, "resource/images/thoughts.png": {"path": "/apps/obsidian/德州扑克/resource/images/thoughts.png", "isdir": false, "fsid": 160231654094700, "ctime": **********, "mtime": 1715923596, "size": 29635}, "resource/images/typewriter.png": {"path": "/apps/obsidian/德州扑克/resource/images/typewriter.png", "isdir": false, "fsid": 1115057986387668, "ctime": **********, "mtime": 1715923596, "size": 33266}, "Tags/Script/updatePracticeDate.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/updatePracticeDate.js", "isdir": false, "fsid": 856471337179512, "ctime": **********, "mtime": 1754047767, "size": 1754}, "英语/口语训练/语料卡/update_it.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/语料卡/update_it.md", "isdir": false, "fsid": 991869084069370, "ctime": **********, "mtime": 1754168702, "size": 757}, "Wiki Home.components": {"path": "/apps/obsidian/德州扑克/Wiki Home.components", "isdir": false, "fsid": 258933105230411, "ctime": 1754176734, "mtime": 1753952507, "size": 28424}, "Tags/Script/xiaohongshu.js": {"path": "/apps/obsidian/德州扑克/Tags/Script/xiaohongshu.js", "isdir": false, "fsid": 728521521487865, "ctime": **********, "mtime": 1753934179, "size": 5406}, "口语训练/杂七杂八/动图/XIuu0VpSVOqiJVMODmV8xNCnIYRADpK3.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/XIuu0VpSVOqiJVMODmV8xNCnIYRADpK3.gif", "isdir": false, "fsid": 572361906185824, "ctime": **********, "mtime": 1754091815, "size": 564651}, "工作室项目/免责条款/免责/XX隐私合规服务协议第四条服务模式甲方理解并同意，乙方服务不包含：-政府公关协调-绝对合规结果保证第七条责任限制因以下情形导致的损失，乙方不承担责任：√甲方未及时提供完整材料√法律法规突然修订第十二条自动续约本合同期满前90日，甲方未书面终止的，自动按原条件续期1年（本条已加粗标红）附件：服务价目表-基础咨询费：XX元-加急服务费：基础费×300%-政府沟通协助：另议.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/XX隐私合规服务协议第四条服务模式甲方理解并同意，乙方服务不包含：-政府公关协调-绝对合规结果保证第七条责任限制因以下情形导致的损失，乙方不承担责任：√甲方未及时提供完整材料√法律法规突然修订第十二条自动续约本合同期满前90日，甲方未书面终止的，自动按原条件续期1年（本条已加粗标红）附件：服务价目表-基础咨询费：XX元-加急服务费：基础费×300%-政府沟通协助：另议.md", "isdir": false, "fsid": 744527544414573, "ctime": 1754214533, "mtime": 1754216440, "size": 3709}, "工作室项目/免责条款/免责/，实际上揭示了一个深层的社会认知规律：审美能力本质上是系统思维的可视化表达.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/，实际上揭示了一个深层的社会认知规律：审美能力本质上是系统思维的可视化表达.md", "isdir": false, "fsid": 1043624565035483, "ctime": 1754214533, "mtime": 1754216440, "size": 14174}, "工作室项目/免责条款/extracted_disclaimers/“跨境电商爬虫公司合规需求”的深度解析与风险规避指南.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/extracted_disclaimers/“跨境电商爬虫公司合规需求”的深度解析与风险规避指南.md", "isdir": false, "fsid": 691703403156088, "ctime": 1754215503, "mtime": 1754215521, "size": 4206}, "工作室项目/免责条款/免责/《弹性规则案例库》.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/《弹性规则案例库》.md", "isdir": false, "fsid": 580608670453359, "ctime": 1754214533, "mtime": 1754216440, "size": 5583}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/《弹性规则案例库》.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/《弹性规则案例库》.md", "isdir": false, "fsid": 927909679910217, "ctime": 1754185147, "mtime": 1754187012, "size": 5583}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/「抛弃体面」的本质—.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/「抛弃体面」的本质—.md", "isdir": false, "fsid": 928835652942419, "ctime": 1754185147, "mtime": 1754187010, "size": 5009}, "口语训练/杂七杂八/英语练习插件/按钮测试.html": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/按钮测试.html", "isdir": false, "fsid": 290062493034355, "ctime": **********, "mtime": 1753339158, "size": 11239}, "切分笔记/按钮位（BTN）最后一个行动的位置，通常被认为是位置最好的位置.md": {"path": "/apps/obsidian/德州扑克/切分笔记/按钮位（BTN）最后一个行动的位置，通常被认为是位置最好的位置.md", "isdir": false, "fsid": 489395947549914, "ctime": 1754176556, "mtime": 1754000622, "size": 1316}, "口语训练/杂七杂八/英语练习插件/按钮修复说明.md": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/按钮修复说明.md", "isdir": false, "fsid": 883047224267102, "ctime": **********, "mtime": 1754088372, "size": 4805}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/案例故事创作表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/案例故事创作表单.cform", "isdir": false, "fsid": 445408760074975, "ctime": 1754187868, "mtime": 1754187868, "size": 11819}, "工作室项目/免责条款/免责/摆摊期合规指南：不注册公司也能开干.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/摆摊期合规指南：不注册公司也能开干.md", "isdir": false, "fsid": 91384859746953, "ctime": 1754214533, "mtime": 1754216440, "size": 3665}, "口语训练/杂七杂八/英语练习插件/笔记数据提取器.py": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/笔记数据提取器.py", "isdir": false, "fsid": 1003403717644489, "ctime": **********, "mtime": 1753337774, "size": 15363}, "切分笔记/避免“万一”心态.md": {"path": "/apps/obsidian/德州扑克/切分笔记/避免“万一”心态.md", "isdir": false, "fsid": 405277596902590, "ctime": 1754176556, "mtime": 1753952876, "size": 719}, "切分笔记/避免依赖‘万一’心态.md": {"path": "/apps/obsidian/德州扑克/切分笔记/避免依赖‘万一’心态.md", "isdir": false, "fsid": 33156894383677, "ctime": 1754177011, "mtime": 1754177011, "size": 762}, "工作室项目/免责条款/免责/不同版本的隐私合规方案需要根据目标读者和使用场景调整侧重点.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/不同版本的隐私合规方案需要根据目标读者和使用场景调整侧重点.md", "isdir": false, "fsid": 310585544939209, "ctime": 1754214533, "mtime": 1754216440, "size": 4845}, "口语训练/杂七杂八/英语练习插件/插件改进说明.md": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/插件改进说明.md", "isdir": false, "fsid": 210671319135271, "ctime": **********, "mtime": 1754088372, "size": 5983}, "切分笔记/长期视角下的策略选择.md": {"path": "/apps/obsidian/德州扑克/切分笔记/长期视角下的策略选择.md", "isdir": false, "fsid": 632468394328020, "ctime": 1754176556, "mtime": 1753952875, "size": 719}, "切分笔记/出错点分析.md": {"path": "/apps/obsidian/德州扑克/切分笔记/出错点分析.md", "isdir": false, "fsid": 65886336623459, "ctime": 1754177107, "mtime": 1754177107, "size": 980}, "工作室项目/免责条款/免责/初始理解.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/初始理解.md", "isdir": false, "fsid": 433303523313916, "ctime": 1754214533, "mtime": 1754216440, "size": 16525}, "工作室项目/免责条款/免责/从两位农民的成功经验中.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/从两位农民的成功经验中.md", "isdir": false, "fsid": 903506338402606, "ctime": 1754214533, "mtime": 1754216440, "size": 23199}, "切分笔记/搭配使用.md": {"path": "/apps/obsidian/德州扑克/切分笔记/搭配使用.md", "isdir": false, "fsid": 767788988859422, "ctime": 1754177107, "mtime": 1754177107, "size": 617}, "切分笔记/大盲（BB）和小盲（SB）的跟注成本低.md": {"path": "/apps/obsidian/德州扑克/切分笔记/大盲（BB）和小盲（SB）的跟注成本低.md", "isdir": false, "fsid": 674592353473906, "ctime": 1754176556, "mtime": 1754001030, "size": 1145}, "切分笔记/大盲（BB）可以玩更宽的牌.md": {"path": "/apps/obsidian/德州扑克/切分笔记/大盲（BB）可以玩更宽的牌.md", "isdir": false, "fsid": 518494450939549, "ctime": 1754176556, "mtime": 1754001035, "size": 1149}, "切分笔记/大盲位跟注成本低.md": {"path": "/apps/obsidian/德州扑克/切分笔记/大盲位跟注成本低.md", "isdir": false, "fsid": 479194308166792, "ctime": 1754177294, "mtime": 1754177294, "size": 811}, "resource/images/呆萌熊猫.png": {"path": "/apps/obsidian/德州扑克/resource/images/呆萌熊猫.png", "isdir": false, "fsid": 230906420705574, "ctime": **********, "mtime": 1715593437, "size": 71353}, "resource/components/views/导航栏.md": {"path": "/apps/obsidian/德州扑克/resource/components/views/导航栏.md", "isdir": false, "fsid": 784876092472684, "ctime": **********, "mtime": 1746319755, "size": 298}, "切分笔记/德州扑克的教训-位置差时应该谨慎操作.md": {"path": "/apps/obsidian/德州扑克/切分笔记/德州扑克的教训-位置差时应该谨慎操作.md", "isdir": false, "fsid": 999935605660889, "ctime": 1754176556, "mtime": 1753958685, "size": 676}, "切分笔记/德州扑克的心态问题.md": {"path": "/apps/obsidian/德州扑克/切分笔记/德州扑克的心态问题.md", "isdir": false, "fsid": 157779500144846, "ctime": 1754176556, "mtime": 1753957955, "size": 685}, "切分笔记/德州扑克心态不应依赖‘万一’.md": {"path": "/apps/obsidian/德州扑克/切分笔记/德州扑克心态不应依赖‘万一’.md", "isdir": false, "fsid": 809288368075219, "ctime": 1754176933, "mtime": 1754176933, "size": 897}, "切分笔记/德州扑克应基于概率和长期稳赢策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/德州扑克应基于概率和长期稳赢策略.md", "isdir": false, "fsid": 262647461013563, "ctime": 1754176933, "mtime": 1754176933, "size": 840}, "resource/脚本工具/德州扑克知识卡片生成器.py": {"path": "/apps/obsidian/德州扑克/resource/脚本工具/德州扑克知识卡片生成器.py", "isdir": false, "fsid": 1102634169164991, "ctime": **********, "mtime": 1753176650, "size": 15009}, "切分笔记/德州扑克中筹码量判断_Fold_Equity_和_All-in_风险.md": {"path": "/apps/obsidian/德州扑克/切分笔记/德州扑克中筹码量判断_Fold_Equity_和_All-in_风险.md", "isdir": false, "fsid": 251304821223664, "ctime": 1754177460, "mtime": 1754177514, "size": 1515}, "切分笔记/等待好牌的策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/等待好牌的策略.md", "isdir": false, "fsid": 268710052493941, "ctime": 1754176556, "mtime": 1753952877, "size": 665}, "切分笔记/动态调整策略与调整自己的范围对应：对紧弱玩家：多偷盲，少诈唬。对松凶玩家：多埋伏（Trap），少跟注。.md": {"path": "/apps/obsidian/德州扑克/切分笔记/动态调整策略与调整自己的范围对应：对紧弱玩家：多偷盲，少诈唬。对松凶玩家：多埋伏（Trap），少跟注。.md", "isdir": false, "fsid": 718433403215742, "ctime": 1754177460, "mtime": 1754218898, "size": 2740}, "工作室项目/卢曼卡片/script和表单/表单模板/读书笔记模板.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/读书笔记模板.cform", "isdir": false, "fsid": 514097782186049, "ctime": **********, "mtime": 1753922082, "size": 4838}, "切分笔记/对紧弱玩家的策略：多偷盲，少诈唬.md": {"path": "/apps/obsidian/德州扑克/切分笔记/对紧弱玩家的策略：多偷盲，少诈唬.md", "isdir": false, "fsid": 304807751733347, "ctime": 1754218345, "mtime": 1754218910, "size": 2830}, "切分笔记/对紧弱玩家的偷盲策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/对紧弱玩家的偷盲策略.md", "isdir": false, "fsid": 228696373430022, "ctime": 1754218954, "mtime": 1754218954, "size": 905}, "切分笔记/对紧弱玩家的诈唬策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/对紧弱玩家的诈唬策略.md", "isdir": false, "fsid": 48230074258385, "ctime": 1754218954, "mtime": 1754218954, "size": 1108}, "切分笔记/对手加注时的应对策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/对手加注时的应对策略.md", "isdir": false, "fsid": 1008208407738793, "ctime": 1754177110, "mtime": 1754177110, "size": 1331}, "切分笔记/对松凶玩家的策略：多埋伏，少跟注.md": {"path": "/apps/obsidian/德州扑克/切分笔记/对松凶玩家的策略：多埋伏，少跟注.md", "isdir": false, "fsid": 263695240483261, "ctime": 1754218345, "mtime": 1754218915, "size": 3296}, "切分笔记/多埋伏的原因.md": {"path": "/apps/obsidian/德州扑克/切分笔记/多埋伏的原因.md", "isdir": false, "fsid": 807177598117649, "ctime": 1754218950, "mtime": 1754218950, "size": 893}, "口语训练/杂七杂八/动图/二次元 美少女 初音未来_爱给网_aigei_com 1.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/二次元 美少女 初音未来_爱给网_aigei_com 1.gif", "isdir": false, "fsid": 631833844821268, "ctime": **********, "mtime": 1754091815, "size": 3444034}, "口语训练/杂七杂八/动图/二次元 美少女 初音未来_爱给网_aigei_com.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/二次元 美少女 初音未来_爱给网_aigei_com.gif", "isdir": false, "fsid": 1036909760876597, "ctime": **********, "mtime": 1754088382, "size": 3444034}, "工作室项目/卢曼卡片/script和表单/表单模板/法规条款提取器.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/法规条款提取器.cform", "isdir": false, "fsid": 869932142301353, "ctime": **********, "mtime": 1753920924, "size": 5185}, "工作室项目/卢曼卡片/script和表单/表单模板/法律案例管理.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/法律案例管理.cform", "isdir": false, "fsid": 615070602557803, "ctime": **********, "mtime": 1752739014, "size": 4320}, "工作室项目/免责条款/免责/法律设置这些规则的底层逻辑.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/法律设置这些规则的底层逻辑.md", "isdir": false, "fsid": 942090606383471, "ctime": 1754214533, "mtime": 1754216440, "size": 4244}, "切分笔记/翻牌后的牌力评估.md": {"path": "/apps/obsidian/德州扑克/切分笔记/翻牌后的牌力评估.md", "isdir": false, "fsid": 679258919636403, "ctime": 1754176556, "mtime": 1753952870, "size": 731}, "切分笔记/翻牌后的形势分析-翻牌后，玩家应分析形势，意识到对手的牌可能比自己更强，如对手持有KQ，已经是两对，而自己仅有顶对.md": {"path": "/apps/obsidian/德州扑克/切分笔记/翻牌后的形势分析-翻牌后，玩家应分析形势，意识到对手的牌可能比自己更强，如对手持有KQ，已经是两对，而自己仅有顶对.md", "isdir": false, "fsid": 19309844165807, "ctime": 1754176556, "mtime": 1753957948, "size": 720}, "切分笔记/翻译与具体意义.md": {"path": "/apps/obsidian/德州扑克/切分笔记/翻译与具体意义.md", "isdir": false, "fsid": 213356505614162, "ctime": 1754177107, "mtime": 1754177107, "size": 809}, "resource/attachments/附件库.components": {"path": "/apps/obsidian/德州扑克/resource/attachments/附件库.components", "isdir": false, "fsid": 414250654716385, "ctime": **********, "mtime": 1746163416, "size": 4808}, "resource/components/附件库.components": {"path": "/apps/obsidian/德州扑克/resource/components/附件库.components", "isdir": false, "fsid": 730429916457462, "ctime": **********, "mtime": 1746163567, "size": 4808}, "概率/概率论.md": {"path": "/apps/obsidian/德州扑克/概率/概率论.md", "isdir": false, "fsid": 600008442149955, "ctime": 1754176563, "mtime": 1746399921, "size": 106}, "概率/概率训练.md": {"path": "/apps/obsidian/德州扑克/概率/概率训练.md", "isdir": false, "fsid": 757091589299109, "ctime": 1754176563, "mtime": 1734691270, "size": 38223}, "笔记/inbox笔记/高对：TT（10 10）、JJ、QQ、KK、AA强高张：AK、AQ、AJs（同花AJ）.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/高对：TT（10 10）、JJ、QQ、KK、AA强高张：AK、AQ、AJs（同花AJ）.md", "isdir": false, "fsid": 423580663662492, "ctime": 1754176814, "mtime": 1754148081, "size": 219}, "工作室项目/卢曼卡片/script和表单/人味表单/高级人味模板提取脚本.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/高级人味模板提取脚本.js", "isdir": false, "fsid": 791395181490210, "ctime": 1754186442, "mtime": 1754187019, "size": 8681}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/个人观察研究表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/个人观察研究表单.cform", "isdir": false, "fsid": 993162971257415, "ctime": 1754188283, "mtime": 1754188283, "size": 12524}, "工作室项目/卢曼卡片/script和表单/卢曼表单/个人信息处理合规表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/个人信息处理合规表单.cform", "isdir": false, "fsid": 389640895491153, "ctime": 1754182579, "mtime": 1754183074, "size": 8194}, "切分笔记/跟注策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/跟注策略.md", "isdir": false, "fsid": 966035149048237, "ctime": 1754220577, "mtime": 1754220592, "size": 662}, "切分笔记/跟注策略的误区.md": {"path": "/apps/obsidian/德州扑克/切分笔记/跟注策略的误区.md", "isdir": false, "fsid": 140876718299126, "ctime": 1754176556, "mtime": 1753952870, "size": 745}, "切分笔记/跟注成本低的优势.md": {"path": "/apps/obsidian/德州扑克/切分笔记/跟注成本低的优势.md", "isdir": false, "fsid": 924128322109370, "ctime": 1754177294, "mtime": 1754177294, "size": 685}, "切分笔记/跟注的时机与策略-位置劣势时，使用边缘牌（如K9）跟注职业玩家的加注，通常不是明智的选择，因为职业玩家能更灵活地操作.md": {"path": "/apps/obsidian/德州扑克/切分笔记/跟注的时机与策略-位置劣势时，使用边缘牌（如K9）跟注职业玩家的加注，通常不是明智的选择，因为职业玩家能更灵活地操作.md", "isdir": false, "fsid": 145094373968532, "ctime": 1754176556, "mtime": 1753958493, "size": 784}, "resource/components/area/工作.components": {"path": "/apps/obsidian/德州扑克/resource/components/area/工作.components", "isdir": false, "fsid": 566148176046641, "ctime": **********, "mtime": 1746312350, "size": 20197}, "工作室项目/免责条款/免责/工作流程—.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/工作流程—.md", "isdir": false, "fsid": 443959398998489, "ctime": 1754214533, "mtime": 1754216440, "size": 26282}, "resource/components/views/工作统计.components": {"path": "/apps/obsidian/德州扑克/resource/components/views/工作统计.components", "isdir": false, "fsid": 478988673362034, "ctime": **********, "mtime": 1746260183, "size": 6889}, "工作室项目/卢曼卡片/script和表单/表单模板/工作周报生成器.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/工作周报生成器.cform", "isdir": false, "fsid": 757967564951940, "ctime": **********, "mtime": 1753920894, "size": 3829}, "工作室项目/卢曼卡片/script和表单/表单模板/供应商合规审核.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/供应商合规审核.cform", "isdir": false, "fsid": 963027543127781, "ctime": **********, "mtime": 1752723439, "size": 5844}, "resource/脚本工具/股权财务法律知识库生成器.py": {"path": "/apps/obsidian/德州扑克/resource/脚本工具/股权财务法律知识库生成器.py", "isdir": false, "fsid": 963245273440548, "ctime": **********, "mtime": 1752800813, "size": 13246}, "切分笔记/关煞位（CO）较好的位置优势.md": {"path": "/apps/obsidian/德州扑克/切分笔记/关煞位（CO）较好的位置优势.md", "isdir": false, "fsid": 19188862124721, "ctime": 1754176556, "mtime": 1754000619, "size": 1316}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/关于“20%任性权”和“读者吐槽法”的深度解析.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/关于“20%任性权”和“读者吐槽法”的深度解析.md", "isdir": false, "fsid": 924188435287816, "ctime": 1754185147, "mtime": 1754215387, "size": 3606}, "工作室项目/免责条款/免责/好的.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/好的.md", "isdir": false, "fsid": 123697261399512, "ctime": 1754214533, "mtime": 1754216440, "size": 4675}, "工作室项目/卢曼卡片/script和表单/表单模板/合规报告生成器.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/合规报告生成器.cform", "isdir": false, "fsid": 208695743868947, "ctime": **********, "mtime": 1752723269, "size": 4655}, "工作室项目/卢曼卡片/script和表单/卢曼表单/合规冲突分析表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/合规冲突分析表单.cform", "isdir": false, "fsid": 845498194924456, "ctime": 1754182460, "mtime": 1754182927, "size": 5577}, "工作室项目/卢曼卡片/script和表单/卢曼表单/合规冲突分析表单V2.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/合规冲突分析表单V2.cform", "isdir": false, "fsid": 589400416562116, "ctime": 1754183630, "mtime": 1754183630, "size": 6156}, "工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/合规冲突分析模板.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/合规冲突分析模板.md", "isdir": false, "fsid": 243166972849872, "ctime": 1754184275, "mtime": 1754186120, "size": 2282}, "工作室项目/卢曼卡片/script和表单/表单模板/合规培训题库.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/合规培训题库.cform", "isdir": false, "fsid": 770054007196999, "ctime": **********, "mtime": 1752723365, "size": 5315}, "工作室项目/卢曼卡片/script和表单/卢曼表单/合规认知更新表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/合规认知更新表单.cform", "isdir": false, "fsid": 411943606184706, "ctime": 1754182474, "mtime": 1754182954, "size": 4508}, "工作室/肌肉/生成笔记/每日合规思考/合规思考-数据跨境传输合规-20250803.md": {"path": "/apps/obsidian/德州扑克/工作室/肌肉/生成笔记/每日合规思考/合规思考-数据跨境传输合规-20250803.md", "isdir": false, "fsid": 390656763197833, "ctime": 1754185242, "mtime": 1754185243, "size": 4279}, "工作室项目/卢曼卡片/script和表单/卢曼表单/合规知识MVP表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/合规知识MVP表单.cform", "isdir": false, "fsid": 164895573711541, "ctime": 1754182613, "mtime": 1754183160, "size": 6047}, "英语/口语训练/具体练习/合同续签合规对接.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习/合同续签合规对接.md", "isdir": false, "fsid": 1114949952339893, "ctime": **********, "mtime": 1754100188, "size": 1178}, "工作室项目/免责条款/免责/黑客进化vs..md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/黑客进化vs..md", "isdir": false, "fsid": 749807605050712, "ctime": 1754214533, "mtime": 1754216440, "size": 4640}, "工作室项目/卢曼卡片/script和表单/表单模板/会议记录模板.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/会议记录模板.cform", "isdir": false, "fsid": 64054012262800, "ctime": **********, "mtime": 1752722714, "size": 2342}, "工作室项目/免责条款/免责/婚姻中的表演艺术—.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/婚姻中的表演艺术—.md", "isdir": false, "fsid": 821769500812489, "ctime": 1754214533, "mtime": 1754216440, "size": 2943}, "切分笔记/基于概率和长期稳赢策略游戏.md": {"path": "/apps/obsidian/德州扑克/切分笔记/基于概率和长期稳赢策略游戏.md", "isdir": false, "fsid": 168257829037346, "ctime": 1754177011, "mtime": 1754177011, "size": 759}, "resource/components/views/计时组件.components": {"path": "/apps/obsidian/德州扑克/resource/components/views/计时组件.components", "isdir": false, "fsid": 300022367562409, "ctime": **********, "mtime": 1739240511, "size": 1614}, "切分笔记/计算技能冷却时间与对手行动频率对应.md": {"path": "/apps/obsidian/德州扑克/切分笔记/计算技能冷却时间与对手行动频率对应.md", "isdir": false, "fsid": 188674713991588, "ctime": 1754177460, "mtime": 1754177460, "size": 1482}, "切分笔记/加注开池.md": {"path": "/apps/obsidian/德州扑克/切分笔记/加注开池.md", "isdir": false, "fsid": 1046047802500637, "ctime": 1754176556, "mtime": 1754000226, "size": 1317}, "切分笔记/加注时机选择.md": {"path": "/apps/obsidian/德州扑克/切分笔记/加注时机选择.md", "isdir": false, "fsid": 409507024597113, "ctime": 1754220578, "mtime": 1754220590, "size": 606}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/监管动态分析表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/监管动态分析表单.cform", "isdir": false, "fsid": 697049972682367, "ctime": 1754187746, "mtime": 1754187746, "size": 11899}, "口语训练/练习场景/监管机构沟通.md": {"path": "/apps/obsidian/德州扑克/口语训练/练习场景/监管机构沟通.md", "isdir": false, "fsid": 936345707111515, "ctime": **********, "mtime": 1754050639, "size": 4989}, "工作室项目/卢曼卡片/script和表单/表单模板/监管问询响应.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/监管问询响应.cform", "isdir": false, "fsid": 206990709831673, "ctime": **********, "mtime": 1752723400, "size": 5154}, "笔记/inbox笔记/简单记：- “中间位置加注 ≈ 我有A，但不是王炸”    - “枪口位加注 ≈ 我有王炸，别惹我”    - “庄位加注 ≈ 我可能偷鸡，小心点”.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/简单记：- “中间位置加注 ≈ 我有A，但不是王炸”    - “枪口位加注 ≈ 我有王炸，别惹我”    - “庄位加注 ≈ 我可能偷鸡，小心点”.md", "isdir": false, "fsid": 510473340934171, "ctime": 1754176814, "mtime": 1754148422, "size": 368}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/简化版每日素材记录表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/简化版每日素材记录表单.cform", "isdir": false, "fsid": 100271352207211, "ctime": 1754186254, "mtime": 1754186254, "size": 9705}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/简化版人味故事创作表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/简化版人味故事创作表单.cform", "isdir": false, "fsid": 563916350300027, "ctime": 1754186303, "mtime": 1754186303, "size": 9471}, "工作室项目/免责条款/免责/将客户项目转化为可携带资产示例defsanitize_assets(original_report)returnoriginal_report.replace(client_ip=192.168.[REDACTED],db_schema=generate_synthetic_schema(),risk_findings=generalize_to_iso_standard()).md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/将客户项目转化为可携带资产示例defsanitize_assets(original_report)returnoriginal_report.replace(client_ip=192.168.[REDACTED],db_schema=generate_synthetic_schema(),risk_findings=generalize_to_iso_standard()).md", "isdir": false, "fsid": 497089407583954, "ctime": 1754214533, "mtime": 1754216440, "size": 5122}, "工作室项目/免责条款/免责/将前述方法论应用到隐私合规工作室的创业中.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/将前述方法论应用到隐私合规工作室的创业中.md", "isdir": false, "fsid": 929785350009541, "ctime": 1754214533, "mtime": 1754216440, "size": 4919}, "德州笔记同步/教学法/教学法—1️⃣天赋与训练他的系统具备自指性（观察行为会改变系统状态）动态密码系统下的「武术降维打击」.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—1️⃣天赋与训练他的系统具备自指性（观察行为会改变系统状态）动态密码系统下的「武术降维打击」.md", "isdir": false, "fsid": 455394422078269, "ctime": 1754176695, "mtime": 1750977541, "size": 170145}, "德州笔记同步/教学法/教学法——扑克决策分层——听音乐.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法——扑克决策分层——听音乐.md", "isdir": false, "fsid": 780767713576399, "ctime": 1754176695, "mtime": 1749291497, "size": 16769}, "德州笔记同步/教学法/教学法—“3-bet”是一个特定的术语.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—“3-bet”是一个特定的术语.md", "isdir": false, "fsid": 1096588467326219, "ctime": 1754176695, "mtime": 1754179728, "size": 11434}, "德州笔记同步/教学法/教学法—「美剧学英语方法论」.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—「美剧学英语方法论」.md", "isdir": false, "fsid": 547879076968332, "ctime": 1754176695, "mtime": 1754179717, "size": 22807}, "德州笔记同步/教学法/教学法—不反弹—阶梯减肥法的科学拆解.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—不反弹—阶梯减肥法的科学拆解.md", "isdir": false, "fsid": 335206040691011, "ctime": 1754176695, "mtime": 1749417829, "size": 76373}, "德州笔记同步/教学法/教学法—从“规则树”到“动态模型”的进化职业牌手的秘密：他们大脑中运行的不是if-then规则，而是一个实时计算的决策流处理器.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—从“规则树”到“动态模型”的进化职业牌手的秘密：他们大脑中运行的不是if-then规则，而是一个实时计算的决策流处理器.md", "isdir": false, "fsid": 148974870114145, "ctime": 1754176695, "mtime": 1754179733, "size": 84373}, "德州笔记同步/教学法/教学法—德州扑克里的“趴草丛”练什么？.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—德州扑克里的“趴草丛”练什么？.md", "isdir": false, "fsid": 254495001359488, "ctime": 1754176695, "mtime": 1754179706, "size": 3044}, "德州笔记同步/教学法/教学法—德州扑克训练计划.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—德州扑克训练计划.md", "isdir": false, "fsid": 1071051016390607, "ctime": 1754176695, "mtime": 1754179718, "size": 29064}, "德州笔记同步/教学法/教学法—关键原则：从“孤立训练”到“整合应用.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—关键原则：从“孤立训练”到“整合应用.md", "isdir": false, "fsid": 456927859141099, "ctime": 1754176695, "mtime": 1754179736, "size": 29612}, "德州笔记同步/教学法/教学法—规则理解、策略应用、心理战术、实战技巧、术语掌握 这五个维度.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—规则理解、策略应用、心理战术、实战技巧、术语掌握 这五个维度.md", "isdir": false, "fsid": 943396269852062, "ctime": 1754176695, "mtime": 1754179709, "size": 14700}, "德州笔记同步/教学法/教学法—结合Albert提出的核心问题和解决方法建立英语专用回路构建GTO决策的「数学反射通道」.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—结合Albert提出的核心问题和解决方法建立英语专用回路构建GTO决策的「数学反射通道」.md", "isdir": false, "fsid": 619675549339766, "ctime": 1754176695, "mtime": 1754179730, "size": 38316}, "德州笔记同步/教学法/教学法—结合心理学和正念饮食（Mindful Eating）的非传统减肥方法.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—结合心理学和正念饮食（Mindful Eating）的非传统减肥方法.md", "isdir": false, "fsid": 466139832476567, "ctime": 1754176695, "mtime": 1754179720, "size": 119726}, "德州笔记同步/教学法/教学法—卡通卡片就是关于火影.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—卡通卡片就是关于火影.md", "isdir": false, "fsid": 994193692761378, "ctime": 1754176695, "mtime": 1754179716, "size": 73631}, "德州笔记同步/教学法/教学法—你的“扑克瘦子习惯”——如何用同样的逻辑在牌桌上建立不败系统.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—你的“扑克瘦子习惯”——如何用同样的逻辑在牌桌上建立不败系统.md", "isdir": false, "fsid": 70076119532442, "ctime": 1754176695, "mtime": 1754179718, "size": 20708}, "德州笔记同步/教学法/教学法—梭哈（All-in）是指玩家将手中全部的筹码一次性押入底池的行为.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—梭哈（All-in）是指玩家将手中全部的筹码一次性押入底池的行为.md", "isdir": false, "fsid": 1096549409292941, "ctime": 1754176695, "mtime": 1754179719, "size": 182572}, "德州笔记同步/教学法/教学法—为什么维持正常体重很难？.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—为什么维持正常体重很难？.md", "isdir": false, "fsid": 816420684670044, "ctime": 1754176695, "mtime": 1750975444, "size": 50658}, "德州笔记同步/教学法/教学法—用「腰围指标」保护你的扑克训练成果？——理性恋爱防沉迷系统.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—用「腰围指标」保护你的扑克训练成果？——理性恋爱防沉迷系统.md", "isdir": false, "fsid": 502889926698206, "ctime": 1754176695, "mtime": 1749291471, "size": 65024}, "德州笔记同步/教学法/教学法—有效诈唬≈有效化妆：比如干燥面对Nit下注≈用阴影修饰圆脸——精准有效.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—有效诈唬≈有效化妆：比如干燥面对Nit下注≈用阴影修饰圆脸——精准有效.md", "isdir": false, "fsid": 1007923598822847, "ctime": 1754176695, "mtime": 1754179721, "size": 15931}, "德州笔记同步/教学法/教学法—中医或传统养生角度提到的内部循环打通，确实能解释瑜伽或太极练习带来的身体变化.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—中医或传统养生角度提到的内部循环打通，确实能解释瑜伽或太极练习带来的身体变化.md", "isdir": false, "fsid": 706758041628476, "ctime": 1754176695, "mtime": 1754179727, "size": 20689}, "德州笔记同步/教学法/教学法—逐句对照的方式学习德州扑克思维，可以借鉴美剧.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—逐句对照的方式学习德州扑克思维，可以借鉴美剧.md", "isdir": false, "fsid": 254964785527526, "ctime": 1754176695, "mtime": 1749355237, "size": 31713}, "德州笔记同步/教学法/教学法—主食减肥法.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—主食减肥法.md", "isdir": false, "fsid": 295084841948689, "ctime": 1754176695, "mtime": 1749291485, "size": 41739}, "德州笔记同步/教学法/教学法—最小阻力路径”实现减肥——也就是找到一种最省力、最不消耗意志力、且能融入现有生活的方式.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—最小阻力路径”实现减肥——也就是找到一种最省力、最不消耗意志力、且能融入现有生活的方式.md", "isdir": false, "fsid": 910543064799798, "ctime": 1754176695, "mtime": 1754179721, "size": 122082}, "德州笔记同步/教学法/教学法—佐助的战斗计算能力 → 德州扑克的“精准决策”佐助在战斗中计算“伊邪那岐的冷却时间”和“团藏的剩余写轮眼数量”，本质上是一种动态资源管理与概率博弈。.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/教学法/教学法—佐助的战斗计算能力 → 德州扑克的“精准决策”佐助在战斗中计算“伊邪那岐的冷却时间”和“团藏的剩余写轮眼数量”，本质上是一种动态资源管理与概率博弈。.md", "isdir": false, "fsid": 574174863052117, "ctime": 1754176695, "mtime": 1754179709, "size": 4753}, "切分笔记/紧弱玩家的策略总结.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家的策略总结.md", "isdir": false, "fsid": 957585029172626, "ctime": 1754218954, "mtime": 1754220573, "size": 861}, "切分笔记/紧弱玩家的大牌识别.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家的大牌识别.md", "isdir": false, "fsid": 109666965847315, "ctime": 1754220579, "mtime": 1754220587, "size": 1046}, "切分笔记/紧弱玩家的特点.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家的特点.md", "isdir": false, "fsid": 926092680348038, "ctime": 1754218954, "mtime": 1754218954, "size": 818}, "切分笔记/紧弱玩家的诈唬应对.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家的诈唬应对.md", "isdir": false, "fsid": 290292197116203, "ctime": 1754220580, "mtime": 1754220586, "size": 1044}, "切分笔记/紧弱玩家反抗时的应对策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家反抗时的应对策略.md", "isdir": false, "fsid": 347293940517604, "ctime": 1754220573, "mtime": 1754220595, "size": 664}, "切分笔记/紧弱玩家跟注或加注时的牌力推测.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家跟注或加注时的牌力推测.md", "isdir": false, "fsid": 1120910717132976, "ctime": 1754220576, "mtime": 1754220577, "size": 732}, "切分笔记/紧弱玩家抢盲注策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家抢盲注策略.md", "isdir": false, "fsid": 848283802152290, "ctime": 1754220573, "mtime": 1754220595, "size": 664}, "切分笔记/紧弱玩家偷盲加注范围.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家偷盲加注范围.md", "isdir": false, "fsid": 584128299408779, "ctime": 1754220579, "mtime": 1754220589, "size": 909}, "切分笔记/紧弱玩家在盲注位的行为特点.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧弱玩家在盲注位的行为特点.md", "isdir": false, "fsid": 678589659960786, "ctime": 1754220576, "mtime": 1754220594, "size": 718}, "切分笔记/紧凶（TAG）.md": {"path": "/apps/obsidian/德州扑克/切分笔记/紧凶（TAG）.md", "isdir": false, "fsid": 562917671823821, "ctime": 1754176556, "mtime": 1754000222, "size": 1272}, "工作室项目/卢曼卡片/script和表单/表单模板/竞品分析记录表.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/竞品分析记录表.cform", "isdir": false, "fsid": 818863156918956, "ctime": **********, "mtime": 1753920868, "size": 4093}, "口语训练/杂七杂八/动图/卡通小猫厨房做饭gif表情包 (7)_爱给网_aigei_com 1.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/卡通小猫厨房做饭gif表情包 (7)_爱给网_aigei_com 1.gif", "isdir": false, "fsid": 865217937560394, "ctime": **********, "mtime": 1754091815, "size": 1045610}, "口语训练/杂七杂八/动图/卡通小猫厨房做饭gif表情包 (7)_爱给网_aigei_com.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/卡通小猫厨房做饭gif表情包 (7)_爱给网_aigei_com.gif", "isdir": false, "fsid": 655063079247071, "ctime": **********, "mtime": 1754088672, "size": 1045610}, "口语训练/练习场景/客户数据合规咨询.md": {"path": "/apps/obsidian/德州扑克/口语训练/练习场景/客户数据合规咨询.md", "isdir": false, "fsid": 187249132504715, "ctime": **********, "mtime": 1754050935, "size": 3719}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/客户痛点挖掘表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/客户痛点挖掘表单.cform", "isdir": false, "fsid": 669831415818648, "ctime": 1754187687, "mtime": 1754187687, "size": 11759}, "工作室项目/卢曼卡片/script和表单/表单模板/客户咨询记录.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/客户咨询记录.cform", "isdir": false, "fsid": 627106663420916, "ctime": **********, "mtime": 1752723328, "size": 5528}, "口语训练/杂七杂八/口语练习notion.md": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/口语练习notion.md", "isdir": false, "fsid": 845487111666863, "ctime": **********, "mtime": 1754045450, "size": 4935}, "工作室/肌肉/生成笔记/跨境数据传输/跨境传输-未填写场景-03-0930.md": {"path": "/apps/obsidian/德州扑克/工作室/肌肉/生成笔记/跨境数据传输/跨境传输-未填写场景-03-0930.md", "isdir": false, "fsid": 446234471456845, "ctime": 1754184635, "mtime": 1754184636, "size": 5089}, "工作室/肌肉/生成笔记/跨境数据传输/跨境数据传输-undefined-2025-08-03-0922.md": {"path": "/apps/obsidian/德州扑克/工作室/肌肉/生成笔记/跨境数据传输/跨境数据传输-undefined-2025-08-03-0922.md", "isdir": false, "fsid": 31400330567541, "ctime": 1754184164, "mtime": 1754184164, "size": 4834}, "工作室项目/卢曼卡片/script和表单/卢曼表单/跨境数据传输表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/跨境数据传输表单.cform", "isdir": false, "fsid": 801899541773443, "ctime": 1754182554, "mtime": 1754183042, "size": 6697}, "口语训练/练习场景/跨境数据传输谈判.md": {"path": "/apps/obsidian/德州扑克/口语训练/练习场景/跨境数据传输谈判.md", "isdir": false, "fsid": 216441563026784, "ctime": **********, "mtime": 1754050723, "size": 5820}, "切分笔记/烂牌的定义和影响.md": {"path": "/apps/obsidian/德州扑克/切分笔记/烂牌的定义和影响.md", "isdir": false, "fsid": 439613247698323, "ctime": 1754176556, "mtime": 1753952873, "size": 695}, "切分笔记/烂牌的定义及风险-是翻牌后不易成牌或成牌概率很低的牌，如小杂牌、边缘同花连张等.md": {"path": "/apps/obsidian/德州扑克/切分笔记/烂牌的定义及风险-是翻牌后不易成牌或成牌概率很低的牌，如小杂牌、边缘同花连张等.md", "isdir": false, "fsid": 84197720751136, "ctime": 1754176556, "mtime": 1753958422, "size": 885}, "英语/口语训练/具体练习/离职员工数据合规处理.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习/离职员工数据合规处理.md", "isdir": false, "fsid": 503106901882119, "ctime": **********, "mtime": 1754094448, "size": 1226}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/利用「监管滞后性」（政策刚出.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/利用「监管滞后性」（政策刚出.md", "isdir": false, "fsid": 73217960798843, "ctime": 1754185147, "mtime": 1754215387, "size": 9913}, "工作室项目/免责条款/免责/利用「监管滞后性」（政策刚出.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/利用「监管滞后性」（政策刚出.md", "isdir": false, "fsid": 1046892988278055, "ctime": 1754214533, "mtime": 1754216440, "size": 9913}, "切分笔记/利用心理盲区与对手思维漏洞对应.md": {"path": "/apps/obsidian/德州扑克/切分笔记/利用心理盲区与对手思维漏洞对应.md", "isdir": false, "fsid": 877117727806487, "ctime": 1754177460, "mtime": 1754177460, "size": 1419}, "工作室项目/卢曼卡片/script和表单/表单模板/联系人管理.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/联系人管理.cform", "isdir": false, "fsid": 641567820466194, "ctime": **********, "mtime": 1752722809, "size": 3438}, "口语训练/杂七杂八/英语练习插件/练习配置.json": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/练习配置.json", "isdir": false, "fsid": 597749199042374, "ctime": **********, "mtime": 1753337853, "size": 2593}, "口语训练/杂七杂八/英语练习插件/练习数据.json": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/练习数据.json", "isdir": false, "fsid": 985658222996201, "ctime": **********, "mtime": 1753337853, "size": 387939}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/林雨的故事看似随意.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/林雨的故事看似随意.md", "isdir": false, "fsid": 1094635575438298, "ctime": 1754185147, "mtime": 1754215387, "size": 11501}, "工作室项目/卢曼卡片/卢曼": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/卢曼", "isdir": false, "fsid": 524089019212999, "ctime": 1754181739, "mtime": 1754181750, "size": 36893}, "工作室项目/卢曼卡片/卢曼卡片模板提取.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/卢曼卡片模板提取.md", "isdir": false, "fsid": 934313086601104, "ctime": 1754188909, "mtime": 1754215387, "size": 8111}, "工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/卢曼卡片模板提取.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/卢曼卡片模板提取.md", "isdir": false, "fsid": 999771602394285, "ctime": 1754182126, "mtime": 1754182128, "size": 5450}, "英语/口语训练/录音/录音，2025-08-02 22.03.04.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-02 22.03.04.m4a", "isdir": false, "fsid": 865351741503471, "ctime": **********, "mtime": 1754143387, "size": 105851}, "英语/口语训练/录音/录音，2025-08-02 22.03.35.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-02 22.03.35.m4a", "isdir": false, "fsid": 4680550909814, "ctime": **********, "mtime": 1754143419, "size": 109860}, "英语/口语训练/录音/录音，2025-08-02 22.06.08.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-02 22.06.08.m4a", "isdir": false, "fsid": 103712851254036, "ctime": **********, "mtime": 1754143571, "size": 103055}, "英语/口语训练/录音/录音，2025-08-02 22.07.35.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-02 22.07.35.m4a", "isdir": false, "fsid": 205424299094946, "ctime": **********, "mtime": 1754143668, "size": 225533}, "英语/口语训练/录音/录音，2025-08-02 22.16.27.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-02 22.16.27.m4a", "isdir": false, "fsid": 891844705451923, "ctime": **********, "mtime": 1754144193, "size": 141430}, "英语/口语训练/录音/录音，2025-08-02 22.24.13.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-02 22.24.13.m4a", "isdir": false, "fsid": 382572844471802, "ctime": **********, "mtime": 1754144661, "size": 155290}, "英语/口语训练/录音/录音，2025-08-02 22.44.49.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-02 22.44.49.m4a", "isdir": false, "fsid": 524762165245772, "ctime": **********, "mtime": 1754145897, "size": 158966}, "英语/口语训练/录音/录音，2025-08-03 04.46.25.m4a": {"path": "/apps/obsidian/德州扑克/英语/口语训练/录音/录音，2025-08-03 04.46.25.m4a", "isdir": false, "fsid": 696841573312203, "ctime": **********, "mtime": 1754167598, "size": 231447}, "resource/components/area/旅游.components": {"path": "/apps/obsidian/德州扑克/resource/components/area/旅游.components", "isdir": false, "fsid": 995747826877754, "ctime": **********, "mtime": 1746260039, "size": 4471}, "切分笔记/埋伏的执行要点.md": {"path": "/apps/obsidian/德州扑克/切分笔记/埋伏的执行要点.md", "isdir": false, "fsid": 336136774236447, "ctime": 1754218950, "mtime": 1754220599, "size": 950}, "切分笔记/埋伏后的心理战调整.md": {"path": "/apps/obsidian/德州扑克/切分笔记/埋伏后的心理战调整.md", "isdir": false, "fsid": 1098098341720709, "ctime": 1754220577, "mtime": 1754220592, "size": 777}, "切分笔记/埋伏位置选择.md": {"path": "/apps/obsidian/德州扑克/切分笔记/埋伏位置选择.md", "isdir": false, "fsid": 165506030439250, "ctime": 1754220577, "mtime": 1754220593, "size": 581}, "切分笔记/盲注位的“门票”理论.md": {"path": "/apps/obsidian/德州扑克/切分笔记/盲注位的“门票”理论.md", "isdir": false, "fsid": 899020753805207, "ctime": 1754176556, "mtime": 1754001038, "size": 1145}, "切分笔记/盲注位的“强制投资”.md": {"path": "/apps/obsidian/德州扑克/切分笔记/盲注位的“强制投资”.md", "isdir": false, "fsid": 215092963636031, "ctime": 1754176556, "mtime": 1754001030, "size": 1133}, "工作室/肌肉/生成笔记/每日合规思考/每日合规思考-undefined-undefined.md": {"path": "/apps/obsidian/德州扑克/工作室/肌肉/生成笔记/每日合规思考/每日合规思考-undefined-undefined.md", "isdir": false, "fsid": 378870658012850, "ctime": 1754183732, "mtime": 1754183732, "size": 4208}, "工作室项目/卢曼卡片/script和表单/卢曼表单/每日合规思考表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/每日合规思考表单.cform", "isdir": false, "fsid": 477240968844141, "ctime": 1754182492, "mtime": 1754183543, "size": 6438}, "工作室项目/卢曼卡片/script和表单/卢曼表单/每日合规思考表单V2.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/每日合规思考表单V2.cform", "isdir": false, "fsid": 225271417499120, "ctime": 1754183602, "mtime": 1754183602, "size": 6221}, "工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/每日合规思考模板.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/每日合规思考模板.md", "isdir": false, "fsid": 639388456546645, "ctime": 1754184255, "mtime": 1754186121, "size": 2547}, "口语训练/杂七杂八/动图/梦幻卡通背景动图 (29)_爱给网_aigei_com 1.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/梦幻卡通背景动图 (29)_爱给网_aigei_com 1.gif", "isdir": false, "fsid": 58380567793868, "ctime": **********, "mtime": 1754091815, "size": 23483593}, "口语训练/杂七杂八/动图/梦幻卡通背景动图 (29)_爱给网_aigei_com.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/梦幻卡通背景动图 (29)_爱给网_aigei_com.gif", "isdir": false, "fsid": 591483612890664, "ctime": **********, "mtime": 1754088672, "size": 23483593}, "工作室项目/免责条款/免责/免责律师.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/免责律师.md", "isdir": false, "fsid": 798859364861844, "ctime": 1754214620, "mtime": 1754216440, "size": 4437}, "免责条款使用指南.md": {"path": "/apps/obsidian/德州扑克/免责条款使用指南.md", "isdir": false, "fsid": 101064008328298, "ctime": 1754214930, "mtime": 1754214930, "size": 4232}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/明白.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/明白.md", "isdir": false, "fsid": 1090850668251391, "ctime": 1754185147, "mtime": 1754215387, "size": 5170}, "resource/脚本工具/默写本原子笔记生成器.py": {"path": "/apps/obsidian/德州扑克/resource/脚本工具/默写本原子笔记生成器.py", "isdir": false, "fsid": 384977763834882, "ctime": **********, "mtime": 1752801281, "size": 12507}, "resource/脚本工具/默写本知识卡片生成器_改进版.py": {"path": "/apps/obsidian/德州扑克/resource/脚本工具/默写本知识卡片生成器_改进版.py", "isdir": false, "fsid": 1058989963391522, "ctime": **********, "mtime": 1752799113, "size": 14027}, "工作室项目/免责条款/免责/男人的核心思维模式（与女性差异对比）.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/男人的核心思维模式（与女性差异对比）.md", "isdir": false, "fsid": 703410570463117, "ctime": 1754214533, "mtime": 1754216440, "size": 6792}, "工作室项目/免责条款/免责/你不是“写条款的”.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你不是“写条款的”.md", "isdir": false, "fsid": 251245927518837, "ctime": 1754214533, "mtime": 1754216440, "size": 7278}, "工作室项目/免责条款/免责/你的第一个MVP（最小可行产品）设计方案.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你的第一个MVP（最小可行产品）设计方案.md", "isdir": false, "fsid": 788493719283744, "ctime": 1754214533, "mtime": 1754216440, "size": 10059}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你的观察非常准确—.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你的观察非常准确—.md", "isdir": false, "fsid": 1091256833405388, "ctime": 1754185147, "mtime": 1754215387, "size": 11214}, "工作室项目/免责条款/免责/你的核心问题拆解：.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你的核心问题拆解：.md", "isdir": false, "fsid": 58525221883016, "ctime": 1754214533, "mtime": 1754216440, "size": 20168}, "工作室项目/免责条款/免责/你对问题的定义方式.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你对问题的定义方式.md", "isdir": false, "fsid": 1114310312698098, "ctime": 1754214533, "mtime": 1754216440, "size": 5868}, "工作室项目/免责条款/免责/你计划开设的隐私合规工作室.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你计划开设的隐私合规工作室.md", "isdir": false, "fsid": 203691000221579, "ctime": 1754214533, "mtime": 1754216440, "size": 5347}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你能做到「从青铜到大师」的跃迁.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你能做到「从青铜到大师」的跃迁.md", "isdir": false, "fsid": 994135012609752, "ctime": 1754185147, "mtime": 1754215387, "size": 8974}, "工作室项目/免责条款/免责/你能做到「从青铜到大师」的跃迁.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你能做到「从青铜到大师」的跃迁.md", "isdir": false, "fsid": 806596850295355, "ctime": 1754214533, "mtime": 1754216440, "size": 8974}, "工作室项目/免责条款/免责/你提到的「合同陷阱检测」服务.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你提到的「合同陷阱检测」服务.md", "isdir": false, "fsid": 1024909465847760, "ctime": 1754214533, "mtime": 1754216440, "size": 8329}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的本质问题是：“如果我要设计一个隐私合规咨询产品服务.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的本质问题是：“如果我要设计一个隐私合规咨询产品服务.md", "isdir": false, "fsid": 644116496400215, "ctime": 1754185147, "mtime": 1754215387, "size": 5628}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的核心问题是：“每天记录的内容到底从哪里来.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的核心问题是：“每天记录的内容到底从哪里来.md", "isdir": false, "fsid": 617781748909344, "ctime": 1754185147, "mtime": 1754215387, "size": 5371}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的几个场景（寺庙规则、练武偏执、日常漏洞）和「监管滞后性」看似不相关.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的几个场景（寺庙规则、练武偏执、日常漏洞）和「监管滞后性」看似不相关.md", "isdir": false, "fsid": 31111929675375, "ctime": 1754185147, "mtime": 1754215387, "size": 5171}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的这个案例之所以打动人.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你提到的这个案例之所以打动人.md", "isdir": false, "fsid": 373038628399365, "ctime": 1754185147, "mtime": 1754186171, "size": 4571}, "工作室项目/免责条款/免责/你提到的这个部分是「方法论植入」—.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你提到的这个部分是「方法论植入」—.md", "isdir": false, "fsid": 362089594976404, "ctime": 1754214533, "mtime": 1754216440, "size": 20444}, "工作室项目/免责条款/免责/你提到的这个原则—.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你提到的这个原则—.md", "isdir": false, "fsid": 432659918339580, "ctime": 1754214533, "mtime": 1754216440, "size": 5054}, "工作室项目/免责条款/免责/你提到的正是使用AI辅助信息获取时的核心痛点：AI回答速度快.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你提到的正是使用AI辅助信息获取时的核心痛点：AI回答速度快.md", "isdir": false, "fsid": 287653889539382, "ctime": 1754214533, "mtime": 1754216440, "size": 4332}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你问到了最本质的问题.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你问到了最本质的问题.md", "isdir": false, "fsid": 396265636071438, "ctime": 1754185147, "mtime": 1754215387, "size": 4031}, "工作室项目/免责条款/免责/你问到了最本质的问题—.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你问到了最本质的问题—.md", "isdir": false, "fsid": 627710673820477, "ctime": 1754214533, "mtime": 1754216440, "size": 13928}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你问题的本质是：“用AI写作的人那么多.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你问题的本质是：“用AI写作的人那么多.md", "isdir": false, "fsid": 747792163016940, "ctime": 1754185147, "mtime": 1754215387, "size": 11177}, "工作室项目/免责条款/免责/你问题的本质是：“用AI写作的人那么多.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/你问题的本质是：“用AI写作的人那么多.md", "isdir": false, "fsid": 472332264466940, "ctime": 1754214533, "mtime": 1754216440, "size": 11177}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你在隐私合规领域的写作需求（行业观察、人物化表达、避免AI雷同）和之前讨论的“缩小词汇+替换高频词”策略本质上是一致的.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你在隐私合规领域的写作需求（行业观察、人物化表达、避免AI雷同）和之前讨论的“缩小词汇+替换高频词”策略本质上是一致的.md", "isdir": false, "fsid": 1072539794451531, "ctime": 1754185147, "mtime": 1754215387, "size": 4636}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你这个问题非常本质.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/你这个问题非常本质.md", "isdir": false, "fsid": 838871856185697, "ctime": 1754185147, "mtime": 1754215387, "size": 4710}, "笔记/inbox笔记/你坐在 “按钮位”（最后一个行动，位置最好），用 T♠9♠（黑桃10和黑桃9）加注开池。  这时，一个 “紧凶”（玩得紧但很凶）的对手在 “关煞位”（CO位）对你 3bet（再加注）.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/你坐在 “按钮位”（最后一个行动，位置最好），用 T♠9♠（黑桃10和黑桃9）加注开池。  这时，一个 “紧凶”（玩得紧但很凶）的对手在 “关煞位”（CO位）对你 3bet（再加注）.md", "isdir": false, "fsid": 1049497447876875, "ctime": 1754176814, "mtime": 1754000627, "size": 10949}, "工作室项目/免责条款/免责/您提到的这两个问题.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/您提到的这两个问题.md", "isdir": false, "fsid": 236752372547672, "ctime": 1754214533, "mtime": 1754216440, "size": 10433}, "工作室项目/免责条款/免责/您完全抓住了精髓.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/您完全抓住了精髓.md", "isdir": false, "fsid": 471617211830312, "ctime": 1754214533, "mtime": 1754216440, "size": 9583}, "工作室项目/免责条款/免责/您选择这种“轻咨询+重分包”的模式(1).md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/您选择这种“轻咨询+重分包”的模式(1).md", "isdir": false, "fsid": 505934082982331, "ctime": 1754214533, "mtime": 1754216440, "size": 9926}, "工作室项目/免责条款/免责/您选择这种“轻咨询+重分包”的模式.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/您选择这种“轻咨询+重分包”的模式.md", "isdir": false, "fsid": 371431505595239, "ctime": 1754214533, "mtime": 1754216440, "size": 4056}, "笔记/inbox笔记/偶尔玩一些中等牌：99、88、JTs（同花J10）、QJs（同花QJ）.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/偶尔玩一些中等牌：99、88、JTs（同花J10）、QJs（同花QJ）.md", "isdir": false, "fsid": 736664363090717, "ctime": 1754176814, "mtime": 1754148133, "size": 459}, "切分笔记/牌局分析：对手加注时可能的牌型.md": {"path": "/apps/obsidian/德州扑克/切分笔记/牌局分析：对手加注时可能的牌型.md", "isdir": false, "fsid": 72155045656381, "ctime": 1754177110, "mtime": 1754177110, "size": 1432}, "切分笔记/赔率变好，跟注成本低.md": {"path": "/apps/obsidian/德州扑克/切分笔记/赔率变好，跟注成本低.md", "isdir": false, "fsid": 1079727618637463, "ctime": 1754176556, "mtime": 1754001032, "size": 1249}, "工作室项目/卢曼卡片/script和表单/人味表单/批量提取人味模板脚本.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/批量提取人味模板脚本.js", "isdir": false, "fsid": 1075779006807932, "ctime": 1754186403, "mtime": 1754186403, "size": 3279}, "切分笔记/平跟-对手加注后选择跟注而不是加注或弃牌.md": {"path": "/apps/obsidian/德州扑克/切分笔记/平跟-对手加注后选择跟注而不是加注或弃牌.md", "isdir": false, "fsid": 478892260204345, "ctime": 1754176556, "mtime": 1754000614, "size": 1241}, "工作室项目/免责条款/免责/普通女生最难做到的.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/普通女生最难做到的.md", "isdir": false, "fsid": 452804267717859, "ctime": 1754214533, "mtime": 1754216440, "size": 2043}, "切分笔记/弃牌.md": {"path": "/apps/obsidian/德州扑克/切分笔记/弃牌.md", "isdir": false, "fsid": 518675227925732, "ctime": 1754176556, "mtime": 1754000232, "size": 1239}, "切分笔记/弃牌的重要性.md": {"path": "/apps/obsidian/德州扑克/切分笔记/弃牌的重要性.md", "isdir": false, "fsid": 455171802289024, "ctime": 1754176556, "mtime": 1753952872, "size": 668}, "笔记/inbox笔记/枪口位（UTG）✅ 只玩顶级牌：比如 AA、KK、QQ、AK、AQ。  ❌ 扔掉垃圾牌：比如 Q4o、K3o、J7s、73s.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/枪口位（UTG）✅ 只玩顶级牌：比如 AA、KK、QQ、AK、AQ。  ❌ 扔掉垃圾牌：比如 Q4o、K3o、J7s、73s.md", "isdir": false, "fsid": 473172249994442, "ctime": 1754176814, "mtime": 1754177707, "size": 1891}, "切分笔记/枪口位（UTG）必须玩超强牌.md": {"path": "/apps/obsidian/德州扑克/切分笔记/枪口位（UTG）必须玩超强牌.md", "isdir": false, "fsid": 898907329882587, "ctime": 1754176556, "mtime": 1754001036, "size": 1148}, "切分笔记/强牌平跟策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/强牌平跟策略.md", "isdir": false, "fsid": 717992764044664, "ctime": 1754220578, "mtime": 1754220590, "size": 627}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/人味—酸豆角.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/人味—酸豆角.md", "isdir": false, "fsid": 738865608179997, "ctime": 1754185147, "mtime": 1754215387, "size": 15218}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单使用指南.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单使用指南.md", "isdir": false, "fsid": 985935994562325, "ctime": 1754187912, "mtime": 1754215387, "size": 7927}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板/人味模板完整提取.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板/人味模板完整提取.md", "isdir": false, "fsid": 368272288797947, "ctime": 1754187157, "mtime": 1754187186, "size": 7200}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板/人味模板完整提取_补充.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单模板/人味模板完整提取_补充.md", "isdir": false, "fsid": 613472284084382, "ctime": 1754187222, "mtime": 1754187226, "size": 7736}, "resource/components/area/日记-月复盘.components": {"path": "/apps/obsidian/德州扑克/resource/components/area/日记-月复盘.components", "isdir": false, "fsid": 1061838151323183, "ctime": **********, "mtime": 1735303940, "size": 5689}, "resource/components/area/日记.components": {"path": "/apps/obsidian/德州扑克/resource/components/area/日记.components", "isdir": false, "fsid": 876334167172924, "ctime": **********, "mtime": 1746259748, "size": 25426}, "工作室项目/免责条款/免责/如果你想开一家「隐私合规工作室」.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/如果你想开一家「隐私合规工作室」.md", "isdir": false, "fsid": 258119092765307, "ctime": 1754214533, "mtime": 1754216440, "size": 7945}, "笔记/inbox笔记/如果一个人拿到 AA、KK、AK 这种顶级牌，他通常会：        - 在更早的位置（如枪口位UTG）直接加注，因为不想错过价值。            - 在中间位置（MP）可能会选择“平跟”埋伏，希望后面有人加注，他再反加（3bet），这样能赚更多钱。.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/如果一个人拿到 AA、KK、AK 这种顶级牌，他通常会：        - 在更早的位置（如枪口位UTG）直接加注，因为不想错过价值。            - 在中间位置（MP）可能会选择“平跟”埋伏，希望后面有人加注，他再反加（3bet），这样能赚更多钱。.md", "isdir": false, "fsid": 897875709940188, "ctime": 1754176814, "mtime": 1754148310, "size": 125}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/如何避免“随大流”.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/如何避免“随大流”.md", "isdir": false, "fsid": 285773664034190, "ctime": 1754185147, "mtime": 1754215387, "size": 6075}, "工作室项目/免责条款/免责/如何向企业传递.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/如何向企业传递.md", "isdir": false, "fsid": 321432894036051, "ctime": 1754214533, "mtime": 1754216440, "size": 641}, "切分笔记/少跟注的理由.md": {"path": "/apps/obsidian/德州扑克/切分笔记/少跟注的理由.md", "isdir": false, "fsid": 2235237201970, "ctime": 1754218950, "mtime": 1754218950, "size": 845}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/摄影构图式人味记录表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/摄影构图式人味记录表单.cform", "isdir": false, "fsid": 981019387703480, "ctime": 1754188156, "mtime": 1754188156, "size": 11431}, "工作室项目/免责条款/免责/深度解析“婚后男人抠门不交钱”现象.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/深度解析“婚后男人抠门不交钱”现象.md", "isdir": false, "fsid": 378050737612282, "ctime": 1754214533, "mtime": 1754216440, "size": 4948}, "切分笔记/胜率.md": {"path": "/apps/obsidian/德州扑克/切分笔记/胜率.md", "isdir": false, "fsid": 258694717906254, "ctime": 1754176556, "mtime": 1754000229, "size": 1266}, "工作室项目/免责条款/免责/什么时候需要找律师.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/什么时候需要找律师.md", "isdir": false, "fsid": 376419124908425, "ctime": 1754214533, "mtime": 1754216440, "size": 2581}, "切分笔记/什么是“起手位置”？.md": {"path": "/apps/obsidian/德州扑克/切分笔记/什么是“起手位置”？.md", "isdir": false, "fsid": 391522019773425, "ctime": 1754176556, "mtime": 1754001556, "size": 1593}, "口语训练/杂七杂八/英语练习插件/使用演示.md": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/使用演示.md", "isdir": false, "fsid": 493479289381787, "ctime": **********, "mtime": 1754088372, "size": 5993}, "resource/components/area/书库.components": {"path": "/apps/obsidian/德州扑克/resource/components/area/书库.components", "isdir": false, "fsid": 291465473016707, "ctime": **********, "mtime": 1746320204, "size": 29314}, "工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/数据合规领域卡片模板.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/卢曼卡片模板/数据合规领域卡片模板.md", "isdir": false, "fsid": 622029662984392, "ctime": 1754182119, "mtime": 1754182135, "size": 8115}, "工作室项目/卢曼卡片/script和表单/卢曼表单/数据合规问答表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/数据合规问答表单.cform", "isdir": false, "fsid": 709380116541131, "ctime": 1754182507, "mtime": 1754183009, "size": 5494}, "英语/口语训练/具体练习/数据迁移合规评估.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习/数据迁移合规评估.md", "isdir": false, "fsid": 544785433252290, "ctime": **********, "mtime": 1754145206, "size": 1090}, "切分笔记/数学概率-长期来看，烂牌翻牌成牌的概率低，即使成牌也可能被更好的牌反杀.md": {"path": "/apps/obsidian/德州扑克/切分笔记/数学概率-长期来看，烂牌翻牌成牌的概率低，即使成牌也可能被更好的牌反杀.md", "isdir": false, "fsid": 420977476746520, "ctime": 1754176556, "mtime": 1753958642, "size": 736}, "切分笔记/松凶玩家的特点.md": {"path": "/apps/obsidian/德州扑克/切分笔记/松凶玩家的特点.md", "isdir": false, "fsid": 349682424360883, "ctime": 1754218950, "mtime": 1754218950, "size": 842}, "切分笔记/松凶玩家跟注易被剥削.md": {"path": "/apps/obsidian/德州扑克/切分笔记/松凶玩家跟注易被剥削.md", "isdir": false, "fsid": 698536324819763, "ctime": 1754220580, "mtime": 1754220585, "size": 650}, "切分笔记/松凶玩家下注习惯.md": {"path": "/apps/obsidian/德州扑克/切分笔记/松凶玩家下注习惯.md", "isdir": false, "fsid": 931970807358730, "ctime": 1754220578, "mtime": 1754220591, "size": 618}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/酸豆角式人味创作表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/酸豆角式人味创作表单.cform", "isdir": false, "fsid": 68948392171619, "ctime": 1754185822, "mtime": 1754186195, "size": 10643}, "口语训练/杂七杂八/动图/天空 动漫 女生 彩虹 夜景 GIF_爱给网_aigei_com 1.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/天空 动漫 女生 彩虹 夜景 GIF_爱给网_aigei_com 1.gif", "isdir": false, "fsid": 216829552855189, "ctime": **********, "mtime": 1754091815, "size": 4692547}, "口语训练/杂七杂八/动图/天空 动漫 女生 彩虹 夜景 GIF_爱给网_aigei_com.gif": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/动图/天空 动漫 女生 彩虹 夜景 GIF_爱给网_aigei_com.gif", "isdir": false, "fsid": 609406515663763, "ctime": **********, "mtime": 1754088672, "size": 4692547}, "切分笔记/偷鸡的定义.md": {"path": "/apps/obsidian/德州扑克/切分笔记/偷鸡的定义.md", "isdir": false, "fsid": 630325104107293, "ctime": 1754176556, "mtime": 1754001708, "size": 658}, "切分笔记/偷盲策略适用情况.md": {"path": "/apps/obsidian/德州扑克/切分笔记/偷盲策略适用情况.md", "isdir": false, "fsid": 483852916068386, "ctime": 1754220579, "mtime": 1754220588, "size": 729}, "切分笔记/偷盲加注示例.md": {"path": "/apps/obsidian/德州扑克/切分笔记/偷盲加注示例.md", "isdir": false, "fsid": 646202692282915, "ctime": 1754220579, "mtime": 1754220587, "size": 797}, "笔记/inbox笔记/为什么大盲（BB）和小盲（SB）可以玩更宽的牌？大盲（BB） 已经放了钱，跟注成本低，可以玩更宽的牌（比如同花连张、小对子）.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/为什么大盲（BB）和小盲（SB）可以玩更宽的牌？大盲（BB） 已经放了钱，跟注成本低，可以玩更宽的牌（比如同花连张、小对子）.md", "isdir": false, "fsid": 402264085911722, "ctime": 1754176814, "mtime": 1754000956, "size": 3158}, "切分笔记/位置差但“被迫防守”即使拿烂牌（如72o、J3s）也得跟注或加注，防止被剥削.md": {"path": "/apps/obsidian/德州扑克/切分笔记/位置差但“被迫防守”即使拿烂牌（如72o、J3s）也得跟注或加注，防止被剥削.md", "isdir": false, "fsid": 682776574015832, "ctime": 1754176556, "mtime": 1754002219, "size": 1229}, "切分笔记/位置差的劣势.md": {"path": "/apps/obsidian/德州扑克/切分笔记/位置差的劣势.md", "isdir": false, "fsid": 653458582129608, "ctime": 1754176556, "mtime": 1753952870, "size": 752}, "笔记/inbox笔记/位置差还乱跟注.md": {"path": "/apps/obsidian/德州扑克/笔记/inbox笔记/位置差还乱跟注.md", "isdir": false, "fsid": 365079782221242, "ctime": 1754176814, "mtime": 1753952986, "size": 6084}, "切分笔记/位置优势.md": {"path": "/apps/obsidian/德州扑克/切分笔记/位置优势.md", "isdir": false, "fsid": 1085188078142945, "ctime": 1754176556, "mtime": 1754000233, "size": 1317}, "切分笔记/无脑跟注的风险-导致玩家在后期牌局中陷入不利，最终输光筹码.md": {"path": "/apps/obsidian/德州扑克/切分笔记/无脑跟注的风险-导致玩家在后期牌局中陷入不利，最终输光筹码.md", "isdir": false, "fsid": 757205003242634, "ctime": 1754176556, "mtime": 1753958753, "size": 670}, "英语/口语训练/具体练习/系统安全漏洞评估对话.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习/系统安全漏洞评估对话.md", "isdir": false, "fsid": 876358836836260, "ctime": **********, "mtime": 1754166335, "size": 1171}, "工作室项目/免责条款/免责/系统生成模板：.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/系统生成模板：.md", "isdir": false, "fsid": 607794434079166, "ctime": 1754214533, "mtime": 1754216440, "size": 382}, "工作室项目/卢曼卡片/script和表单/表单模板/想法快捷记录.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/想法快捷记录.cform", "isdir": false, "fsid": 917246937669202, "ctime": **********, "mtime": 1752722756, "size": 1598}, "工作室项目/卢曼卡片/script和表单/表单模板/项目管理.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/项目管理.cform", "isdir": false, "fsid": 464932824199284, "ctime": **********, "mtime": 1752722792, "size": 3492}, "工作室项目/免责条款/免责/项目转载于网络!.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/项目转载于网络!.md", "isdir": false, "fsid": 789865001444967, "ctime": 1754214533, "mtime": 1754216440, "size": 574}, "德州笔记同步/训练笔记/新手期训练计划.md": {"path": "/apps/obsidian/德州扑克/德州笔记同步/训练笔记/新手期训练计划.md", "isdir": false, "fsid": 296302357839183, "ctime": 1754176720, "mtime": 1754179765, "size": 12942}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/行业观察记录表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/行业观察记录表单.cform", "isdir": false, "fsid": 272375317747715, "ctime": 1754187627, "mtime": 1754187627, "size": 10796}, "resource/images/熊猫.png": {"path": "/apps/obsidian/德州扑克/resource/images/熊猫.png", "isdir": false, "fsid": 583451822215989, "ctime": **********, "mtime": 1715593437, "size": 80971}, "resource/images/熊猫吃饭.png": {"path": "/apps/obsidian/德州扑克/resource/images/熊猫吃饭.png", "isdir": false, "fsid": 312703845886270, "ctime": **********, "mtime": 1715593437, "size": 108732}, "resource/images/熊猫吃蔬菜.png": {"path": "/apps/obsidian/德州扑克/resource/images/熊猫吃蔬菜.png", "isdir": false, "fsid": 940319514127014, "ctime": **********, "mtime": 1715593437, "size": 122865}, "resource/images/熊猫喝饮料.png": {"path": "/apps/obsidian/德州扑克/resource/images/熊猫喝饮料.png", "isdir": false, "fsid": 666045800735091, "ctime": **********, "mtime": 1715593437, "size": 98945}, "resource/images/熊猫玩.png": {"path": "/apps/obsidian/德州扑克/resource/images/熊猫玩.png", "isdir": false, "fsid": 711427959805107, "ctime": **********, "mtime": 1715593437, "size": 80132}, "工作室项目/卢曼卡片/script和表单/卢曼表单/修复版合规思考表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/修复版合规思考表单.cform", "isdir": false, "fsid": 819558475966712, "ctime": 1754184929, "mtime": 1754184929, "size": 12318}, "工作室项目/卢曼卡片/script和表单/卢曼表单/修复版跨境数据传输表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/修复版跨境数据传输表单.cform", "isdir": false, "fsid": 697789777972264, "ctime": 1754184860, "mtime": 1754184860, "size": 14010}, "英语/口语训练/具体练习/续约谈判中的数据合规问题.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习/续约谈判中的数据合规问题.md", "isdir": false, "fsid": 328096998301522, "ctime": **********, "mtime": 1754105997, "size": 1207}, "resource/模板/学习笔记.md": {"path": "/apps/obsidian/德州扑克/resource/模板/学习笔记.md", "isdir": false, "fsid": 21875687347260, "ctime": **********, "mtime": 1753954089, "size": 92}, "工作室项目/卢曼卡片/script和表单/表单模板/学习笔记模板.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/表单模板/学习笔记模板.cform", "isdir": false, "fsid": 885686533275831, "ctime": **********, "mtime": 1752722733, "size": 3515}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/要将鲍斯威尔的“黑暗观察术”迁移到你的隐私合规工作室纪实写作中.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/要将鲍斯威尔的“黑暗观察术”迁移到你的隐私合规工作室纪实写作中.md", "isdir": false, "fsid": 520443809518845, "ctime": 1754185147, "mtime": 1754215387, "size": 14073}, "工作室项目/免责条款/免责/要像张廷玉一样构建「无可辩驳却自保无忧」的记录系统.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/要像张廷玉一样构建「无可辩驳却自保无忧」的记录系统.md", "isdir": false, "fsid": 553120098582047, "ctime": 1754214533, "mtime": 1754216440, "size": 4721}, "工作室项目/免责条款/免责/一、客户问“你为什么贵”.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/一、客户问“你为什么贵”.md", "isdir": false, "fsid": 889546385018399, "ctime": 1754214533, "mtime": 1754216440, "size": 10040}, "工作室项目/免责条款/免责/一、如果对方送你股份.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/一、如果对方送你股份.md", "isdir": false, "fsid": 503701733609245, "ctime": 1754214533, "mtime": 1754216440, "size": 5197}, "工作室项目/卢曼卡片/script和表单/人味表单/一键提取人味模板.js": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/一键提取人味模板.js", "isdir": false, "fsid": 63292514235720, "ctime": 1754186480, "mtime": 1754186480, "size": 6986}, "工作室项目/免责条款/免责/以下是为你量身定制的「5分钟每日写作行动清单」.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/以下是为你量身定制的「5分钟每日写作行动清单」.md", "isdir": false, "fsid": 196392803885274, "ctime": 1754214533, "mtime": 1754216440, "size": 11181}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/以下是为你量身定制的「5分钟每日写作行动清单」.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/以下是为你量身定制的「5分钟每日写作行动清单」.md", "isdir": false, "fsid": 792866786606347, "ctime": 1754185147, "mtime": 1754215387, "size": 11181}, "工作室项目/免责条款/免责/以下是针对隐私合规服务定价与商业模式的核心解析.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/以下是针对隐私合规服务定价与商业模式的核心解析.md", "isdir": false, "fsid": 110501378164786, "ctime": 1754214533, "mtime": 1754216440, "size": 4491}, "工作室项目/免责条款/免责/以下是专为你的隐私合规AI工作室设计的客户会议策略.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/以下是专为你的隐私合规AI工作室设计的客户会议策略.md", "isdir": false, "fsid": 466965441564599, "ctime": 1754214533, "mtime": 1754216440, "size": 4269}, "工作室项目/卢曼卡片/script和表单/人味表单/人味模板/以下是专为隐私合规顾问设计的「人味素材记录模板」.md": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味模板/以下是专为隐私合规顾问设计的「人味素材记录模板」.md", "isdir": false, "fsid": 614214695088030, "ctime": 1754185147, "mtime": 1754215387, "size": 14210}, "工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(1).md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(1).md", "isdir": false, "fsid": 83455286699851, "ctime": 1754214533, "mtime": 1754216440, "size": 47268}, "工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(2).md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(2).md", "isdir": false, "fsid": 900346730934731, "ctime": 1754214533, "mtime": 1754216440, "size": 47268}, "工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(3).md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(3).md", "isdir": false, "fsid": 1011420015898769, "ctime": 1754214533, "mtime": 1754216440, "size": 47268}, "工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(4).md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）(4).md", "isdir": false, "fsid": 560454281311666, "ctime": 1754214533, "mtime": 1754216440, "size": 47268}, "工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/隐私合规工作室的认证策略（分阶段指南）.md", "isdir": false, "fsid": 602196828130944, "ctime": 1754214533, "mtime": 1754216440, "size": 47265}, "工作室项目/免责条款/免责/隐私合规工作室极简说明书.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/隐私合规工作室极简说明书.md", "isdir": false, "fsid": 659009426040203, "ctime": 1754214533, "mtime": 1754216440, "size": 4284}, "英语/口语训练/具体练习/隐私投诉处理.md": {"path": "/apps/obsidian/德州扑克/英语/口语训练/具体练习/隐私投诉处理.md", "isdir": false, "fsid": 651697062730925, "ctime": **********, "mtime": 1754137190, "size": 1349}, "口语训练/练习场景/隐私影响评估会议.md": {"path": "/apps/obsidian/德州扑克/口语训练/练习场景/隐私影响评估会议.md", "isdir": false, "fsid": 187189465959593, "ctime": **********, "mtime": 1754050771, "size": 6929}, "切分笔记/应对策略：对抗对手加注的具体行动.md": {"path": "/apps/obsidian/德州扑克/切分笔记/应对策略：对抗对手加注的具体行动.md", "isdir": false, "fsid": 506088146654557, "ctime": 1754177110, "mtime": 1754177110, "size": 1424}, "切分笔记/应对松凶玩家的正确做法.md": {"path": "/apps/obsidian/德州扑克/切分笔记/应对松凶玩家的正确做法.md", "isdir": false, "fsid": 773426506715624, "ctime": 1754220580, "mtime": 1754220584, "size": 702}, "切分笔记/应对中间位置加注的策略-你的跟注或加注的牌也要更强（比如至少AQ+、JJ+）垃圾牌（如Q4o、K7s）直接弃掉，别送钱.md": {"path": "/apps/obsidian/德州扑克/切分笔记/应对中间位置加注的策略-你的跟注或加注的牌也要更强（比如至少AQ+、JJ+）垃圾牌（如Q4o、K7s）直接弃掉，别送钱.md", "isdir": false, "fsid": 848866993742371, "ctime": 1754176556, "mtime": 1754001882, "size": 1796}, "切分笔记/应对庄位加注的策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/应对庄位加注的策略.md", "isdir": false, "fsid": 851471451098557, "ctime": 1754176556, "mtime": 1754001923, "size": 1734}, "口语训练/杂七杂八/英语练习插件/英语练习插件 1.html": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/英语练习插件 1.html", "isdir": false, "fsid": 199512887872841, "ctime": **********, "mtime": 1753339577, "size": 36396}, "口语训练/杂七杂八/英语练习插件/英语练习插件.html": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/英语练习插件.html", "isdir": false, "fsid": 1053343174125099, "ctime": **********, "mtime": 1753339100, "size": 54613}, "口语训练/杂七杂八/英语练习插件/英语练习插件使用说明.md": {"path": "/apps/obsidian/德州扑克/口语训练/杂七杂八/英语练习插件/英语练习插件使用说明.md", "isdir": false, "fsid": 818242160232447, "ctime": **********, "mtime": 1754088372, "size": 6156}, "工作室项目/免责条款/免责/用GPT-4+爬虫做“隐私政策漏洞检测Demo”是什么.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/用GPT-4+爬虫做“隐私政策漏洞检测Demo”是什么.md", "isdir": false, "fsid": 85078612813011, "ctime": 1754214533, "mtime": 1754216440, "size": 6875}, "口语训练/练习场景/员工数据保护培训.md": {"path": "/apps/obsidian/德州扑克/口语训练/练习场景/员工数据保护培训.md", "isdir": false, "fsid": 1112568225855402, "ctime": **********, "mtime": 1754050603, "size": 4125}, "resource/脚本工具/原始知识卡片生成器.py": {"path": "/apps/obsidian/德州扑克/resource/脚本工具/原始知识卡片生成器.py", "isdir": false, "fsid": 250283293052943, "ctime": **********, "mtime": 1752799742, "size": 11699}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/约翰逊传背刺写法表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/约翰逊传背刺写法表单.cform", "isdir": false, "fsid": 244525205277815, "ctime": 1754188218, "mtime": 1754188218, "size": 11938}, "工作室项目/免责条款/免责/运营一个隐私合规且专注于行业交流的社群（如抖音视频创作、法律、医疗、金融等需要高度合规的领域）.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/运营一个隐私合规且专注于行业交流的社群（如抖音视频创作、法律、医疗、金融等需要高度合规的领域）.md", "isdir": false, "fsid": 1100266935066959, "ctime": 1754214533, "mtime": 1754216440, "size": 8663}, "工作室项目/免责条款/免责/在隐私合规领域.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/在隐私合规领域.md", "isdir": false, "fsid": 273422002971069, "ctime": 1754214533, "mtime": 1754216440, "size": 4547}, "工作室项目/免责条款/免责/在隐私合规领域的写作中.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/在隐私合规领域的写作中.md", "isdir": false, "fsid": 923027733110166, "ctime": 1754214533, "mtime": 1754216440, "size": 4785}, "工作室项目/免责条款/免责/责任转嫁失效.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/责任转嫁失效.md", "isdir": false, "fsid": 715339264800929, "ctime": 1754214533, "mtime": 1754216440, "size": 1037}, "工作室项目/免责条款/免责/这段文字充满了生活智慧和幽默感.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/这段文字充满了生活智慧和幽默感.md", "isdir": false, "fsid": 410604331347909, "ctime": 1754214533, "mtime": 1754216440, "size": 22510}, "工作室项目/免责条款/免责/这个截图展示了一份短视频剪辑的高频功能清单.md": {"path": "/apps/obsidian/德州扑克/工作室项目/免责条款/免责/这个截图展示了一份短视频剪辑的高频功能清单.md", "isdir": false, "fsid": 314096292057900, "ctime": 1754214533, "mtime": 1754216440, "size": 7660}, "切分笔记/正确策略：弃牌.md": {"path": "/apps/obsidian/德州扑克/切分笔记/正确策略：弃牌.md", "isdir": false, "fsid": 451198591972974, "ctime": 1754176556, "mtime": 1753957955, "size": 649}, "工作室项目/卢曼卡片/script和表单/卢曼表单/智能合规冲突分析表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/智能合规冲突分析表单.cform", "isdir": false, "fsid": 332694742674311, "ctime": 1754183929, "mtime": 1754184465, "size": 9852}, "工作室项目/卢曼卡片/script和表单/卢曼表单/智能合规思考表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/智能合规思考表单.cform", "isdir": false, "fsid": 366558595417689, "ctime": 1754183886, "mtime": 1754184422, "size": 11091}, "工作室项目/卢曼卡片/script和表单/卢曼表单/智能跨境数据传输表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/智能跨境数据传输表单.cform", "isdir": false, "fsid": 27039549281649, "ctime": 1754184056, "mtime": 1754184379, "size": 12247}, "工作室项目/卢曼卡片/script和表单/卢曼表单/智能数据合规问答表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/卢曼表单/智能数据合规问答表单.cform", "isdir": false, "fsid": 984874745197741, "ctime": 1754183997, "mtime": 1754184508, "size": 10277}, "切分笔记/中间位置加注不等于超级强牌.md": {"path": "/apps/obsidian/德州扑克/切分笔记/中间位置加注不等于超级强牌.md", "isdir": false, "fsid": 355486998956319, "ctime": 1754176556, "mtime": 1754001563, "size": 1905}, "切分笔记/中间位置加注不会乱诈唬的原因.md": {"path": "/apps/obsidian/德州扑克/切分笔记/中间位置加注不会乱诈唬的原因.md", "isdir": false, "fsid": 1097543466122184, "ctime": 1754176556, "mtime": 1754001593, "size": 1650}, "切分笔记/中间位置加注的范围-不会随便拿垃圾牌加注（比如72o、K3o），因为后面的人可能用更强的牌反击他.md": {"path": "/apps/obsidian/德州扑克/切分笔记/中间位置加注的范围-不会随便拿垃圾牌加注（比如72o、K3o），因为后面的人可能用更强的牌反击他.md", "isdir": false, "fsid": 410017386796008, "ctime": 1754176556, "mtime": 1754001813, "size": 1909}, "切分笔记/中间位置加注的原因.md": {"path": "/apps/obsidian/德州扑克/切分笔记/中间位置加注的原因.md", "isdir": false, "fsid": 848096733208446, "ctime": 1754176556, "mtime": 1754001558, "size": 1531}, "工作室项目/卢曼卡片/script和表单/人味表单/人味表单/专业洞察提炼表单.cform": {"path": "/apps/obsidian/德州扑克/工作室项目/卢曼卡片/script和表单/人味表单/人味表单/专业洞察提炼表单.cform", "isdir": false, "fsid": 1097153712868038, "ctime": 1754187809, "mtime": 1754187809, "size": 11909}, "切分笔记/庄位加注？他的范围很宽，可以用 更强的牌（JJ+、AQ+）反击.md": {"path": "/apps/obsidian/德州扑克/切分笔记/庄位加注？他的范围很宽，可以用 更强的牌（JJ+、AQ+）反击.md", "isdir": false, "fsid": 1120890375396560, "ctime": 1754176556, "mtime": 1754147934, "size": 714}, "切分笔记/庄位加注的含义.md": {"path": "/apps/obsidian/德州扑克/切分笔记/庄位加注的含义.md", "isdir": false, "fsid": 34062014235686, "ctime": 1754176556, "mtime": 1754001705, "size": 1921}, "切分笔记/庄位加注时的反击策略.md": {"path": "/apps/obsidian/德州扑克/切分笔记/庄位加注时的反击策略.md", "isdir": false, "fsid": 404401080302965, "ctime": 1754177110, "mtime": 1754177110, "size": 1382}, "切分笔记/庄位加注时如何应对大小盲位置-- 别随便弃牌！否则会被庄位疯狂剥削。- 用稍好的牌防守（比如任意对子、A2o+、K8s+），跟注或反加。- 如果牌太烂（如72o、J3o），该弃就弃，别送钱.md": {"path": "/apps/obsidian/德州扑克/切分笔记/庄位加注时如何应对大小盲位置-- 别随便弃牌！否则会被庄位疯狂剥削。- 用稍好的牌防守（比如任意对子、A2o+、K8s+），跟注或反加。- 如果牌太烂（如72o、J3o），该弃就弃，别送钱.md", "isdir": false, "fsid": 344443933326996, "ctime": 1754176556, "mtime": 1754002178, "size": 1120}, "切分笔记/庄位偷鸡的优势-对手防守弱：大小盲（SBBB）的牌通常很烂（比如72o、J3s），面对加注多数会直接弃牌.md": {"path": "/apps/obsidian/德州扑克/切分笔记/庄位偷鸡的优势-对手防守弱：大小盲（SBBB）的牌通常很烂（比如72o、J3s），面对加注多数会直接弃牌.md", "isdir": false, "fsid": 1015149100821539, "ctime": 1754176556, "mtime": 1754001773, "size": 1235}, "resource/attachments/组合统计【恋爱粉】.components": {"path": "/apps/obsidian/德州扑克/resource/attachments/组合统计【恋爱粉】.components", "isdir": false, "fsid": 272692340095541, "ctime": **********, "mtime": 1746163410, "size": 5703}, "resource/components/组合统计【恋爱粉】.components": {"path": "/apps/obsidian/德州扑克/resource/components/组合统计【恋爱粉】.components", "isdir": false, "fsid": 933040300593721, "ctime": **********, "mtime": 1746163574, "size": 5703}, "resource/components/组合统计【青春版】.components": {"path": "/apps/obsidian/德州扑克/resource/components/组合统计【青春版】.components", "isdir": false, "fsid": 716618476901548, "ctime": **********, "mtime": 1746163573, "size": 5703}, "resource/attachments/组合统计【青春版】.components": {"path": "/apps/obsidian/德州扑克/resource/attachments/组合统计【青春版】.components", "isdir": false, "fsid": 266763652099603, "ctime": **********, "mtime": 1746163413, "size": 5703}, "切分笔记/佐助观察团藏写轮眼数量计算复活次数.md": {"path": "/apps/obsidian/德州扑克/切分笔记/佐助观察团藏写轮眼数量计算复活次数.md", "isdir": false, "fsid": 792090737910610, "ctime": 1754177460, "mtime": 1754177460, "size": 1413}, "切分笔记/佐助战斗计算能力与德州扑克精准决策对应.md": {"path": "/apps/obsidian/德州扑克/切分笔记/佐助战斗计算能力与德州扑克精准决策对应.md", "isdir": false, "fsid": 1060505613978068, "ctime": 1754177460, "mtime": 1754177460, "size": 1525}, "切分笔记/佐助战斗计算与德州扑克精准读牌对应.md": {"path": "/apps/obsidian/德州扑克/切分笔记/佐助战斗计算与德州扑克精准读牌对应.md", "isdir": false, "fsid": 560538566040987, "ctime": 1754177460, "mtime": 1754177460, "size": 1825}}