#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据出境合规实务50问汇总脚本
从各个PDF文件夹中的full.md文件提取问题和答案，重新整理成单独的文件
"""

import os
import re
import json
from pathlib import Path

def extract_questions_from_md(file_path):
    """从markdown文件中提取问题和答案"""
    questions = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 多种匹配模式
        patterns = [
            # 匹配 "# 一、问题内容"
            r'^#\s*([一二三四五六七八九十百千万]+[一二三四五六七八九十]*、[^#\n]+)',
            # 匹配 "一、问题内容" (不带#)
            r'^([一二三四五六七八九十百千万]+[一二三四五六七八九十]*、[^#\n]+)$',
            # 匹配 "## 一、问题内容"
            r'^##\s*([一二三四五六七八九十百千万]+[一二三四五六七八九十]*、[^#\n]+)'
        ]

        all_matches = []

        # 尝试所有模式
        for pattern in patterns:
            matches = list(re.finditer(pattern, content, flags=re.MULTILINE))
            for match in matches:
                all_matches.append((match.start(), match.group(1).strip()))

        # 按位置排序
        all_matches.sort(key=lambda x: x[0])

        # 提取问题内容
        for i, (start_pos, question_title) in enumerate(all_matches):
            # 确定内容结束位置
            if i + 1 < len(all_matches):
                end_pos = all_matches[i + 1][0]
                question_content = content[start_pos:end_pos]
            else:
                question_content = content[start_pos:]

            # 清理内容，移除标题行
            lines = question_content.split('\n')
            content_lines = []
            skip_first = True
            for line in lines:
                if skip_first and (line.startswith('#') or re.match(r'^[一二三四五六七八九十百千万]+[一二三四五六七八九十]*、', line)):
                    skip_first = False
                    continue
                content_lines.append(line)

            clean_content = '\n'.join(content_lines).strip()

            # 提取问题编号
            number_match = re.match(r'([一二三四五六七八九十百千万]+[一二三四五六七八九十]*)、(.+)', question_title)
            if number_match:
                chinese_num = number_match.group(1)
                question_text = number_match.group(2)

                # 转换中文数字为阿拉伯数字
                arabic_num = chinese_to_arabic(chinese_num)

                questions.append({
                    'number': arabic_num,
                    'chinese_number': chinese_num,
                    'title': question_text,
                    'content': clean_content,
                    'full_title': question_title
                })

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")

    return questions

def chinese_to_arabic(chinese_num):
    """将中文数字转换为阿拉伯数字"""
    chinese_digits = {
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
        '百': 100, '千': 1000, '万': 10000
    }
    
    # 处理特殊情况
    if chinese_num == '十':
        return 10
    
    result = 0
    temp = 0
    
    for char in chinese_num:
        if char in chinese_digits:
            digit = chinese_digits[char]
            if digit == 10:
                if temp == 0:
                    temp = 1
                result += temp * 10
                temp = 0
            elif digit == 100:
                result += temp * 100
                temp = 0
            elif digit == 1000:
                result += temp * 1000
                temp = 0
            elif digit == 10000:
                result += temp * 10000
                temp = 0
            else:
                temp = digit
    
    result += temp
    return result

def find_full_md_files():
    """查找所有的full.md文件"""
    current_dir = Path('.')
    full_md_files = []
    
    for item in current_dir.iterdir():
        if item.is_dir() and 'pdf-' in item.name:
            full_md_path = item / 'full.md'
            if full_md_path.exists():
                full_md_files.append(full_md_path)
    
    return sorted(full_md_files)

def main():
    """主函数"""
    print("开始汇总数据出境合规实务50问...")
    
    # 查找所有full.md文件
    full_md_files = find_full_md_files()
    print(f"找到 {len(full_md_files)} 个full.md文件")
    
    # 提取所有问题
    all_questions = []
    for file_path in full_md_files:
        print(f"处理文件: {file_path}")
        questions = extract_questions_from_md(file_path)
        all_questions.extend(questions)
        print(f"从 {file_path} 提取了 {len(questions)} 个问题")
    
    # 按问题编号排序并去重
    unique_questions = {}
    for question in all_questions:
        key = question['number']
        if key not in unique_questions or len(question['content']) > len(unique_questions[key]['content']):
            unique_questions[key] = question

    # 转换为列表并排序
    final_questions = list(unique_questions.values())
    final_questions.sort(key=lambda x: x['number'])

    # 只保留1-50的问题
    final_questions = [q for q in final_questions if 1 <= q['number'] <= 50]

    print(f"总共提取了 {len(all_questions)} 个问题，去重后保留 {len(final_questions)} 个问题")

    # 创建输出文件夹
    output_dir = Path('数据出境合规实务50问汇总')
    output_dir.mkdir(exist_ok=True)

    # 为每个问题创建单独的文件
    for question in final_questions:
        filename = f"{question['number']:02d}_{question['title'][:20].replace('/', '_').replace('?', '').replace('？', '')}.md"
        # 清理文件名中的特殊字符
        filename = re.sub(r'[<>:"/\\|*?]', '_', filename)

        file_path = output_dir / filename

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"# {question['full_title']}\n\n")
            f.write(question['content'])

        print(f"创建文件: {filename}")

    # 创建总目录文件
    index_path = output_dir / "00_目录.md"
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write("# 数据出境合规实务50问 - 目录\n\n")
        for question in final_questions:
            f.write(f"{question['number']}. {question['title']}\n")

    print(f"\n汇总完成！所有文件已保存到 '{output_dir}' 文件夹中")
    print(f"共生成 {len(final_questions)} 个问题文件和1个目录文件")

if __name__ == "__main__":
    main()
