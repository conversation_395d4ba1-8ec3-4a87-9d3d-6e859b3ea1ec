#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按样本格式生成网信办文件脚本
根据湖南网信办样本格式，生成所有网信办文件并放到单独文件夹
"""

import os
from pathlib import Path

# 网信办数据
contact_data = {
    "国家网信办.md": {
        "省份": "国家级",
        "级别": "国家级",
        "地址": "北京市西城区车公庄大街11号",
        "电话": "数据出境安全评估申报: 010-55627135; 个人信息出境标准合同备案: 010-55627565; 个人信息保护认证申请: 010-82261100",
        "名称": "国家互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "全国数据出境政策制定和监管",
        "注意点": "不同业务类型有专门的联系电话，请按需拨打"
    },
    "北京网信办.md": {
        "省份": "北京",
        "级别": "直辖市",
        "地址": "北京市朝阳区华威南路弘善家园413号",
        "电话": "010-67676912",
        "名称": "北京市互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "作为首都，政策执行相对严格",
        "注意点": "建议充分准备申报材料"
    },
    "天津网信办.md": {
        "省份": "天津",
        "级别": "直辖市",
        "地址": "天津市河西区梅江道20号",
        "电话": "022-88355322",
        "名称": "天津市互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "上海网信办.md": {
        "省份": "上海",
        "级别": "直辖市",
        "地址": "上海市徐汇区宛平路315号",
        "电话": "021-64743030-2711",
        "名称": "上海市互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "上海自贸区在数据出境方面可能享有便利监管措施",
        "注意点": "金融企业需特别关注行业特殊要求"
    },
    "重庆网信办.md": {
        "省份": "重庆",
        "级别": "直辖市",
        "地址": "重庆市渝北区青竹东路6号",
        "电话": "023-63151805",
        "名称": "重庆市互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "河北网信办.md": {
        "省份": "河北",
        "级别": "省级",
        "地址": "河北省石家庄市桥西区维明南大街79号",
        "电话": "0311-87909716",
        "名称": "河北省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "河南网信办.md": {
        "省份": "河南",
        "级别": "省级",
        "地址": "河南省郑州市金水区金水路16号",
        "电话": "0371-65901067",
        "名称": "河南省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "浙江网信办.md": {
        "省份": "浙江",
        "级别": "省级",
        "地址": "浙江省杭州市西湖区省府路29号",
        "电话": "0571-81051250",
        "名称": "浙江省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "数字经济发达地区，政策相对开放",
        "注意点": ""
    },
    "江苏网信办.md": {
        "省份": "江苏",
        "级别": "省级",
        "地址": "江苏省南京市建邺区白龙江东街8号",
        "电话": "025-63090194",
        "名称": "江苏省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "福建网信办.md": {
        "省份": "福建",
        "级别": "省级",
        "地址": "福建省福州市鼓楼区北大路133号",
        "电话": "0591-86300613",
        "名称": "福建省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "安徽网信办.md": {
        "省份": "安徽",
        "级别": "省级",
        "地址": "安徽省合肥市包河区中山路1号",
        "电话": "0551-62606014",
        "名称": "安徽省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "贵州网信办.md": {
        "省份": "贵州",
        "级别": "省级",
        "地址": "贵州省贵阳市云岩区宝山北路39号",
        "电话": "0851-82995001 / 0851-82995061",
        "名称": "贵州省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "山东网信办.md": {
        "省份": "山东",
        "级别": "省级",
        "地址": "山东省济南市市中区经十路20637号",
        "电话": "0531-51773249 / 0531-51771297",
        "名称": "山东省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "广东网信办.md": {
        "省份": "广东",
        "级别": "省级",
        "地址": "广东省广州市越秀区中山一路104号",
        "电话": "020-87100794 / 020-87100793",
        "名称": "广东省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "经济发达地区，数据出境需求较多",
        "注意点": "深圳前海等地区可能有特殊政策"
    },
    "陕西网信办.md": {
        "省份": "陕西",
        "级别": "省级",
        "地址": "陕西省西安市雁塔区雁塔路南段10号",
        "电话": "029-63907136",
        "名称": "陕西省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "甘肃网信办.md": {
        "省份": "甘肃",
        "级别": "省级",
        "地址": "甘肃省兰州市城关区南昌路1648号",
        "电话": "0931-8928721",
        "名称": "甘肃省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "山西网信办.md": {
        "省份": "山西",
        "级别": "省级",
        "地址": "山西省太原市迎泽区五一路36号",
        "电话": "0351-5236020",
        "名称": "山西省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "江西网信办.md": {
        "省份": "江西",
        "级别": "省级",
        "地址": "江西省南昌市红谷滩区卧龙路999号",
        "电话": "0791-88912737",
        "名称": "江西省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "云南网信办.md": {
        "省份": "云南",
        "级别": "省级",
        "地址": "云南省昆明市西山区日新中路516号",
        "电话": "0871-63902424",
        "名称": "云南省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "湖北网信办.md": {
        "省份": "湖北",
        "级别": "省级",
        "地址": "湖北省武汉市武昌区水果湖路268号",
        "电话": "027-87231397",
        "名称": "湖北省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "湖南网信办.md": {
        "省份": "湖南",
        "级别": "省级",
        "地址": "湖南省长沙市芙蓉区韶山北路1号",
        "电话": "0731-81121089",
        "名称": "湖南省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "青海网信办.md": {
        "省份": "青海",
        "级别": "省级",
        "地址": "青海省西宁市海湖新区文景街32号",
        "电话": "0971-8485510",
        "名称": "青海省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "辽宁网信办.md": {
        "省份": "辽宁",
        "级别": "省级",
        "地址": "辽宁省沈阳市和平区光荣街26号甲",
        "电话": "024-81680082",
        "名称": "辽宁省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "吉林网信办.md": {
        "省份": "吉林",
        "级别": "省级",
        "地址": "吉林省长春市朝阳区新发路666号",
        "电话": "0431-82761087",
        "名称": "吉林省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "黑龙江网信办.md": {
        "省份": "黑龙江",
        "级别": "省级",
        "地址": "黑龙江省哈尔滨市南岗区华山路12号",
        "电话": "0451-58685723",
        "名称": "黑龙江省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "海南网信办.md": {
        "省份": "海南",
        "级别": "省级",
        "地址": "海南省海口市国兴大道69号",
        "电话": "0898-65380723",
        "名称": "海南省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "海南自贸港可能有特殊的数据流动政策",
        "注意点": "关注自贸港相关便利化措施"
    },
    "四川网信办.md": {
        "省份": "四川",
        "级别": "省级",
        "地址": "四川省成都市青羊区桂花巷21号",
        "电话": "028-86601862",
        "名称": "四川省互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "广西网信办.md": {
        "省份": "广西",
        "级别": "自治区",
        "地址": "广西壮族自治区南宁市青秀区民族大道112号",
        "电话": "0771-2093017 / 0771-2093049",
        "名称": "广西壮族自治区互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "宁夏网信办.md": {
        "省份": "宁夏",
        "级别": "自治区",
        "地址": "宁夏回族自治区银川市金风区康平路1号",
        "电话": "0951-6668938",
        "名称": "宁夏回族自治区互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "西藏网信办.md": {
        "省份": "西藏",
        "级别": "自治区",
        "地址": "西藏自治区拉萨市城关区农科路7号",
        "电话": "0891-6591509",
        "名称": "西藏自治区互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "内蒙古网信办.md": {
        "省份": "内蒙古",
        "级别": "自治区",
        "地址": "内蒙古自治区呼和浩特市赛罕区银河南街8号",
        "电话": "0471-4821277",
        "名称": "内蒙古自治区互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "新疆网信办.md": {
        "省份": "新疆",
        "级别": "自治区",
        "地址": "新疆维吾尔自治区乌鲁木齐市新市区西环北路2221号",
        "电话": "0991-2384855",
        "名称": "新疆维吾尔自治区互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    },
    "新疆兵团网信办.md": {
        "省份": "新疆兵团",
        "级别": "兵团",
        "地址": "新疆维吾尔自治区乌鲁木齐市天山区中山路462号",
        "电话": "0991-2899091",
        "名称": "新疆生产建设兵团互联网信息办公室",
        "负责人": "",
        "负责人联系方式": "",
        "地区政策": "",
        "注意点": ""
    }
}

def generate_contact_file(filename, data):
    """按样本格式生成联系方式文件"""
    content = f"""---
省份: {data['省份']}
级别: {data['级别']}
地址: {data['地址']}
电话: {data['电话']}
名称: {data['名称']}
负责人: {data['负责人']}
负责人联系方式: {data['负责人联系方式']}
地区政策: {data['地区政策']}
注意点: {data['注意点']}
---

"""
    return content

def main():
    """主函数"""
    print("开始按样本格式生成网信办文件...")
    
    # 创建网信办文件夹
    base_dir = Path('数据出境合规实务50问汇总')
    netinfo_dir = base_dir / '网信办联系方式'
    netinfo_dir.mkdir(exist_ok=True)
    
    # 生成每个网信办文件
    for filename, data in contact_data.items():
        content = generate_contact_file(filename, data)
        file_path = netinfo_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"生成文件: {filename}")
    
    # 移动原有的网信办文件到新文件夹（如果存在的话）
    for filename in contact_data.keys():
        old_file = base_dir / filename
        if old_file.exists():
            old_file.unlink()  # 删除原文件
    
    print(f"\n生成完成！")
    print(f"共生成 {len(contact_data)} 个网信办文件")
    print(f"文件保存在: {netinfo_dir}")

if __name__ == "__main__":
    main()
