# 二十四、 如何准确识别跨境传输数据的类型？

在实践中，企业往往会遇到难以识别个人信息、敏感个人信息、重要数据的问题。

# （一）个人信息的识别

《个人信息保护法》与《网络安全法》均明确了个人信息的概念，其判断的标准主要在于信息是否“已识别”或“可识别”自然人个人身份。但是，经过匿名化处理过的信息则不属于个人信息16。

企业通常对个人信息进行去标识化或匿名化处理。需要注意的是，虽然经匿名化处理的个人信息因无法再识别到个人而不再是个人信息，但是经去标识化处理的个人信息若在借助额外信息的情况下可以再次识别到个人，则仍属于个人信息。例如，企业曾向境外接收方传输用户的手机号码、地址、姓名等完整字段，境外接收方也在其数据库中对这些字段进行了保留。在这种情况下，即使出境方企业在本次数据传输中使用了去标识化措施，境外接收方仍可以通过撞库或者用户 ID 映射的方式将这些信息识别到具体个人。此时，出境后的数据依然保留了个人信息的属性，应当被认为属于个人信息出境。

# （二）敏感个人信息的识别

根据《个人信息保护法》，敏感个人信息是指一旦泄露或者非法使用，容易导致自然人的人格尊严受到侵害或者人身、财产安全受到危害的个人信息，包括生物识别、宗教信仰、特定身份、医疗健康、金融账户、行踪轨迹等信息以及不满十四周岁未成年人的个人信息17。相较于个人信息，敏感个人信息遭到损害后会造成更为严重的影响。GB/T 35273-2020《信息安全技术 个人信息安全规范》在附录B 列举了部分敏感个人信息类型，为企业合规实践提供了指引。

但是，在实践中，许多企业对经过去标识化处理后的敏感个人信息是否还应被视为敏感个人信息存在疑问。根据向省级网信办等监管部门咨询的结果，如果去标识化处理能够改变敏感个人信息的性质，那么经该等处理后的个人信息不再属于敏感个人信息。换言之，如果经过去标识化处理的敏感个人信息被泄露或者非法使用，且泄露和非法使用不会导致自然人的人格尊严受到侵害或者人身、财产安全受到危害，那么经过去标识化处理的“敏感个人信息”不再属于敏感个人信息。

# （三）重要数据的识别

（具体内容请见《上篇：基础篇》“十五、识别重要数据的法律法规依据有

哪些？”部分）