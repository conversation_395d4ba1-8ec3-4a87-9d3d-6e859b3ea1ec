{"name": "德州扑克", "md5": "", "children": [{"name": "extract_disclaimer_clauses.py", "type": "file", "size": 12233, "mtime": 1754214768000}, {"name": "generate_disclaimer_summary.py", "type": "file", "size": 10323, "mtime": 1754214857000}, {"name": "resource", "type": "directory", "size": 12658124, "mtime": 1754304218000, "children": [{"name": "attachments", "type": "directory", "size": 3190052, "mtime": 1754304218000, "children": [{"name": "🤖 AI 干点儿活.cform", "type": "file", "size": 5604, "mtime": 1746163430000}, {"name": "DVJS-多条件复合检索V2 1.components", "type": "file", "size": 8710, "mtime": 1746163427000}, {"name": "DVJS-多条件复合检索V2.components", "type": "file", "size": 8710, "mtime": 1746163424000}, {"name": "Pasted image 20240513163145.png", "type": "file", "size": 1564267, "mtime": 1715593437000}, {"name": "Pasted image 20240513172142.png", "type": "file", "size": 1586547, "mtime": 1715593437000}, {"name": "组合统计【恋爱粉】.components", "type": "file", "size": 5703, "mtime": 1746163410000}, {"name": "组合统计【青春版】.components", "type": "file", "size": 5703, "mtime": 1746163413000}, {"name": "附件库.components", "type": "file", "size": 4808, "mtime": 1746163416000}]}, {"name": "components", "type": "directory", "size": 298065, "mtime": 1754304271000, "children": [{"name": "🤖 AI 干点儿活.cform", "type": "file", "size": 5604, "mtime": 1746162754000}, {"name": "area", "type": "directory", "size": 88749, "mtime": 1754304271000, "children": [{"name": "pkm.components", "type": "file", "size": 3652, "mtime": 1746260023000}, {"name": "书库.components", "type": "file", "size": 29314, "mtime": 1746320204000}, {"name": "工作.components", "type": "file", "size": 20197, "mtime": 1746312350000}, {"name": "旅游.components", "type": "file", "size": 4471, "mtime": 1746260039000}, {"name": "日记.components", "type": "file", "size": 25426, "mtime": 1746259748000}, {"name": "日记-月复盘.components", "type": "file", "size": 5689, "mtime": 1735303940000}]}, {"name": "DVJS-多条件复合检索V2.components", "type": "file", "size": 8710, "mtime": 1746163529000}, {"name": "scripts", "type": "directory", "size": 157751, "mtime": 1754304296000, "children": [{"name": "addBulletPoints.js", "type": "file", "size": 2338, "mtime": 1753067290000}, {"name": "aifront(1).js", "type": "file", "size": 5335, "mtime": 1752707735000}, {"name": "aifront.js", "type": "file", "size": 5340, "mtime": 1752713631000}, {"name": "aiSummary (1).js", "type": "file", "size": 2305, "mtime": 1749277202000}, {"name": "atomicNoteSplitter.js", "type": "file", "size": 5913, "mtime": 1754343264000}, {"name": "batchAI.js", "type": "file", "size": 10478, "mtime": 1754019616000}, {"name": "calculateScore.js", "type": "file", "size": 6433, "mtime": 1754094354000}, {"name": "calculateSleepQuality.js", "type": "file", "size": 3135, "mtime": 1753079685000}, {"name": "calculateSleepQualityPro.js", "type": "file", "size": 3248, "mtime": 1753079601000}, {"name": "calculatesSleepTime.js", "type": "file", "size": 2536, "mtime": 1753079572000}, {"name": "createCorpusNote.js", "type": "file", "size": 11841, "mtime": 1754079414000}, {"name": "createPracticeNote.js", "type": "file", "size": 5354, "mtime": 1754094170000}, {"name": "customAI.js", "type": "file", "size": 7896, "mtime": 1753960219000}, {"name": "extractHeadingContent.js", "type": "file", "size": 2854, "mtime": 1753067289000}, {"name": "generateSpecificScenario.js", "type": "file", "size": 18305, "mtime": 1754084424000}, {"name": "insertTaskToProject.js", "type": "file", "size": 1569, "mtime": 1753067290000}, {"name": "insertTaskToProject2.0.js", "type": "file", "size": 2603, "mtime": 1753067290000}, {"name": "insertWeek.js", "type": "file", "size": 3867, "mtime": 1753067290000}, {"name": "keywordFlowchartGenerator.js", "type": "file", "size": 7746, "mtime": 1753860680000}, {"name": "practiceEvaluation.js", "type": "file", "size": 9184, "mtime": 1754095339000}, {"name": "quickAI_MultiAPI.js", "type": "file", "size": 13025, "mtime": 1754074879000}, {"name": "quickAI2.js", "type": "file", "size": 13050, "mtime": 1754042241000}, {"name": "sleepCalculator-1.0.js", "type": "file", "size": 2297, "mtime": 1753067290000}, {"name": "smartHeadingExtractor.js", "type": "file", "size": 3435, "mtime": 1753067290000}, {"name": "switchLightDark.js", "type": "file", "size": 504, "mtime": 1731453377000}, {"name": "updatePracticeDate.js", "type": "file", "size": 1754, "mtime": 1754047767000}, {"name": "xiaohongshu.js", "type": "file", "size": 5406, "mtime": 1753934179000}]}, {"name": "views", "type": "directory", "size": 21037, "mtime": 1754304307000, "children": [{"name": "20250502132503.components", "type": "file", "size": 497, "mtime": 1746163504000}, {"name": "Area 导航.md", "type": "file", "size": 331, "mtime": 1746320293000}, {"name": "area.components", "type": "file", "size": 8194, "mtime": 1735297689000}, {"name": "Home-B.components", "type": "file", "size": 3214, "mtime": 1739240524000}, {"name": "导航栏.md", "type": "file", "size": 298, "mtime": 1746319755000}, {"name": "工作统计.components", "type": "file", "size": 6889, "mtime": 1746260183000}, {"name": "计时组件.components", "type": "file", "size": 1614, "mtime": 1739240511000}]}, {"name": "组合统计【恋爱粉】.components", "type": "file", "size": 5703, "mtime": 1746163574000}, {"name": "组合统计【青春版】.components", "type": "file", "size": 5703, "mtime": 1746163573000}, {"name": "附件库.components", "type": "file", "size": 4808, "mtime": 1746163567000}]}, {"name": "images", "type": "directory", "size": 625899, "mtime": 1754304244000, "children": [{"name": "thoughts.png", "type": "file", "size": 29635, "mtime": 1715923596000}, {"name": "typewriter.png", "type": "file", "size": 33266, "mtime": 1715923596000}, {"name": "呆萌熊猫.png", "type": "file", "size": 71353, "mtime": 1715593437000}, {"name": "熊猫.png", "type": "file", "size": 80971, "mtime": 1715593437000}, {"name": "熊猫吃蔬菜.png", "type": "file", "size": 122865, "mtime": 1715593437000}, {"name": "熊猫吃饭.png", "type": "file", "size": 108732, "mtime": 1715593437000}, {"name": "熊猫喝饮料.png", "type": "file", "size": 98945, "mtime": 1715593437000}, {"name": "熊猫玩.png", "type": "file", "size": 80132, "mtime": 1715593437000}]}, {"name": "图片素材", "type": "directory", "size": 8477528, "mtime": 1754304347000, "children": [{"name": "0c5b0198185be7c7874b56fdb90df141.jpg", "type": "file", "size": 20506, "mtime": 1739241879000}, {"name": "3254002f58d97566b5ceb0e54f99aadd.jpg", "type": "file", "size": 8974, "mtime": *************}, {"name": "50f1b13b70b37f02d5a7578acdc3e606.jpg", "type": "file", "size": 39661, "mtime": *************}, {"name": "anders-jilden-cYrMQA7a3Wc-unsplash.jpg", "type": "file", "size": 808153, "mtime": *************}, {"name": "braden-collum-75XHJzEIeUc-unsplash.jpg", "type": "file", "size": 2713115, "mtime": *************}, {"name": "carolina-nichitin-5OY83OiKlNQ-unsplash.jpg", "type": "file", "size": 3842929, "mtime": *************}, {"name": "clay-banks-QPivxTs7QVU-unsplash.jpg", "type": "file", "size": 1032833, "mtime": *************}, {"name": "LICENSE", "type": "file", "size": 11357, "mtime": *************}]}, {"name": "模板", "type": "directory", "size": 92, "mtime": *************, "children": [{"name": "学习笔记.md", "type": "file", "size": 92, "mtime": *************}]}, {"name": "脚本工具", "type": "directory", "size": 66488, "mtime": *************, "children": [{"name": "原始知识卡片生成器.py", "type": "file", "size": 11699, "mtime": *************}, {"name": "德州扑克知识卡片生成器.py", "type": "file", "size": 15009, "mtime": *************}, {"name": "股权财务法律知识库生成器.py", "type": "file", "size": 13246, "mtime": *************}, {"name": "默写本原子笔记生成器.py", "type": "file", "size": 12507, "mtime": *************}, {"name": "默写本知识卡片生成器_改进版.py", "type": "file", "size": 14027, "mtime": *************}]}]}, {"name": "Tags", "type": "directory", "size": 197917, "mtime": 1754304121000, "children": [{"name": "<PERSON><PERSON><PERSON>", "type": "directory", "size": 197917, "mtime": 1754304121000, "children": [{"name": "addBulletPoints.js", "type": "file", "size": 2338, "mtime": 1753067290000}, {"name": "aifront(1).js", "type": "file", "size": 5335, "mtime": 1752707735000}, {"name": "aifront.js", "type": "file", "size": 5340, "mtime": 1752713631000}, {"name": "aiSummary (1).js", "type": "file", "size": 2305, "mtime": 1749277202000}, {"name": "atomicNoteSplitter.js", "type": "file", "size": 8100, "mtime": 1753957417000}, {"name": "batchAI.js", "type": "file", "size": 10478, "mtime": 1754019616000}, {"name": "batchCreateCorpus.js", "type": "file", "size": 12377, "mtime": 1754169292000}, {"name": "calculatePracticeProgress.js", "type": "file", "size": 5893, "mtime": 1754093002000}, {"name": "calculateSleepQuality.js", "type": "file", "size": 3135, "mtime": 1753079685000}, {"name": "calculateSleepQualityPro.js", "type": "file", "size": 3248, "mtime": 1753079601000}, {"name": "calculatesSleepTime.js", "type": "file", "size": 2536, "mtime": 1753079572000}, {"name": "createCorpusNote.js", "type": "file", "size": 15514, "mtime": 1754166938000}, {"name": "createPracticeNote.js", "type": "file", "size": 5354, "mtime": 1754094170000}, {"name": "customAI.js", "type": "file", "size": 7896, "mtime": 1753960219000}, {"name": "extractCorpus.js", "type": "file", "size": 13726, "mtime": 1754166345000}, {"name": "extractHeadingContent.js", "type": "file", "size": 2854, "mtime": 1753067289000}, {"name": "generateSpecificScenario.js", "type": "file", "size": 18305, "mtime": 1754084424000}, {"name": "insertTaskToProject.js", "type": "file", "size": 1569, "mtime": 1753067290000}, {"name": "insertTaskToProject2.0.js", "type": "file", "size": 2603, "mtime": 1753067290000}, {"name": "insertWeek.js", "type": "file", "size": 3867, "mtime": 1753067290000}, {"name": "keywordFlowchartGenerator.js", "type": "file", "size": 7746, "mtime": 1753860680000}, {"name": "originalIntentSimple.js", "type": "file", "size": 8743, "mtime": 1754170002000}, {"name": "practiceEvaluation.js", "type": "file", "size": 9184, "mtime": 1754095339000}, {"name": "quickAI_MultiAPI.js", "type": "file", "size": 13025, "mtime": 1754074879000}, {"name": "quickAI2.js", "type": "file", "size": 13050, "mtime": 1754042241000}, {"name": "sleepCalculator-1.0.js", "type": "file", "size": 2297, "mtime": 1753067290000}, {"name": "smartHeadingExtractor.js", "type": "file", "size": 3435, "mtime": 1753067290000}, {"name": "switchLightDark.js", "type": "file", "size": 504, "mtime": 1731453377000}, {"name": "updatePracticeDate.js", "type": "file", "size": 1754, "mtime": 1754047767000}, {"name": "xiaohongshu.js", "type": "file", "size": 5406, "mtime": 1753934179000}]}]}, {"name": "其他主页", "type": "directory", "size": 437397, "mtime": 1754344701000, "children": [{"name": "Home 1.components", "type": "file", "size": 54796, "mtime": 1753952504000}, {"name": "Home.components", "type": "file", "size": 42973, "mtime": 1754319153000}, {"name": "Wiki Home.components", "type": "file", "size": 28424, "mtime": 1753952507000}, {"name": "🧜口语页.components", "type": "file", "size": 30632, "mtime": 1754172042000}, {"name": "📄句式.components", "type": "file", "size": 134111, "mtime": 1754095771000}, {"name": "📒复习页.components", "type": "file", "size": 34842, "mtime": 1754179590000}, {"name": "⛄概念关系.components", "type": "file", "size": 30829, "mtime": 1754319121000}, {"name": "🌈错题页.components", "type": "file", "size": 80790, "mtime": 1754319167000}]}, {"name": "切分笔记", "type": "directory", "size": 135247, "mtime": 1754298659000, "children": [{"name": "3bet.md", "type": "file", "size": 1308, "mtime": 1754000221000}, {"name": "3bet范围-玩家在3bet时可能使用的牌的范围.md", "type": "file", "size": 1364, "mtime": 1754000616000}, {"name": "4bet诈唬-在对手3bet后，玩家进行再次加注以吓唬对手.md", "type": "file", "size": 1318, "mtime": 1754000610000}, {"name": "T♠9♠.md", "type": "file", "size": 1268, "mtime": 1754000225000}, {"name": "中间位置加注不会乱诈唬的原因.md", "type": "file", "size": 1650, "mtime": 1754001593000}, {"name": "中间位置加注不等于超级强牌.md", "type": "file", "size": 1905, "mtime": 1754001563000}, {"name": "中间位置加注的原因.md", "type": "file", "size": 1531, "mtime": 1754001558000}, {"name": "中间位置加注的范围-不会随便拿垃圾牌加注（比如72o、K3o），因为后面的人可能用更强的牌反击他.md", "type": "file", "size": 1909, "mtime": 1754001813000}, {"name": "什么是“起手位置”？.md", "type": "file", "size": 1593, "mtime": 1754001556000}, {"name": "位置优势.md", "type": "file", "size": 1317, "mtime": 1754000233000}, {"name": "位置优势加上干燥牌面是完美的埋伏时机。.md", "type": "file", "size": 482, "mtime": 1754344415000}, {"name": "位置差但“被迫防守”即使拿烂牌（如72o、J3s）也得跟注或加注，防止被剥削.md", "type": "file", "size": 1229, "mtime": 1754002219000}, {"name": "位置差的劣势.md", "type": "file", "size": 752, "mtime": 1753952870000}, {"name": "佐助战斗计算与德州扑克精准读牌对应.md", "type": "file", "size": 1825, "mtime": 1754177460000}, {"name": "佐助战斗计算能力与德州扑克精准决策对应.md", "type": "file", "size": 1525, "mtime": 1754177460000}, {"name": "佐助观察团藏写轮眼数量计算复活次数.md", "type": "file", "size": 1413, "mtime": 1754177460000}, {"name": "偷盲加注示例.md", "type": "file", "size": 797, "mtime": 1754220587000}, {"name": "偷盲策略适用情况.md", "type": "file", "size": 729, "mtime": 1754220588000}, {"name": "偷鸡的定义.md", "type": "file", "size": 658, "mtime": 1754001708000}, {"name": "关煞位（CO）较好的位置优势.md", "type": "file", "size": 1316, "mtime": 1754000619000}, {"name": "出错点分析.md", "type": "file", "size": 980, "mtime": 1754177107000}, {"name": "利用心理盲区与对手思维漏洞对应.md", "type": "file", "size": 1419, "mtime": 1754177460000}, {"name": "加注开池.md", "type": "file", "size": 1317, "mtime": 1754000226000}, {"name": "加注时机选择.md", "type": "file", "size": 606, "mtime": 1754220590000}, {"name": "动态调整策略与调整自己的范围对应：对紧弱玩家：多偷盲，少诈唬。对松凶玩家：多埋伏（Trap），少跟注。.md", "type": "file", "size": 2740, "mtime": 1754218898000}, {"name": "埋伏位置的重要性.md", "type": "file", "size": 399, "mtime": 1754344439000}, {"name": "埋伏位置选择.md", "type": "file", "size": 581, "mtime": 1754220593000}, {"name": "埋伏位置选择_在有利位置或牌面干燥时操作。.md", "type": "file", "size": 2900, "mtime": 1754344408000}, {"name": "埋伏位置选择_在有利位置或牌面干燥时操作。_原子笔记索引.md", "type": "file", "size": 2061, "mtime": 1754344407000}, {"name": "埋伏后的心理战调整.md", "type": "file", "size": 777, "mtime": 1754220592000}, {"name": "埋伏时机.md", "type": "file", "size": 921, "mtime": 1754344274000}, {"name": "埋伏的执行要点.md", "type": "file", "size": 950, "mtime": 1754220599000}, {"name": "埋伏的执行要点_原子笔记索引.md", "type": "file", "size": 1739, "mtime": 1754344193000}, {"name": "基于概率和长期稳赢策略游戏.md", "type": "file", "size": 759, "mtime": 1754177011000}, {"name": "多埋伏的原因.md", "type": "file", "size": 893, "mtime": 1754218950000}, {"name": "大盲（BB）可以玩更宽的牌.md", "type": "file", "size": 1149, "mtime": 1754001035000}, {"name": "大盲（BB）和小盲（SB）的跟注成本低.md", "type": "file", "size": 1145, "mtime": 1754001030000}, {"name": "大盲位跟注成本低.md", "type": "file", "size": 811, "mtime": 1754177294000}, {"name": "对手加注时的应对策略.md", "type": "file", "size": 1331, "mtime": 1754177110000}, {"name": "对松凶玩家的策略：多埋伏，少跟注.md", "type": "file", "size": 3296, "mtime": 1754218915000}, {"name": "对紧弱玩家的偷盲策略.md", "type": "file", "size": 905, "mtime": 1754218954000}, {"name": "对紧弱玩家的策略：多偷盲，少诈唬.md", "type": "file", "size": 2830, "mtime": 1754218910000}, {"name": "对紧弱玩家的诈唬策略.md", "type": "file", "size": 1108, "mtime": 1754218954000}, {"name": "少跟注的理由.md", "type": "file", "size": 845, "mtime": 1754218950000}, {"name": "干燥牌面通常有以下特点：1. 没有明显的听牌可能：牌面上没有顺子或同花的可能性。2. 牌面结构简单：牌与牌之间没有联系。3. 对手不太可能继续下注：因为牌面没有给他们带来什么好牌的可能性。.md", "type": "file", "size": 970, "mtime": 1754344445000}, {"name": "平跟-对手加注后选择跟注而不是加注或弃牌.md", "type": "file", "size": 1241, "mtime": 1754000614000}, {"name": "庄位偷鸡的优势-对手防守弱：大小盲（SBBB）的牌通常很烂（比如72o、J3s），面对加注多数会直接弃牌.md", "type": "file", "size": 1235, "mtime": 1754001773000}, {"name": "庄位加注？他的范围很宽，可以用 更强的牌（JJ+、AQ+）反击.md", "type": "file", "size": 714, "mtime": 1754147934000}, {"name": "庄位加注时如何应对大小盲位置-- 别随便弃牌！否则会被庄位疯狂剥削。- 用稍好的牌防守（比如任意对子、A2o+、K8s+），跟注或反加。- 如果牌太烂（如72o、J3o），该弃就弃，别送钱.md", "type": "file", "size": 1120, "mtime": 1754002178000}, {"name": "庄位加注时的反击策略.md", "type": "file", "size": 1382, "mtime": 1754177110000}, {"name": "庄位加注的含义.md", "type": "file", "size": 1921, "mtime": 1754001705000}, {"name": "应对中间位置加注的策略-你的跟注或加注的牌也要更强（比如至少AQ+、JJ+）垃圾牌（如Q4o、K7s）直接弃掉，别送钱.md", "type": "file", "size": 1796, "mtime": 1754001882000}, {"name": "应对庄位加注的策略.md", "type": "file", "size": 1734, "mtime": 1754001923000}, {"name": "应对松凶玩家的正确做法.md", "type": "file", "size": 702, "mtime": 1754220584000}, {"name": "应对松凶玩家的正确做法_原子笔记索引.md", "type": "file", "size": 1545, "mtime": 1754341803000}, {"name": "应对松凶玩家的策略：弃掉边缘牌-避免陷入被动跟注的陷阱，同时减少对手对你手牌的猜测。.md", "type": "file", "size": 740, "mtime": 1754343911000}, {"name": "应对松凶玩家策略：弃掉边缘牌_原子笔记索引.md", "type": "file", "size": 2200, "mtime": 1754343909000}, {"name": "应对松凶玩家策略：用强牌埋伏反击.md", "type": "file", "size": 972, "mtime": 1754341815000}, {"name": "应对松凶玩家策略：避免陷入被动跟注.md", "type": "file", "size": 945, "mtime": 1754341815000}, {"name": "应对策略：对抗对手加注的具体行动.md", "type": "file", "size": 1424, "mtime": 1754177110000}, {"name": "弃掉边缘牌的好处-避免陷入被动跟注的陷阱，同时减少对手对你手牌的猜测.md", "type": "file", "size": 518, "mtime": 1754343889000}, {"name": "弃牌.md", "type": "file", "size": 1239, "mtime": 1754000232000}, {"name": "弃牌的重要性.md", "type": "file", "size": 668, "mtime": 1753952872000}, {"name": "强牌平跟策略.md", "type": "file", "size": 627, "mtime": 1754220590000}, {"name": "德州扑克中筹码量判断_Fold_Equity_和_All-in_风险.md", "type": "file", "size": 1515, "mtime": 1754177514000}, {"name": "德州扑克应基于概率和长期稳赢策略.md", "type": "file", "size": 840, "mtime": 1754176933000}, {"name": "德州扑克弃牌原因：赔率不合适.md", "type": "file", "size": 524, "mtime": 1754344347000}, {"name": "德州扑克弃牌策略.md", "type": "file", "size": 963, "mtime": 1754344271000}, {"name": "德州扑克心态不应依赖‘万一’.md", "type": "file", "size": 897, "mtime": 1754176933000}, {"name": "德州扑克的心态问题.md", "type": "file", "size": 685, "mtime": 1753957955000}, {"name": "德州扑克的教训-位置差时应该谨慎操作.md", "type": "file", "size": 676, "mtime": 1753958685000}, {"name": "德州扑克跟注条件：明确领先的牌.md", "type": "file", "size": 1021, "mtime": 1754344371000}, {"name": "德州扑克跟注策略例如，持有同花听牌或顺子听牌，且赔率合适时可以考虑跟注。.md", "type": "file", "size": 672, "mtime": 1754344434000}, {"name": "心理战：偶尔用埋伏反击后，松凶玩家可能会对你收敛，这时可以适当调整策略，增加偷盲和诈唬频率。.md", "type": "file", "size": 580, "mtime": 1754344181000}, {"name": "按钮位（BTN）最后一个行动的位置，通常被认为是位置最好的位置.md", "type": "file", "size": 1316, "mtime": 1754000622000}, {"name": "搭配使用.md", "type": "file", "size": 617, "mtime": 1754177107000}, {"name": "数学概率-长期来看，烂牌翻牌成牌的概率低，即使成牌也可能被更好的牌反杀.md", "type": "file", "size": 736, "mtime": 1753958642000}, {"name": "无脑跟注的风险-导致玩家在后期牌局中陷入不利，最终输光筹码.md", "type": "file", "size": 670, "mtime": 1753958753000}, {"name": "有利位置包括庄位、按钮位等靠后位置，以及能更好地观察对手动作和掌握主动权的位置.md", "type": "file", "size": 602, "mtime": 1754344522000}, {"name": "有利位置和牌面干燥的埋伏效果能获取更多信息、更容易获胜、引诱对手继续下注以及控制底池大小。.md", "type": "file", "size": 673, "mtime": 1754344468000}, {"name": "有利位置的定义.md", "type": "file", "size": 846, "mtime": 1754344274000}, {"name": "松凶玩家下注习惯.md", "type": "file", "size": 618, "mtime": 1754220591000}, {"name": "松凶玩家的特点.md", "type": "file", "size": 842, "mtime": 1754218950000}, {"name": "松凶玩家跟注易被剥削.md", "type": "file", "size": 650, "mtime": 1754220585000}, {"name": "枪口位（UTG）必须玩超强牌.md", "type": "file", "size": 1148, "mtime": 1754001036000}, {"name": "正确策略：弃牌.md", "type": "file", "size": 649, "mtime": 1753957955000}, {"name": "烂牌的定义及风险-是翻牌后不易成牌或成牌概率很低的牌，如小杂牌、边缘同花连张等.md", "type": "file", "size": 885, "mtime": 1753958422000}, {"name": "烂牌的定义和影响.md", "type": "file", "size": 695, "mtime": 1753952873000}, {"name": "牌局分析：对手加注时可能的牌型.md", "type": "file", "size": 1432, "mtime": 1754177110000}, {"name": "牌面干燥的定义-公共牌面没有明显的听牌可能，牌面结构简单，不太可能帮助对手提升牌力，对手持续下注的可能性较低。.md", "type": "file", "size": 1916, "mtime": 1754344407000}, {"name": "由于牌面干燥，对手持续下注的可能性会降低，因为他们知道很难通过下注来迫使你弃牌，也很难通过后续发牌来改善自己的牌力。.md", "type": "file", "size": 632, "mtime": 1754344610000}, {"name": "盲注位的“强制投资”.md", "type": "file", "size": 1133, "mtime": 1754001030000}, {"name": "盲注位的“门票”理论.md", "type": "file", "size": 1145, "mtime": 1754001038000}, {"name": "等待好牌的策略.md", "type": "file", "size": 665, "mtime": 1753952877000}, {"name": "紧凶（TAG）.md", "type": "file", "size": 1272, "mtime": 1754000222000}, {"name": "紧弱玩家偷盲加注范围.md", "type": "file", "size": 909, "mtime": 1754220589000}, {"name": "紧弱玩家反抗时的应对策略.md", "type": "file", "size": 664, "mtime": 1754220595000}, {"name": "紧弱玩家在盲注位的行为特点.md", "type": "file", "size": 718, "mtime": 1754220594000}, {"name": "紧弱玩家抢盲注策略.md", "type": "file", "size": 664, "mtime": 1754220595000}, {"name": "紧弱玩家的大牌识别.md", "type": "file", "size": 1046, "mtime": 1754220587000}, {"name": "紧弱玩家的特点.md", "type": "file", "size": 818, "mtime": 1754218954000}, {"name": "紧弱玩家的策略总结.md", "type": "file", "size": 861, "mtime": 1754220573000}, {"name": "紧弱玩家的诈唬应对.md", "type": "file", "size": 1044, "mtime": 1754220586000}, {"name": "紧弱玩家跟注或加注时的牌力推测.md", "type": "file", "size": 732, "mtime": 1754220577000}, {"name": "翻牌后的形势分析-翻牌后，玩家应分析形势，意识到对手的牌可能比自己更强，如对手持有KQ，已经是两对，而自己仅有顶对.md", "type": "file", "size": 720, "mtime": 1753957948000}, {"name": "翻牌后的牌力评估.md", "type": "file", "size": 731, "mtime": 1753952870000}, {"name": "翻牌是Q♠8♦2♣，这就是典型的干燥牌面。它既没有同花听牌的可能（花色不统一），也没有明显的顺子听牌（牌面数字间隔太大），对手很难在这个牌面上提升牌力.md", "type": "file", "size": 2373, "mtime": 1754344548000}, {"name": "翻译与具体意义.md", "type": "file", "size": 809, "mtime": 1754177107000}, {"name": "胜率.md", "type": "file", "size": 1266, "mtime": 1754000229000}, {"name": "计算技能冷却时间与对手行动频率对应.md", "type": "file", "size": 1482, "mtime": 1754177460000}, {"name": "赔率变好，跟注成本低.md", "type": "file", "size": 1249, "mtime": 1754001032000}, {"name": "跟注与弃牌策略 只跟注有发展潜力的听牌或明确领先的牌，其他情况果断弃牌.md", "type": "file", "size": 2035, "mtime": 1754344238000}, {"name": "跟注与弃牌策略 只跟注有发展潜力的听牌或明确领先的牌，其他情况果断弃牌_原子笔记索引.md", "type": "file", "size": 1935, "mtime": 1754344431000}, {"name": "跟注成本低的优势.md", "type": "file", "size": 685, "mtime": 1754177294000}, {"name": "跟注的时机与策略-位置劣势时，使用边缘牌（如K9）跟注职业玩家的加注，通常不是明智的选择，因为职业玩家能更灵活地操作.md", "type": "file", "size": 784, "mtime": 1753958493000}, {"name": "跟注策略.md", "type": "file", "size": 662, "mtime": 1754220592000}, {"name": "跟注策略的误区.md", "type": "file", "size": 745, "mtime": 1753952870000}, {"name": "边缘牌的定义不太可能赢得底池但也不太可能输光的牌。.md", "type": "file", "size": 556, "mtime": 1754343888000}, {"name": "避免“万一”心态.md", "type": "file", "size": 719, "mtime": 1753952876000}, {"name": "避免依赖‘万一’心态.md", "type": "file", "size": 762, "mtime": 1754177011000}, {"name": "长期视角下的策略选择.md", "type": "file", "size": 719, "mtime": 1753952875000}]}, {"name": "🌞复盘页.components", "type": "file", "size": 7610, "mtime": 1754319127000}, {"name": "🍊学习页.components", "type": "file", "size": 27420, "mtime": 1754341589000}, {"name": "德州笔记同步", "type": "directory", "size": 1272180, "mtime": 1754298654000, "children": [{"name": "学习笔记", "type": "directory", "size": 16, "mtime": 1754298654000, "children": [{"name": "SyncToy_6729af83-560a-418e-affc-28276e26f7c8.dat", "type": "file", "size": 16, "mtime": 1754178629000}]}, {"name": "教学法", "type": "directory", "size": 1245863, "mtime": 1754298657000, "children": [{"name": "教学法—“3-bet”是一个特定的术语.md", "type": "file", "size": 11434, "mtime": 1754179728000}, {"name": "教学法—「美剧学英语方法论」.md", "type": "file", "size": 22807, "mtime": 1754179717000}, {"name": "教学法—1️⃣天赋与训练他的系统具备自指性（观察行为会改变系统状态）动态密码系统下的「武术降维打击」.md", "type": "file", "size": 170145, "mtime": 1750977541000}, {"name": "教学法—不反弹—阶梯减肥法的科学拆解.md", "type": "file", "size": 76373, "mtime": 1749417829000}, {"name": "教学法—中医或传统养生角度提到的内部循环打通，确实能解释瑜伽或太极练习带来的身体变化.md", "type": "file", "size": 20689, "mtime": 1754179727000}, {"name": "教学法—为什么维持正常体重很难？.md", "type": "file", "size": 50658, "mtime": 1750975444000}, {"name": "教学法—主食减肥法.md", "type": "file", "size": 41739, "mtime": 1749291485000}, {"name": "教学法—从“规则树”到“动态模型”的进化职业牌手的秘密：他们大脑中运行的不是if-then规则，而是一个实时计算的决策流处理器.md", "type": "file", "size": 84373, "mtime": 1754179733000}, {"name": "教学法—佐助的战斗计算能力 → 德州扑克的“精准决策”佐助在战斗中计算“伊邪那岐的冷却时间”和“团藏的剩余写轮眼数量”，本质上是一种动态资源管理与概率博弈。.md", "type": "file", "size": 4753, "mtime": 1754179709000}, {"name": "教学法—你的“扑克瘦子习惯”——如何用同样的逻辑在牌桌上建立不败系统.md", "type": "file", "size": 20708, "mtime": 1754179718000}, {"name": "教学法—关键原则：从“孤立训练”到“整合应用.md", "type": "file", "size": 29612, "mtime": 1754179736000}, {"name": "教学法—卡通卡片就是关于火影.md", "type": "file", "size": 73631, "mtime": 1754179716000}, {"name": "教学法—德州扑克训练计划.md", "type": "file", "size": 29064, "mtime": 1754179718000}, {"name": "教学法—德州扑克里的“趴草丛”练什么？.md", "type": "file", "size": 3044, "mtime": 1754179706000}, {"name": "教学法——扑克决策分层——听音乐.md", "type": "file", "size": 16769, "mtime": 1749291497000}, {"name": "教学法—最小阻力路径”实现减肥——也就是找到一种最省力、最不消耗意志力、且能融入现有生活的方式.md", "type": "file", "size": 122082, "mtime": 1754179721000}, {"name": "教学法—有效诈唬≈有效化妆：比如干燥面对Nit下注≈用阴影修饰圆脸——精准有效.md", "type": "file", "size": 15931, "mtime": 1754179721000}, {"name": "教学法—梭哈（All-in）是指玩家将手中全部的筹码一次性押入底池的行为.md", "type": "file", "size": 182572, "mtime": 1754179719000}, {"name": "教学法—用「腰围指标」保护你的扑克训练成果？——理性恋爱防沉迷系统.md", "type": "file", "size": 65024, "mtime": 1749291471000}, {"name": "教学法—结合Albert提出的核心问题和解决方法建立英语专用回路构建GTO决策的「数学反射通道」.md", "type": "file", "size": 38316, "mtime": 1754179730000}, {"name": "教学法—结合心理学和正念饮食（Mindful Eating）的非传统减肥方法.md", "type": "file", "size": 119726, "mtime": 1754179720000}, {"name": "教学法—规则理解、策略应用、心理战术、实战技巧、术语掌握 这五个维度.md", "type": "file", "size": 14700, "mtime": 1754179709000}, {"name": "教学法—逐句对照的方式学习德州扑克思维，可以借鉴美剧.md", "type": "file", "size": 31713, "mtime": 1749355237000}]}, {"name": "训练笔记", "type": "directory", "size": 26301, "mtime": 1754298744000, "children": [{"name": "Obsidian看视频插件.md", "type": "file", "size": 13359, "mtime": 1754179629000}, {"name": "新手期训练计划.md", "type": "file", "size": 12942, "mtime": 1754179765000}]}]}, {"name": "🏢教学页.components", "type": "file", "size": 13867, "mtime": 1754179674000}, {"name": "概率", "type": "directory", "size": 38329, "mtime": 1754298653000, "children": [{"name": "概率训练.md", "type": "file", "size": 38223, "mtime": 1734691270000}, {"name": "概率论.md", "type": "file", "size": 106, "mtime": 1746399921000}]}, {"name": "笔记", "type": "directory", "size": 58389, "mtime": 1754308887000, "children": [{"name": "inbox笔记", "type": "directory", "size": 58389, "mtime": 1754308887000, "children": [{"name": "✅ 中间位置加注 = “我有好牌，但不是超级强”（比如TT+、AJ+、偶尔99或同花连张）  🚫 不要以为他在诈唬，他大概率真有货！  🃏 你的应对策略：要么拿更强的牌干他，要么弃牌，别头铁！.md", "type": "file", "size": 463, "mtime": 1754177938000}, {"name": "为什么大盲（BB）和小盲（SB）可以玩更宽的牌？大盲（BB） 已经放了钱，跟注成本低，可以玩更宽的牌（比如同花连张、小对子）.md", "type": "file", "size": 3158, "mtime": 1754000956000}, {"name": "位置差还乱跟注.md", "type": "file", "size": 6084, "mtime": 1753952986000}, {"name": "你坐在 “按钮位”（最后一个行动，位置最好），用 T♠9♠（黑桃10和黑桃9）加注开池。  这时，一个 “紧凶”（玩得紧但很凶）的对手在 “关煞位”（CO位）对你 3bet（再加注）.md", "type": "file", "size": 10949, "mtime": 1754000627000}, {"name": "偶尔玩一些中等牌：99、88、JTs（同花J10）、QJs（同花QJ）.md", "type": "file", "size": 459, "mtime": 1754148133000}, {"name": "如果一个人拿到 AA、KK、AK 这种顶级牌，他通常会：        - 在更早的位置（如枪口位UTG）直接加注，因为不想错过价值。            - 在中间位置（MP）可能会选择“平跟”埋伏，希望后面有人加注，他再反加（3bet），这样能赚更多钱。.md", "type": "file", "size": 125, "mtime": 1754148310000}, {"name": "时候该Check、什么时候该直接弃牌了吧？ 😎.md", "type": "file", "size": 34673, "mtime": 1753956011000}, {"name": "枪口位（UTG）✅ 只玩顶级牌：比如 AA、KK、QQ、AK、AQ。  ❌ 扔掉垃圾牌：比如 Q4o、K3o、J7s、73s.md", "type": "file", "size": 1891, "mtime": 1754177707000}, {"name": "简单记：- “中间位置加注 ≈ 我有A，但不是王炸”    - “枪口位加注 ≈ 我有王炸，别惹我”    - “庄位加注 ≈ 我可能偷鸡，小心点”.md", "type": "file", "size": 368, "mtime": 1754148422000}, {"name": "高对：TT（10 10）、JJ、QQ、KK、AA强高张：AK、AQ、AJ<PERSON>（同花AJ）.md", "type": "file", "size": 219, "mtime": 1754148081000}]}]}]}