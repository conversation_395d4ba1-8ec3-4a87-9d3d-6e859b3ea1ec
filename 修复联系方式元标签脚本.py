#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复联系方式元标签脚本
将联系方式信息作为元标签放在YAML front matter中
"""

import os
import re
from pathlib import Path

# 联系方式数据
contact_data = {
    "国家网信办.md": {
        "title": "国家互联网信息办公室",
        "province": "国家级",
        "address": "北京市西城区车公庄大街11号",
        "phone_assessment": "010-55627135",
        "phone_contract": "010-55627565", 
        "phone_certification": "010-82261100"
    },
    "北京网信办.md": {
        "title": "北京市互联网信息办公室",
        "province": "北京",
        "address": "北京市朝阳区华威南路弘善家园413号",
        "phone": "010-67676912"
    },
    "天津网信办.md": {
        "title": "天津市互联网信息办公室",
        "province": "天津", 
        "address": "天津市河西区梅江道20号",
        "phone": "022-88355322"
    },
    "上海网信办.md": {
        "title": "上海市互联网信息办公室",
        "province": "上海",
        "address": "上海市徐汇区宛平路315号", 
        "phone": "021-64743030-2711"
    },
    "重庆网信办.md": {
        "title": "重庆市互联网信息办公室",
        "province": "重庆",
        "address": "重庆市渝北区青竹东路6号",
        "phone": "023-63151805"
    },
    "河北网信办.md": {
        "title": "河北省互联网信息办公室",
        "province": "河北",
        "address": "河北省石家庄市桥西区维明南大街79号",
        "phone": "0311-87909716"
    },
    "河南网信办.md": {
        "title": "河南省互联网信息办公室",
        "province": "河南",
        "address": "河南省郑州市金水区金水路16号",
        "phone": "0371-65901067"
    },
    "浙江网信办.md": {
        "title": "浙江省互联网信息办公室",
        "province": "浙江",
        "address": "浙江省杭州市西湖区省府路29号",
        "phone": "0571-81051250"
    },
    "江苏网信办.md": {
        "title": "江苏省互联网信息办公室",
        "province": "江苏",
        "address": "江苏省南京市建邺区白龙江东街8号",
        "phone": "025-63090194"
    },
    "福建网信办.md": {
        "title": "福建省互联网信息办公室",
        "province": "福建",
        "address": "福建省福州市鼓楼区北大路133号",
        "phone": "0591-86300613"
    },
    "安徽网信办.md": {
        "title": "安徽省互联网信息办公室",
        "province": "安徽",
        "address": "安徽省合肥市包河区中山路1号",
        "phone": "0551-62606014"
    },
    "贵州网信办.md": {
        "title": "贵州省互联网信息办公室",
        "province": "贵州",
        "address": "贵州省贵阳市云岩区宝山北路39号",
        "phone": "0851-82995001 / 0851-82995061"
    },
    "山东网信办.md": {
        "title": "山东省互联网信息办公室",
        "province": "山东",
        "address": "山东省济南市市中区经十路20637号",
        "phone": "0531-51773249 / 0531-51771297"
    },
    "广东网信办.md": {
        "title": "广东省互联网信息办公室",
        "province": "广东",
        "address": "广东省广州市越秀区中山一路104号",
        "phone": "020-87100794 / 020-87100793"
    },
    "陕西网信办.md": {
        "title": "陕西省互联网信息办公室",
        "province": "陕西",
        "address": "陕西省西安市雁塔区雁塔路南段10号",
        "phone": "029-63907136"
    },
    "甘肃网信办.md": {
        "title": "甘肃省互联网信息办公室",
        "province": "甘肃",
        "address": "甘肃省兰州市城关区南昌路1648号",
        "phone": "0931-8928721"
    },
    "山西网信办.md": {
        "title": "山西省互联网信息办公室",
        "province": "山西",
        "address": "山西省太原市迎泽区五一路36号",
        "phone": "0351-5236020"
    },
    "江西网信办.md": {
        "title": "江西省互联网信息办公室",
        "province": "江西",
        "address": "江西省南昌市红谷滩区卧龙路999号",
        "phone": "0791-88912737"
    },
    "云南网信办.md": {
        "title": "云南省互联网信息办公室",
        "province": "云南",
        "address": "云南省昆明市西山区日新中路516号",
        "phone": "0871-63902424"
    },
    "湖北网信办.md": {
        "title": "湖北省互联网信息办公室",
        "province": "湖北",
        "address": "湖北省武汉市武昌区水果湖路268号",
        "phone": "027-87231397"
    },
    "湖南网信办.md": {
        "title": "湖南省互联网信息办公室",
        "province": "湖南",
        "address": "湖南省长沙市芙蓉区韶山北路1号",
        "phone": "0731-81121089"
    },
    "青海网信办.md": {
        "title": "青海省互联网信息办公室",
        "province": "青海",
        "address": "青海省西宁市海湖新区文景街32号",
        "phone": "0971-8485510"
    },
    "辽宁网信办.md": {
        "title": "辽宁省互联网信息办公室",
        "province": "辽宁",
        "address": "辽宁省沈阳市和平区光荣街26号甲",
        "phone": "024-81680082"
    },
    "吉林网信办.md": {
        "title": "吉林省互联网信息办公室",
        "province": "吉林",
        "address": "吉林省长春市朝阳区新发路666号",
        "phone": "0431-82761087"
    },
    "黑龙江网信办.md": {
        "title": "黑龙江省互联网信息办公室",
        "province": "黑龙江",
        "address": "黑龙江省哈尔滨市南岗区华山路12号",
        "phone": "0451-58685723"
    },
    "海南网信办.md": {
        "title": "海南省互联网信息办公室",
        "province": "海南",
        "address": "海南省海口市国兴大道69号",
        "phone": "0898-65380723"
    },
    "四川网信办.md": {
        "title": "四川省互联网信息办公室",
        "province": "四川",
        "address": "四川省成都市青羊区桂花巷21号",
        "phone": "028-86601862"
    },
    "广西网信办.md": {
        "title": "广西壮族自治区互联网信息办公室",
        "province": "广西",
        "address": "广西壮族自治区南宁市青秀区民族大道112号",
        "phone": "0771-2093017 / 0771-2093049"
    },
    "宁夏网信办.md": {
        "title": "宁夏回族自治区互联网信息办公室",
        "province": "宁夏",
        "address": "宁夏回族自治区银川市金风区康平路1号",
        "phone": "0951-6668938"
    },
    "西藏网信办.md": {
        "title": "西藏自治区互联网信息办公室",
        "province": "西藏",
        "address": "西藏自治区拉萨市城关区农科路7号",
        "phone": "0891-6591509"
    },
    "内蒙古网信办.md": {
        "title": "内蒙古自治区互联网信息办公室",
        "province": "内蒙古",
        "address": "内蒙古自治区呼和浩特市赛罕区银河南街8号",
        "phone": "0471-4821277"
    },
    "新疆网信办.md": {
        "title": "新疆维吾尔自治区互联网信息办公室",
        "province": "新疆",
        "address": "新疆维吾尔自治区乌鲁木齐市新市区西环北路2221号",
        "phone": "0991-2384855"
    },
    "新疆兵团网信办.md": {
        "title": "新疆生产建设兵团互联网信息办公室",
        "province": "新疆兵团",
        "address": "新疆维吾尔自治区乌鲁木齐市天山区中山路462号",
        "phone": "0991-2899091"
    }
}

def generate_contact_with_meta(filename, data):
    """生成带元标签的联系方式内容"""
    content = f"# {data['title']}\n\n---\n"
    content += f"province: {data['province']}\n"
    content += f"address: {data['address']}\n"
    
    if filename == "国家网信办.md":
        # 国家网信办有多个电话
        content += f"phone_assessment: {data['phone_assessment']}\n"
        content += f"phone_contract: {data['phone_contract']}\n"
        content += f"phone_certification: {data['phone_certification']}\n"
    else:
        # 其他网信办只有一个电话
        content += f"phone: {data['phone']}\n"
    
    content += "---\n"
    
    return content

def main():
    """主函数"""
    print("开始修复联系方式元标签...")
    
    output_dir = Path('数据出境合规实务50问汇总')
    
    # 处理每个联系方式文件
    for filename, data in contact_data.items():
        file_path = output_dir / filename
        
        if file_path.exists():
            content = generate_contact_with_meta(filename, data)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"修复文件: {filename}")
        else:
            print(f"文件不存在: {filename}")
    
    # 处理环球律师事务所
    lawyer_file = output_dir / "环球律师事务所.md"
    if lawyer_file.exists():
        lawyer_content = """# 环球律师事务所

---
email: <EMAIL>
---
"""
        with open(lawyer_file, 'w', encoding='utf-8') as f:
            f.write(lawyer_content)
        print("修复文件: 环球律师事务所.md")
    
    print(f"\n修复完成！共处理 {len(contact_data) + 1} 个文件")

if __name__ == "__main__":
    main()
