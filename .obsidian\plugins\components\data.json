{"createComponentSilent": false, "receiveBetaVersion": true, "folder": "resource/components/views", "formFolder": "components/form", "scriptFolder": "02-Areas/003-项目/Obsidian Components/scripts", "enableDebug": false, "debugLogOutputFolder": "components/logs", "fileEntrypoints": {}, "components": [{"id": "1b69abb8-4ba2-4059-8e46-4fdab10ab1a2", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-23T15:56:59.128Z", "updateAt": "2024-04-23T15:56:59.128Z", "backgroundStyle": "card", "contentPrefix": "", "contentSuffix": "", "countType": "number", "query": {"valueType": "query", "value": 100, "filter": {"id": "006ba832-1b9d-480a-a81a-43570e3d61e7", "type": "group", "operator": "and", "conditions": []}}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "d9ecfa86-556a-4775-9558-fedf8c26919c", "type": "group", "operator": "and", "conditions": []}}}, {"id": "b84440af-3f53-4d1a-8b5d-0b33ff5ce763", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-21T11:28:58.233Z", "updateAt": "2024-04-21T12:29:38.958Z", "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"id": "b38178bf-d546-47c7-9127-f571852ff5f6", "type": "group", "operator": "and", "conditions": [{"id": "0c0a3976-8a37-40b0-9b60-ba41501871ec", "type": "filter", "operator": "contains", "property": "readingStatus", "value": "在读", "conditions": []}, {"id": "88bd1bd3-cf17-432b-b9c9-290eb2c875ab", "type": "group", "operator": "or", "conditions": [{"id": "2e398a57-1c69-4ab3-a94b-165d3ccd8b4c", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "", "conditions": []}]}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [], "templates": [], "groups": [], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-medium", "showPropertyName": false, "cover": {"type": "pageFirstImage", "value": "", "fit": "contains"}}}, {"id": "95c98ca3-0c22-447b-9057-3d8583b97161", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "Components", "maxWidthRatio": -1, "createAt": "2024-04-21T07:34:06.576Z", "updateAt": "2024-04-21T10:33:24.918Z", "backgroundStyle": "none", "viewType": "table", "datasource": {"filter": {"id": "b38178bf-d546-47c7-9127-f571852ff5f6", "type": "group", "operator": "and", "conditions": [{"id": "9ff40be7-e776-4c7a-8bd1-fa0d08b51de1", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian Components]]", "conditions": []}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [{"id": "d49ca357-489b-4fa5-aa37-e26ea5c51646", "name": "tags", "isShow": true, "defaultValue": [], "type": "multiSelect", "alias": ""}, {"id": "50271a48-bd47-42c9-ac1b-7e6e40130851", "name": "status", "isShow": true, "defaultValue": "", "type": "select", "alias": ""}, {"id": "891614c5-d078-47bb-90ae-bfd51a0cd814", "name": "type", "isShow": true, "defaultValue": "", "type": "select", "alias": ""}, {"id": "6ac8706d-1881-45e9-8086-8af90c897fb3", "name": "createTime", "isShow": true, "defaultValue": "", "type": "datetime", "alias": ""}, {"id": "c477a9ad-5d16-4330-9b4c-d7e0b4cbb6a2", "name": "doneTime", "isShow": true, "defaultValue": "", "type": "datetime", "alias": ""}, {"id": "5e46f842-faf8-432b-b3d9-edf8356ec1e9", "name": "完成", "isShow": true, "defaultValue": "", "type": "button", "options": {"name": "完成", "action": {"type": "updateProperty", "properties": [{"id": "fe6a3582-97b3-4378-8dcb-6b1a683947df", "name": "doneTime", "type": "datetime", "value": "$now"}, {"id": "c996bb40-729d-4c97-9de9-276b1ae36496", "name": "status", "type": "string", "value": "DONE"}]}}}], "templates": [], "groups": [], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-medium", "showPropertyName": false, "cover": {"type": "pageFirstImage", "value": "", "fit": "contains"}, "headColumnWidth": "247"}}, {"id": "23e52e9b-e3d1-4233-b06a-fb94355ae0db", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-21T06:16:38.764Z", "updateAt": "2024-04-23T15:56:59.130Z", "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "b84440af-3f53-4d1a-8b5d-0b33ff5ce763"}, {"componentId": "1b69abb8-4ba2-4059-8e46-4fdab10ab1a2"}], "layoutType": "column", "remark": ""}, {"id": "a60095a2-5a9f-492c-bdf8-4b890c1d7c2b", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-19T13:12:14.214Z", "updateAt": "2024-04-19T13:14:01.990Z", "backgroundStyle": "card", "contentPrefix": "", "contentSuffix": "", "countType": "number", "query": {"valueType": "query", "value": 100, "filter": {"id": "1c13316e-6047-4f43-b7c7-42ddec98e95d", "type": "group", "operator": "and", "conditions": [{"id": "fb75c647-2c33-4594-9168-006468014157", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "94744a77-e554-46ab-b1b8-3d8b86b35210", "type": "filter", "operator": "equals", "property": "status", "value": "DONE", "conditions": []}, {"id": "2fb0955c-5e8d-4f60-8cfe-e9f4f246f827", "type": "filter", "operator": "time_after", "property": "doneTime", "value": "$startOfToday", "conditions": []}, {"id": "d9c03b29-67e7-420b-8546-8131478f01f4", "type": "filter", "operator": "time_before", "property": "doneTime", "value": "$endOfToday", "conditions": []}, {"id": "a6a9135f-6ab1-4fd0-a3ba-e41aae8c5668", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian]]", "conditions": []}]}}, "totalQuery": {"valueType": "constant", "value": 100, "filter": {"id": "99f83226-6519-4ccb-908f-49b1f3dcd242", "type": "group", "operator": "and", "conditions": []}}, "title": "今天完成项目"}, {"id": "1bb20e34-ea12-4f90-8cc0-e0679526c5b4", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-12T13:09:31.906Z", "updateAt": "2024-04-18T09:44:50.816Z", "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "85fd4f09-ea28-4e9f-9d96-80b189286a2f", "type": "filter", "operator": "contains", "property": "tags", "value": "resource", "conditions": []}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [{"id": "e7b9c6b4-6ecd-4c81-a9da-028285f3d9b7", "name": "Delete", "isShow": true, "defaultValue": "", "type": "button", "options": {"name": "删除", "action": {"type": "deleteFile", "properties": []}}}], "templates": [], "viewOptions": {"openPageIn": "tab", "itemSize": "gallary-small", "cover": {"type": "pageFirstImage", "value": "", "fit": "contains"}}}, {"id": "9aeab79e-e2c1-4e73-99be-aa01b106da76", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "Backlog", "maxWidthRatio": -1, "createAt": "2024-04-12T04:42:27.191Z", "updateAt": "2024-04-18T12:44:54.120Z", "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "e55ca4eb-9054-421b-89e7-9692a0d130d7", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "e715a91c-8baf-41e4-8bae-6d4ef1ce1a15", "type": "filter", "operator": "contains", "property": "status", "value": "BACKLOG", "conditions": []}], "operator": "and"}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": [{"id": "e6e02432-9851-4a7a-a382-9392a8ef5897", "property": "createTime", "direction": "desc"}]}}, "properties": [{"id": "d9b617d9-f45e-479d-a70e-9ff08e13bf91", "name": "area", "isShow": true, "defaultValue": "", "type": "link"}, {"id": "37d87244-306b-4ea5-b232-701593460e30", "name": "status", "isShow": true, "defaultValue": "", "type": "select"}, {"id": "0328f67b-3ecd-4880-b09f-65690dc882ae", "name": "createTime", "isShow": false, "defaultValue": "", "type": "datetime"}, {"id": "db806894-a965-414b-b6ec-f5d1a32cb209", "name": "完成", "isShow": true, "defaultValue": "", "type": "button", "options": {"name": "完成", "action": {"type": "updateProperty", "properties": [{"id": "37f27d93-e35d-4f23-bec4-a274aec987cd", "name": "doneTime", "type": "datetime", "value": "$now"}, {"id": "41002b29-1676-4105-8cde-4fd85f912fc9", "name": "status", "type": "string", "value": "DONE"}, {"id": "41fd9c51-c4e5-41d9-bf19-40e2cf511a0e", "name": "archived", "type": "boolean", "value": true}]}}}], "templates": [], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-small", "showPropertyName": false, "cover": {"type": "none", "value": "", "fit": "contains"}}}, {"id": "e739ac4c-4321-49c7-9045-da2e09255b78", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-12T04:42:23.437Z", "updateAt": "2024-04-17T07:01:57.329Z", "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "9aeab79e-e2c1-4e73-99be-aa01b106da76"}], "layoutType": "tab", "remark": ""}, {"id": "e5ee28c0-3687-4d41-a358-76c1d10f806c", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-04-10T15:16:53.599Z", "createAt": "2024-03-28T08:18:10.013Z", "remark": "打卡", "components": [{"componentId": "942d8003-dda0-46c9-96d3-2716bcd85ddc"}]}, {"id": "92697fb2-9e0c-43ed-b21b-51c8dea41ed5", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "tab", "updateAt": "2024-04-11T13:14:19.344Z", "createAt": "2024-03-26T13:40:06.327Z", "remark": "", "components": [{"componentId": "8e11ac32-1da2-432a-b04f-de89ef111771"}, {"componentId": "e5a6c4d3-46ec-4c82-8fe3-3b0ff60386ef"}, {"componentId": "d067b02e-8e91-4b94-9094-b282a6ef27e8"}]}, {"id": "8e11ac32-1da2-432a-b04f-de89ef111771", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "我来取一个非常长的名字看看会发生什么样的事情我来取一个非常长的名字看看会发生什么样的事情", "maxWidthRatio": 100, "backgroundStyle": "none", "viewType": "table", "datasource": {"filter": {"conditions": [{"value": "#sample", "operator": "contains", "property": "${file.tags}", "id": "3b3d76a3-882a-427d-ac9a-8e3a667059a7"}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [{"id": "4993b77d-2463-4edb-8cf8-098b72ca5bbd", "name": "tags", "isShow": true, "defaultValue": "", "type": "multiSelect", "alias": "", "options": {"width": "166", "formula": "duration(prop('createTime'), prop('doneTime')).as('days').toFixed(2)+\"天\""}}, {"id": "13308c6d-14a0-47f8-b7a7-b851e98c0cc2", "name": "progress", "isShow": true, "defaultValue": "", "type": "number", "alias": "", "options": {"prefix": "加个前缀", "suffix": "加个后缀", "color": "components--color-orange", "uiType": "progressBar", "total": "100", "width": "179", "formula": "\"镜头：\"+prop('shot')"}}, {"id": "7c003b4a-7814-464c-8f8b-85731da1c81a", "name": "cover", "isShow": true, "defaultValue": "", "type": "number", "alias": "", "options": {"prefix": "", "suffix": "", "color": "components--color-theme", "uiType": "progressRing", "total": 100, "width": "114", "formula": "\"镜头：\"+prop('shot')"}}, {"id": "f238b0c6-59e7-4a7e-92ab-40af8445e033", "name": "button2", "isShow": true, "defaultValue": "", "type": "button", "options": {"name": "Click", "action": {"type": "updateProperty", "properties": []}}}], "templates": [{"id": "ded23a05-fd2d-4520-985c-cc31c03ecb3d", "path": "system/template/project.md", "name": "project.md", "type": "normal"}, {"id": "4d5aa517-4f4b-4ea6-a56a-48b7f7b4f1d1", "path": "system/template/memo.md", "name": "memo.md", "type": "templater"}], "viewOptions": {"openPageIn": "current", "itemSize": "gallary-medium", "cover": {"type": "pageFirstImage", "value": "", "fit": "fill"}, "headColumnWidth": "188"}, "defaultTemplate": "4d5aa517-4f4b-4ea6-a56a-48b7f7b4f1d1", "updateAt": "2024-04-05T01:32:57.194Z"}, {"id": "174046c3-a216-490a-b813-209c69cfa2a0", "type": "multi", "titleAlign": "center", "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-04-10T15:11:54.407Z", "createAt": "2024-02-07T07:32:41.663Z", "remark": "领域", "components": [{"componentId": "76f789b9-b2ad-42aa-b354-0591e564a4b3", "widthRatio": 23.919191919191913}, {"componentId": "00a57244-98df-4965-999e-5f72125e0010", "widthRatio": 19.353535353535353}, {"componentId": "bd3ae31d-7596-43d6-83be-0f4af5c25b0c", "widthRatio": 20.727272727272723}, {"componentId": "6b775592-9da5-4c75-b4eb-817dec4cdeb9", "widthRatio": 18.545454545454543}, {"componentId": "d3cca672-0783-40ba-9ab9-357cc528660b", "widthRatio": 17.454545454545475}]}, {"id": "76f789b9-b2ad-42aa-b354-0591e564a4b3", "type": "count", "titleAlign": "center", "backgroundStyle": "none", "maxWidthRatio": 20, "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "9f9d2c9c-66ac-4fab-b6c1-046967043127", "type": "filter", "operator": "contains", "property": "tags", "value": "area", "conditions": []}]}, "type": "page"}, "title": "领域", "tabTitle": "领域", "backgroundColor": "hsl(var(--interactive-accent-hsl), 0.1)", "fontColor": "hsl(var(--interactive-accent-hsl), 1)", "picture": "", "countType": "number", "updateAt": "2024-04-18T09:29:48.508Z"}, {"id": "00a57244-98df-4965-999e-5f72125e0010", "type": "count", "titleAlign": "center", "backgroundStyle": "none", "maxWidthRatio": 20, "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "0942a130-0113-4004-bc30-7bb66b25b2b1", "type": "filter", "operator": "contains", "property": "tags", "value": "inbox", "conditions": []}]}, "type": "page"}, "title": "收件箱", "backgroundColor": "#F4DFEB", "fontColor": "#AD1A72", "tabTitle": "收件箱", "countType": "number", "updateAt": "2024-04-18T09:29:59.658Z"}, {"id": "bd3ae31d-7596-43d6-83be-0f4af5c25b0c", "type": "count", "titleAlign": "center", "backgroundStyle": "none", "maxWidthRatio": 23.664921465968586, "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "3962e693-3a9a-4b88-8c28-f74f74617559", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "79b2c039-fef1-4da6-ae59-c302dd45cd64", "type": "group", "operator": "or", "conditions": [{"id": "eead459f-284d-4063-b228-fee676aafb9a", "type": "filter", "operator": "contains", "property": "status", "value": "DOING", "conditions": []}, {"id": "42c8901d-4f2b-4b4e-8007-89c2083fa928", "type": "filter", "operator": "contains", "property": "status", "value": "TODO", "conditions": []}]}], "operator": "and"}, "type": "page"}, "title": "进行中", "backgroundColor": "#DDEBF1", "fontColor": "#0B6E99", "countType": "number", "updateAt": "2024-04-18T09:30:53.787Z"}, {"id": "6b775592-9da5-4c75-b4eb-817dec4cdeb9", "type": "count", "titleAlign": "center", "backgroundStyle": "none", "maxWidthRatio": 13.860551605356012, "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "31d04bce-a916-4c99-a197-a90e7bec1ade", "type": "filter", "operator": "contains", "property": "tags", "value": "journal", "conditions": []}], "operator": "and"}, "type": "page"}, "title": "日记", "backgroundColor": "#F4DFEB", "fontColor": "#AD1A72", "countType": "number", "updateAt": "2024-04-18T09:31:26.668Z"}, {"id": "d3cca672-0783-40ba-9ab9-357cc528660b", "type": "count", "titleAlign": "center", "backgroundStyle": "none", "maxWidthRatio": 22.474526928675402, "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "a7e7f8d7-bcd7-441c-875d-0b452a387bc8", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "1e08007d-3e6c-4657-8120-457c36e5226f", "type": "filter", "operator": "equals", "property": "archived", "value": true, "conditions": []}], "operator": "and"}, "type": "page"}, "title": "归档", "backgroundColor": "#E9E5E3", "fontColor": "#64473A", "countType": "number", "updateAt": "2024-04-18T09:38:51.661Z"}, {"type": "multi", "id": "f4f6d625-2ca2-4b1c-a239-47b23831f38e", "widgets": [], "backgroundStyle": "none", "layoutType": "column", "updateAt": "2024-04-09T01:07:26.719Z", "createAt": "2024-02-07T09:24:12.664Z", "remark": "首页倒计时", "components": [{"componentId": "bc9fcac5-0123-4cb4-98dd-85b5eced748e"}, {"componentId": "280e33bf-433d-4718-8819-2798cf2a5ead"}, {"componentId": "0b4229bf-7ff3-4c16-8f8d-cf68bbe5ef56"}, {"componentId": "8969245b-d305-4bfe-a66f-a2ddf16f4a24"}]}, {"id": "bc9fcac5-0123-4cb4-98dd-85b5eced748e", "type": "timing", "title": "🧑‍💻\nobsidian\n插件开发者", "timeTextPattern": "d", "startDateTime": "2023-12-10 09:23:00", "showStartDateTime": true, "backgroundStyle": "card", "maxWidthRatio": 25, "titleAlign": "center", "updateAt": "2024-04-09T01:07:19.332Z"}, {"id": "280e33bf-433d-4718-8819-2798cf2a5ead", "type": "countdown", "title": "😴 \n关机重启还有", "repeatType": "daily", "timeTextPattern": "Hms", "endHour": 23, "endMinute": 30, "endSecond": 0, "showEndDateTime": true, "backgroundStyle": "card", "maxWidthRatio": 24.14965986394558}, {"id": "0b4229bf-7ff3-4c16-8f8d-cf68bbe5ef56", "type": "dateProgress", "title": "地球 Online", "uiType": "ring", "timeTextPattern": "d", "startDateTime": "2000-01-01 00:00:00", "endDateTime": "2099-01-01 00:00:00", "showDateIndicator": false, "showProgressIndicator": true, "backgroundStyle": "card", "maxWidthRatio": 25.85034013605442, "updateAt": "2024-04-11T13:20:10.234Z"}, {"id": "8969245b-d305-4bfe-a66f-a2ddf16f4a24", "type": "clock", "titleAlign": "center", "maxWidthRatio": 25, "maxHeight": 300, "backgroundStyle": "card", "clockOptions": {"showTimeLabel": true, "dialType": "none", "showDateLabel": true}}, {"id": "1a5100dc-e027-4e73-a34f-2063903c2435", "type": "multi", "widgets": [], "backgroundStyle": "none", "layoutType": "column", "updateAt": "2024-03-24T10:54:24.442Z", "createAt": "2024-02-07T10:42:25.334Z", "remark": "名片", "components": [{"componentId": "ba4983c4-4c74-4715-aa0d-3efdf0b0199e"}, {"componentId": "3155618e-b680-4123-be66-3c87cc62af89"}, {"componentId": "49518dad-d941-4527-9868-e0fddd78c5e1"}]}, {"id": "ba4983c4-4c74-4715-aa0d-3efdf0b0199e", "type": "customCard", "title": "小红书 @vran", "picture": "resource/素材库/vran.jpg", "picturePos": "top", "pictureShape": "circle", "description": "🌟  Obsidian 插件开发者\n🌈  Contribution Widget\n☀️   Contribution Graph\n😀  Emojio", "showButton": true, "button": {"text": "关注", "action": {"url": "https://www.xiaohongshu.com/user/profile/5dc432130000000001000b59", "type": "url"}}, "backgroundColor": "#dc2b42ff", "fontColor": "#ffffffff", "maxWidthRatio": 34.***************}, {"id": "3155618e-b680-4123-be66-3c87cc62af89", "type": "customCard", "title": "@vran", "description": "", "pictureShape": "round", "picturePos": "left", "maxWidthRatio": 35.*************, "showButton": true, "button": {"text": "扫码加入公测", "action": {"type": "url", "url": "obsidian://open?vault=Vran&file=02-area%2FReview"}}, "backgroundColor": "#1772f6ff", "fontColor": "#fefefeff", "picture": "resource/素材库/vran-official-account.png", "updateAt": "2024-04-06T08:33:55.168Z"}, {"id": "49518dad-d941-4527-9868-e0fddd78c5e1", "type": "customCard", "title": "公众号@vran", "picture": "resource/素材库/vran.jpg", "picturePos": "top", "pictureShape": "round", "description": "🔥  打工人座右铭\n🔥  摸鱼不被发现\n🔥  努力一定被看见\n💥  Yeah!", "showButton": true, "button": {"text": "关注", "action": {"url": "https://mp.weixin.qq.com/s/G3tZajvK4v_rm7ZC1UcCsg", "type": "url"}}, "backgroundColor": "#1aad19ff", "fontColor": "#ffffffff", "maxWidthRatio": 30.***************}, {"id": "0eeda0c5-6805-4571-a482-01cdd40013b7", "type": "multi", "titleAlign": "center", "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-04-21T08:22:51.063Z", "createAt": "2024-02-09T10:11:09.546Z", "remark": "主页", "components": [{"componentId": "fad18096-38fa-4f81-a527-0060836e389f", "widthRatio": 46.***************}, {"componentId": "c447bd75-a37e-4908-b93d-f847a22179b1", "widthRatio": 33.545996029119785}, {"componentId": "da5efceb-ad8d-402e-9bbb-a753cbfcc469", "widthRatio": 20.06882859033755}]}, {"id": "fad18096-38fa-4f81-a527-0060836e389f", "type": "quote", "titleAlign": "center", "backgroundStyle": "none", "maxWidthRatio": 47.6906552094522, "contentType": "block", "coverType": "pageFirstImage", "filter": {"conditions": [{"type": "file_path", "value": "阅读", "operator": "contains", "property": "${file.path}", "id": "5126dc51-a4eb-4b40-a03f-5220a13fa26b"}, {"type": "property", "value": "1", "operator": "greater_than_or_equal", "property": "noteCount", "id": "4bf1ed76-3310-4e32-bf46-4c1db05558b8"}], "operator": "and"}, "maxHeight": 200, "updateAt": "2024-04-18T09:39:02.065Z"}, {"id": "c447bd75-a37e-4908-b93d-f847a22179b1", "type": "chart", "titleAlign": "center", "chartType": "bar", "backgroundStyle": "none", "maxWidthRatio": 37.85356247762263, "maxHeight": 300, "labelProperty": "createTime", "labelFormat": "$toDate", "valueProperty": "$file_count", "filter": {"conditions": [{"type": "property", "value": "$startOfQuarter", "operator": "time_after", "property": "createTime", "id": "44ac5742-e607-41c8-84e2-29f881f10741"}], "operator": "and"}, "chartColorSet": [], "title": "每日笔记数", "updateAt": "2024-04-18T09:39:11.177Z"}, {"id": "da5efceb-ad8d-402e-9bbb-a753cbfcc469", "type": "chart", "titleAlign": "center", "chartType": "pie", "backgroundStyle": "none", "maxWidthRatio": 14.455782312925173, "maxHeight": 300, "labelProperty": "area", "labelFormat": "$none", "valueProperty": "$file_count", "filter": {"conditions": [], "operator": "and"}, "chartColorSet": [], "title": "", "chartLabelPosition": "hidden", "updateAt": "2024-04-18T09:39:16.360Z"}, {"id": "9063e79d-514b-4fae-9c06-a9966d5dd52c", "type": "multi", "titleAlign": "center", "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-03-24T11:44:41.466Z", "createAt": "2024-02-12T07:37:35.484Z", "remark": "主页备用", "components": [{"componentId": "c546683e-ed26-4fac-9d06-993a20f8db41"}, {"componentId": "709abbaf-772d-41f3-9b57-ded4031163f9"}, {"componentId": "f934c769-3bf7-4e4d-a256-075fa4b0735e"}]}, {"id": "c546683e-ed26-4fac-9d06-993a20f8db41", "type": "dataview", "titleAlign": "center", "query": "const data = dv.pages('#project')\n\t.filter(p => p.createTime)\n\t.map(p => {\n\t\treturn {\n\t\t\tdate: p.createTime.toFormat('yyyy-MM-dd'),\n\t\t\tvalue: p\n\t\t}\n\t})\n\t.groupBy(p => p.date)\n\t.map(entry =>{\n\t\treturn {\n\t\t\tdate: entry.key,\n\t\t\tvalue: entry.rows.length\n\t\t}\n\t})\n\nconst types = ['default', 'month-track',  'calendar']\n\n\tconst calendarData = {\n\t\tdays: 365,\n\t    title: '',\n            titleStyle: { fontSize: '18px', marginBottom: '6px' },\n\t    data: data,\n\t    graphType: 'default',\n\t    startOfWeek: 0,\n\t}\n\trenderContributionGraph(this.container, calendarData)\n", "queryType": "dataviewjs", "backgroundStyle": "card", "maxHeight": 300, "title": "🌈长期价值主义", "contentAlign": "center"}, {"id": "709abbaf-772d-41f3-9b57-ded4031163f9", "type": "chart", "titleAlign": "center", "chartType": "bar", "backgroundStyle": "card", "maxWidthRatio": 100, "maxHeight": 300, "labelProperty": "createTime", "labelFormat": "$toYearWeek", "valueProperty": "$file_count", "filter": {"conditions": [{"type": "tag", "value": ["#project"], "operator": "contains", "property": "", "id": "2da66fed-2847-4e4c-8bf3-124e9d53d2b2"}]}, "chartColorSet": [], "title": "每周项目数"}, {"id": "f934c769-3bf7-4e4d-a256-075fa4b0735e", "type": "chart", "titleAlign": "center", "chartType": "bar", "backgroundStyle": "card", "maxWidthRatio": 100, "maxHeight": 300, "labelProperty": "status", "labelFormat": "$none", "valueProperty": "$file_count", "filter": {"conditions": [{"type": "tag", "value": "#project", "operator": "contains", "property": "", "id": "a684a9d5-2620-47d5-b330-4b811d39d95f"}]}, "chartColorSet": ["#eb2626a3", "#4a80e9ae", "#38d685b5", "#a861e791", "#dcb43796", "#c8bdbda1"], "title": "领域投入度", "chartLabelPosition": "hidden"}, {"id": "2afeeb4a-bc6e-4200-912e-5f9e3f54ce9d", "type": "multi", "titleAlign": "center", "backgroundStyle": "none", "widgets": [], "layoutType": "tab", "updateAt": "2024-03-28T15:09:00.393Z", "createAt": "2024-02-13T04:29:07.754Z", "remark": "老版本", "components": [{"componentId": "05f46cbe-b069-42f4-bb93-560897b3efe9"}, {"componentId": "81236202-e441-4f90-8b9e-a6feb52e43d1"}, {"componentId": "53e3da9d-4bc2-4477-9bf0-38e6c40392d5"}, {"componentId": "e0477700-e8d5-4b57-88b3-11dd6d23d4d0"}, {"componentId": "8083844a-f526-44b4-8072-2c8b72a091ec"}, {"componentId": "266e7852-a72e-494e-9abe-6160b8d94ba4"}, {"componentId": "4b4f1473-4ad3-410e-adc8-9f6a5d654a79"}]}, {"id": "05f46cbe-b069-42f4-bb93-560897b3efe9", "type": "dataview", "titleAlign": "left", "query": "TABLE area, status, dateformat(createTime,\"yyyy-MM-dd\")  as \"创建时间\" from #project where \nicontains(file.name, \"{{name}}\") and\n(icontains(area, \"{{area}}\") or icontains(file.frontmatter.area, \"{{area}}\")) and\n(status=\"TODO\" or status=\"DOING\")\nsort createTime desc", "queryType": "dataview", "backgroundStyle": "card", "maxHeight": -1, "title": "", "tabTitle": "进行中项目", "dynamicParamComponents": [{"id": "ca68c956-371d-49c5-a5ba-2d4c126b926d", "type": "text", "name": "name", "defaultValue": "", "placeholder": "", "label": "文件名"}, {"id": "73e9e12d-428b-4b3e-afb2-63daa31836e3", "type": "propertyValueSuggestions", "name": "area", "defaultValue": "[[Obsidian]]", "placeholder": "", "label": "领域", "options": [{"id": "e70602f1-43a3-428e-a912-031b832a9309", "label": "Ob develop", "value": "Obsidian Plugin Development"}, {"id": "46ef8d74-b21c-4332-b0cc-a087be8ef3b9", "label": "pkm", "value": "pkm"}, {"id": "40d8de1d-03f9-46c0-ae6c-4241716cccb4", "label": "ikea", "value": "ikea"}], "fromProperty": "area"}, {"id": "d633e325-1ed9-435d-8043-2cefed9df474", "type": "tagSuggestions", "name": "tags", "defaultValue": "project", "placeholder": "", "fromProperty": "", "label": "tags"}]}, {"id": "81236202-e441-4f90-8b9e-a6feb52e43d1", "type": "markdown", "titleAlign": "center", "backgroundStyle": "card", "maxHeight": -1, "contentAlign": "left", "markdownsSource": "file", "markdownValue": "resource/主页图表配置合集/archive/Tasks.md", "tabTitle": "Widget 任务【这是一个名字很长的组件哦】", "updateAt": "2024-04-06T13:56:38.279Z"}, {"id": "53e3da9d-4bc2-4477-9bf0-38e6c40392d5", "type": "dataview", "titleAlign": "center", "query": "TABLE FROM #inbox sort createTime desc, file.name desc", "queryType": "dataview", "backgroundStyle": "card", "maxHeight": -1, "title": "", "tabTitle": "收件箱"}, {"id": "e0477700-e8d5-4b57-88b3-11dd6d23d4d0", "type": "dataview", "titleAlign": "left", "query": "TABLE from #area", "queryType": "dataview", "backgroundStyle": "card", "maxHeight": -1, "title": "", "contentAlign": "left", "tabTitle": "长期领域"}, {"id": "8083844a-f526-44b4-8072-2c8b72a091ec", "type": "dataview", "titleAlign": "center", "query": "TABLE from #resource where icontains(file.name, \"{{filename}}\")", "queryType": "dataview", "backgroundStyle": "card", "maxHeight": -1, "title": "", "dynamicParamComponents": [{"id": "0b274602-cdac-4bd4-87ad-1d6d7144f803", "type": "text", "name": "filename", "defaultValue": "", "placeholder": "", "label": "文件名"}], "tabTitle": "资源"}, {"id": "266e7852-a72e-494e-9abe-6160b8d94ba4", "type": "dataview", "titleAlign": "center", "query": "table area, status, archived, doneTime from #project where archived = true or status = \"DONE\" sort doneTime desc limit 20", "queryType": "dataview", "backgroundStyle": "card", "maxHeight": -1, "title": "", "tabTitle": "最近完成项目"}, {"id": "4b4f1473-4ad3-410e-adc8-9f6a5d654a79", "type": "dataview", "titleAlign": "center", "maxWidthRatio": -1, "query": "table  dateformat(createTime,\"yyyy-MM-dd\")  as \"创建时间\" from #post sort createTime desc", "queryType": "dataview", "backgroundStyle": "card", "maxHeight": 400, "contentAlign": "left", "dynamicParamComponents": [], "tabTitle": "已发布"}, {"id": "d5585e71-4c8c-4a55-b149-82c41c1c3a12", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "tab", "updateAt": "2024-04-19T01:42:10.726Z", "createAt": "2024-03-10T08:08:20.629Z", "remark": "Area/Obsidian", "components": [{"componentId": "85014c63-199b-4c41-8980-c96bb4a99f86"}, {"componentId": "adab8647-7281-47ef-930b-0c4483ab9a28"}]}, {"id": "85014c63-199b-4c41-8980-c96bb4a99f86", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-21T14:56:55.447Z", "updateAt": "2024-04-25T07:37:09.617Z", "backgroundStyle": "none", "viewType": "kanban", "datasource": {"filter": {"id": "ca5c923a-6e7d-481a-8b68-5a5ca2fe5021", "type": "group", "operator": "and", "conditions": [{"id": "65afee42-7692-4e0e-a6fd-59ea16d8beed", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "068c93d3-5bd0-408e-921d-c61af2b76054", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian]]", "conditions": []}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [{"id": "8c7111bf-997a-46fa-ada5-fdc636da1d02", "name": "tags", "isShow": true, "defaultValue": [], "type": "multiSelect"}, {"id": "d4d6b183-f8d2-4681-ab8a-8abd36111e3d", "name": "status", "isShow": true, "defaultValue": "", "type": "select"}, {"id": "e83a2ac6-5709-4f42-9331-d9b6c824471a", "name": "createTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "250c4135-d723-4cf2-8b25-b10cd4f2b4f0", "name": "doneTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "1100d5a9-4bd1-468f-9a0c-5de44c0c0d22", "name": "操作", "isShow": true, "defaultValue": "", "type": "button", "options": {"name": "完成", "action": {"type": "updateProperty", "properties": [{"id": "6a5baa63-1802-4152-933a-78935866f944", "name": "doneTime", "type": "datetime", "value": "$now"}, {"id": "0cedc0a6-15ea-4f4a-8b2c-eb6c55a5a351", "name": "status", "type": "string", "value": "DONE"}, {"id": "8c964357-2836-4b70-b0da-ef529bc54d3c", "name": "archived", "type": "boolean", "value": true}]}}}], "templates": [], "groups": [{"id": "044e1278-f267-4319-adb2-aceb17162964", "name": "BACKLOG", "items": [], "collapsed": false}, {"id": "8c55a03e-3b47-48bd-9d20-639afedeb86b", "name": "TODO", "items": [], "collapsed": false}, {"id": "17cb3914-993e-4e06-8666-4bf972c3d405", "name": "DOING", "items": ["space/20240425 废弃自定义卡片组件.md", "space/20240425 重新设计打卡组件.md", "space/20240420 BUG 修改数据视图配置后切换到源码后再渲染配置会被重置.md", "space/20240417 kanban 改为仅支持点击 Header 拖拽.md", "space/20240414 画廊封面支持设置比例.md", "space/20240412 重新实现组件的标题字体大小自适应.md", "space/20240411 构建一个压测 ob 库.md", "space/20240404 数据视图支持与 notion 联动.md", "space/20240325 动态视图增加公式属性类型.md"], "collapsed": false}, {"id": "f6376d24-eab7-4d11-bb2f-64474b1a2fa9", "name": "DONE", "items": [], "collapsed": true}, {"id": "0ec58403-24b9-498b-b1a5-67499583296e", "name": "CANCELED", "items": [], "collapsed": true}], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-medium", "showPropertyName": false, "cover": {"type": "none", "value": "", "fit": "contains"}, "headColumnWidth": "583"}, "groupBy": "status"}, {"id": "adab8647-7281-47ef-930b-0c4483ab9a28", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "已归档项目", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "table", "datasource": {"filter": {"conditions": [{"id": "9488b766-61cb-4b85-bdb6-b253d7f843b3", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "6b5444fc-8e83-47bd-bd58-eff63f9afce7", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian]]", "conditions": []}, {"id": "2c6d16fb-1d10-4fde-a69c-c53e464faf5a", "type": "group", "operator": "or", "conditions": [{"id": "6881ba13-033b-4b83-911c-538379ad68ed", "type": "filter", "operator": "equals", "property": "archived", "value": true, "conditions": []}, {"id": "1681b755-804f-4a2f-9c9d-fec79e4e6a57", "type": "filter", "operator": "contains", "property": "status", "value": "DONE", "conditions": []}, {"id": "8fa672ff-43f9-4e1c-832f-2514a4ab04c7", "type": "filter", "operator": "contains", "property": "status", "value": "CANCELED", "conditions": []}]}], "operator": "and"}, "dataLimit": 50, "storage": {"location": "space"}, "sort": {"orders": [{"id": "f27fe8f9-43f5-45a1-a7e6-8b4c643ba42e", "property": "doneTime", "direction": "desc"}]}}, "properties": [{"id": "36701667-aaec-45b3-8270-98d556e823c4", "name": "tags", "isShow": true, "defaultValue": ["project"], "type": "multiSelect", "options": {"width": "133"}, "alias": ""}, {"id": "83adc845-f8bc-4a49-95cf-807feba6e0b4", "name": "status", "isShow": true, "defaultValue": "TODO", "type": "select", "options": {"width": "144"}, "alias": ""}, {"id": "7328e325-2bf0-4058-8876-c26d45efbde9", "name": "area", "isShow": false, "defaultValue": "\"[[Obsidian]]\"", "type": "link", "options": {"width": "153"}, "alias": ""}, {"id": "10bf5906-4cfb-4813-88e6-481ef746333f", "name": "createTime", "isShow": true, "defaultValue": "$now", "type": "datetime", "alias": "", "options": {"width": "207"}}, {"id": "46f39251-99ab-440b-8157-586e715c9807", "name": "doneTime", "isShow": true, "defaultValue": "", "type": "datetime", "alias": ""}, {"id": "29597cf8-66f5-42d1-b5a8-2b32c67a4f14", "name": "archived", "isShow": false, "defaultValue": "", "type": "checkbox", "alias": "", "options": {"width": "146"}}, {"id": "e49f83f9-1ff7-4ce2-8ab7-e13c90745e21", "name": "投入", "isShow": true, "defaultValue": "", "type": "formula", "alias": "", "options": {"formula": "prop('doneTime')?duration(prop('createTime'), prop('doneTime'), 'days').toFixed(2)+\"天\": \"无\""}}, {"id": "b2f4d122-2234-4025-93ce-6dbe9fed0bd1", "name": "archived", "isShow": true, "defaultValue": "", "type": "checkbox", "alias": "", "options": {"formula": "prop('doneTime')?duration(prop('createTime'), prop('doneTime'), 'days').toFixed(2)+\"天\": \"无\""}}], "viewOptions": {"openPageIn": "tab", "itemSize": "gallary-small", "cover": {"type": "none", "value": "[[undraw_taking_notes_re_bnaf.svg]]", "fit": "contains"}, "headColumnWidth": "399"}, "templates": [{"id": "1d725a77-6856-4a07-b6f0-1617d801a636", "path": "system/template/project.md", "type": "templater"}], "updateAt": "2024-04-20T03:33:19.577Z"}, {"id": "e9391549-2a6d-4986-89d1-f42223fc71e8", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "tab", "updateAt": "2024-03-30T00:16:15.905Z", "createAt": "2024-03-10T10:00:53.390Z", "remark": "", "components": [{"componentId": "cce0cfb3-192f-485e-be46-6f51e926bd26"}, {"componentId": "8d034b68-10c8-4ed6-b70d-b92c8a12257d"}, {"componentId": "e899a919-204b-4cd5-9d34-7c36892ff523"}, {"componentId": "b284f766-90f4-415e-a082-bdf7100bada4"}]}, {"id": "cce0cfb3-192f-485e-be46-6f51e926bd26", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "领域", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "f966a1b3-1396-444d-875f-17a7ec742339", "type": "filter", "operator": "contains", "property": "tags", "value": "area", "conditions": []}, {"id": "3992fabf-23f5-4cb8-b520-467283b25bf5", "type": "filter", "operator": "not_contains", "property": "${file.basename}", "value": "IKEA", "conditions": []}], "operator": "and"}, "dataLimit": 50, "storage": {"location": "space"}, "sort": {"orders": [{"id": "dd94259d-7ec7-4562-b875-68e045b8db5c", "property": "createTime", "direction": "asc"}]}}, "properties": [{"id": "4dcb9d63-c724-40e6-8cf5-912794fa92ed", "name": "tags", "isShow": true, "defaultValue": ["project"], "type": "multiSelect"}, {"id": "982db0d7-8be4-4ec8-be19-988bbe35b16a", "name": "archived", "isShow": true, "defaultValue": "", "type": "checkbox"}, {"id": "20f86c9e-eb8b-4d91-b02a-86a7d39f2b5e", "name": "createTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "122cef65-7146-47ba-9c4d-f7c4feea39b5", "name": "关联项目数", "isShow": true, "defaultValue": "", "type": "backlinkCount"}], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-medium", "showPropertyName": false, "cover": {"type": "none", "value": "cover", "fit": "contains"}}, "updateAt": "2024-04-21T12:13:47.018Z"}, {"id": "8d034b68-10c8-4ed6-b70d-b92c8a12257d", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "进行中项目", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "kanban", "datasource": {"filter": {"conditions": [{"id": "510f613c-8ce7-4835-b84e-ee6505c8f90b", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "1d64d135-07b4-4d1a-ab4d-ce5372f3f46e", "type": "group", "operator": "or", "conditions": [{"id": "0939bbf1-b683-477a-afb6-b01ad8d408bb", "type": "filter", "operator": "contains", "property": "status", "value": "DOING", "conditions": []}, {"id": "9c3c80c1-8bbb-464e-8202-af7e7d7df644", "type": "filter", "operator": "contains", "property": "status", "value": "TODO", "conditions": []}]}], "operator": "and"}, "dataLimit": 50, "storage": {"location": "space"}, "sort": {"orders": [{"id": "5d537d96-7567-4333-8451-50f50aa86f5c", "property": "createTime", "direction": "desc"}]}}, "properties": [{"id": "41aed29c-b6a3-4835-8717-d1155dd5594c", "name": "tags", "isShow": true, "defaultValue": ["#project"], "type": "multiSelect", "alias": ""}, {"id": "ce831977-d6d4-4ccf-b1fe-050c7c6f8df8", "name": "area", "isShow": true, "defaultValue": "\"[[Obsidian]]\"", "type": "link", "alias": ""}, {"id": "84b16aca-296b-42e5-98fd-50c273745ac3", "name": "status", "isShow": true, "defaultValue": "TODO", "type": "select", "alias": ""}, {"id": "73ecae9a-3274-4841-9238-9878be0efc14", "name": "archived", "isShow": true, "defaultValue": "", "type": "checkbox", "alias": ""}, {"id": "74b4440d-be5d-49b8-a6af-ce72eca52435", "name": "createTime", "isShow": true, "defaultValue": "$now", "type": "datetime", "alias": ""}, {"id": "a5baab48-e8d0-4b6e-a974-31eb31514686", "name": "doneTime", "isShow": true, "defaultValue": "", "type": "datetime", "alias": ""}], "viewOptions": {"openPageIn": "split", "itemSize": "gallary-small", "cover": {"type": "none", "value": "/resource/素材库/project-cover.png", "fit": "contains"}, "headColumnWidth": "423"}, "updateAt": "2024-04-22T06:16:12.967Z", "groupBy": "status", "groups": [{"id": "a774f79f-73e0-4e0c-9e86-00a8d91abdc7", "name": "BACKLOG", "items": [], "collapsed": false}, {"id": "7abfe0ea-257e-4df3-9d59-fbd5f6249ef6", "name": "TODO", "items": [], "collapsed": false}, {"id": "09135aeb-b871-4895-b30d-d5a47f565fed", "name": "CANCELED", "items": [], "collapsed": false}, {"id": "2151e130-772e-43d0-9a9e-2f4e47f3c43a", "name": "DOING", "items": [], "collapsed": false}, {"id": "0759f9d5-f813-4c50-bb2a-66fa92962eda", "name": "DONE", "items": [], "collapsed": false}, {"id": "92343a55-2565-45c7-87c4-2bd7351874be", "name": "No status", "items": [], "collapsed": false}]}, {"id": "e899a919-204b-4cd5-9d34-7c36892ff523", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "最近完成任务", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "table", "datasource": {"filter": {"conditions": [{"id": "41f4f85c-ebd5-4766-9659-a32eda2856e5", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "751f0eb8-168e-405e-ab72-12537ac39687", "type": "group", "operator": "or", "conditions": [{"id": "b6911727-2083-4200-8813-bd67c689b996", "type": "filter", "operator": "contains", "property": "status", "value": "DONE", "conditions": []}, {"id": "8ecd8d35-23d7-4ace-bacb-fd2286777114", "type": "filter", "operator": "contains", "property": "status", "value": "CANCELED", "conditions": []}]}], "operator": "and"}, "dataLimit": 50, "storage": {"location": "space"}, "sort": {"orders": [{"id": "5d9953b0-44aa-4670-8e47-2026f23f56f1", "property": "doneTime", "direction": "desc"}]}}, "properties": [{"id": "a423e6c3-60ba-4a3d-8968-d41125c7bcb2", "name": "tags", "isShow": true, "defaultValue": "", "type": "multiSelect"}, {"id": "b2f20832-f9e5-4b15-81fe-e905298d740c", "name": "area", "isShow": true, "defaultValue": "", "type": "link", "alias": "", "options": {"width": "225"}}, {"id": "7947903f-0fd3-48be-9849-4fedba1b2a2d", "name": "status", "isShow": true, "defaultValue": "", "type": "select", "alias": ""}, {"id": "15038275-22b7-45d9-ac0e-d5213222a183", "name": "createTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "2831b601-d731-4847-b742-07f6d75b9211", "name": "doneTime", "isShow": true, "defaultValue": "", "type": "datetime", "alias": ""}, {"id": "70a5ef30-06f7-49f4-a01c-ef94141252b0", "name": "关联文件", "isShow": false, "defaultValue": "", "type": "backlinkCount", "alias": ""}], "viewOptions": {"openPageIn": "split", "itemSize": "gallary-small", "cover": {"type": "none", "value": "", "fit": "contains"}, "headColumnWidth": "359"}, "updateAt": "2024-04-21T08:29:09.926Z"}, {"id": "b284f766-90f4-415e-a082-bdf7100bada4", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "收件箱", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "b5cc2930-ece1-480c-b2c1-43c19a580de4", "type": "filter", "operator": "contains", "property": "tags", "value": "inbox", "conditions": []}]}, "dataLimit": 50, "storage": {"location": "space"}, "sort": {"orders": [{"id": "15f352da-bfc5-4158-b62e-2071234905aa", "property": "createTime", "direction": "desc"}]}}, "properties": [{"id": "c5c9d198-a2d8-4911-ab92-0d6b666f5b0c", "name": "status", "isShow": true, "defaultValue": "TODO", "type": "text"}, {"id": "663223c9-cc59-46cd-a31d-d2e4fd5f269f", "name": "createTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "4ddbc6a3-a6cd-4135-bba2-0610fbcf8d36", "name": "tags", "isShow": true, "defaultValue": ["#inbox"], "type": "multiSelect"}], "viewOptions": {"openPageIn": "split", "itemSize": "gallary-small", "cover": {"type": "pageFirstImage", "value": "", "fit": "contains"}}, "updateAt": "2024-04-18T09:41:18.213Z"}, {"id": "6bbcd7f9-1def-47a8-9656-2125ec1ca18c", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "tab", "updateAt": "2024-04-21T02:46:04.003Z", "createAt": "2024-03-10T13:37:15.532Z", "remark": "已发布文章画廊", "components": [{"componentId": "472dd755-8a07-4b53-8788-2bd0d1907d8e"}, {"componentId": "8fe7870b-af6b-4f7d-93ee-bbf917d494ac"}]}, {"id": "472dd755-8a07-4b53-8788-2bd0d1907d8e", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "领域", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "97e460e8-f5eb-4dd2-b676-6ee2712146b4", "type": "filter", "operator": "contains", "property": "tags", "value": "area", "conditions": []}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": [{"id": "31431750-af74-4d62-8245-62f5a4a67764", "property": "createTime", "direction": "desc"}]}}, "properties": [{"id": "b925ee09-231b-4124-bc24-4d92f8bcd2c8", "name": "createTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "04ac81bf-25d9-446a-9861-c18edaa8163a", "name": "tags", "isShow": true, "defaultValue": [], "type": "multiSelect"}], "viewOptions": {"openPageIn": "tab", "itemSize": "gallary-medium", "cover": {"type": "pageProperty", "value": "cover"}}, "updateAt": "2024-04-21T02:44:06.019Z"}, {"id": "8fe7870b-af6b-4f7d-93ee-bbf917d494ac", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "发布文章集", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "c81f136b-de0f-451e-93ca-47789a766ea2", "type": "filter", "operator": "contains", "property": "tags", "value": "post", "conditions": []}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": [{"id": "31431750-af74-4d62-8245-62f5a4a67764", "property": "createTime", "direction": "desc"}]}}, "properties": [{"id": "e5cc0593-5850-40c7-ab74-d65575c5febc", "name": "description", "isShow": true, "defaultValue": "", "type": "text", "options": {"width": "445"}}, {"id": "bb1539b6-bc47-4a87-ad6c-31da78e9003f", "name": "status", "isShow": true, "defaultValue": "", "type": "text"}, {"id": "b925ee09-231b-4124-bc24-4d92f8bcd2c8", "name": "createTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "04ac81bf-25d9-446a-9861-c18edaa8163a", "name": "tags", "isShow": true, "defaultValue": [], "type": "multiSelect"}], "viewOptions": {"openPageIn": "split", "itemSize": "gallary-medium", "cover": {"type": "pageFirstImage", "value": "cover"}, "headColumnWidth": "629"}, "updateAt": "2024-04-21T02:44:33.844Z"}, {"id": "4bb3df33-73cb-46ca-931f-9af8f1234c29", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "tab", "updateAt": "2024-04-15T11:58:33.768Z", "createAt": "2024-03-18T07:10:58.199Z", "remark": "IKEA工作", "components": [{"componentId": "0659d8f9-61e2-43bd-920c-39d9d24e960a"}]}, {"id": "0659d8f9-61e2-43bd-920c-39d9d24e960a", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "kanban", "datasource": {"filter": {"conditions": [{"id": "455030c8-1b4a-47d5-b02e-ff8a00d08414", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}, {"id": "05ead6a8-c31c-43dd-9dcb-0df67f04aca6", "type": "filter", "operator": "equals", "property": "area", "value": "[[IKEA]]", "conditions": []}], "operator": "and"}, "dataLimit": 50, "storage": {"location": "space"}, "sort": {"orders": []}}, "properties": [{"id": "d5779962-2755-4c9f-b99f-cf39365e67a4", "name": "tags", "isShow": true, "defaultValue": ["project"], "type": "multiSelect", "alias": ""}, {"id": "dc17dfd4-c5d2-49ca-b72b-0b33357faf6f", "name": "createTime", "isShow": true, "defaultValue": "$now", "type": "datetime"}, {"id": "048d14ea-e62f-4c34-a4a7-fb25164799f5", "name": "doneTime", "isShow": true, "defaultValue": "", "type": "datetime"}, {"id": "28f5aa2c-d26a-4a4a-9839-2eaf82c9df65", "name": "status", "isShow": true, "defaultValue": "TODO", "type": "select", "alias": ""}, {"id": "f6edaee1-fb1b-46fb-b6ad-8e41e1518ef6", "name": "area", "isShow": false, "defaultValue": "\"[[IKEA]]\"", "type": "text", "alias": "", "options": {"width": "122"}}, {"id": "48de7fc3-40f1-49a1-8dee-e3285c73348a", "name": "关联项目数", "isShow": true, "defaultValue": "", "type": "backlinkCount", "alias": ""}, {"id": "8ef22eec-abea-4c08-8235-dbd11fa732d5", "name": "完成", "isShow": true, "defaultValue": "", "type": "button", "alias": "", "options": {"name": " 🙂 完成任务", "action": {"type": "updateProperty", "properties": [{"id": "830a5c1d-ea62-45e2-9268-3701de8f4024", "name": "status", "type": "string", "value": "DONE"}, {"id": "945bd9ab-9a09-4f0b-8089-c3dfd1403f7b", "name": "doneTime", "type": "datetime", "value": "$now"}, {"id": "fc08d013-4450-44ea-9c36-f2750d6b6ec8", "name": "archived", "type": "boolean", "value": true}]}}}], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-medium", "showPropertyName": false, "cover": {"type": "none", "value": "resource/素材库/ikea-2.png", "fit": "fill"}, "headColumnWidth": "502"}, "updateAt": "2024-04-21T14:12:12.855Z", "groupBy": "status", "groups": [{"id": "d0649bc8-e645-4899-9d58-1f4253ce452d", "name": "BACKLOG", "items": []}, {"id": "82983778-2469-4fde-8bdf-27db216d0301", "name": "TODO", "items": []}, {"id": "a8135337-997c-446e-9a27-db826f1f65d1", "name": "DOING", "items": []}, {"id": "b4c951ee-e763-4af9-810b-afa51d6a86c6", "name": "DONE", "items": [], "collapsed": false}, {"id": "0e2d1553-2099-46c3-8089-82cba1abee09", "name": "CANCELED", "items": ["space/20240228 family-member-task 不归档未确认收货的线上订单数据.md", "space/20231108  当前积分的使用场景.md", "space/archive/20230915 活动管理中心/20230915 活动管理中心设计.md", "space/20230428 25周年活动积分商城库存转移.md"], "collapsed": true}]}, {"id": "4e8b2c2b-b040-447f-ae95-7a7585f47726", "type": "multi", "titleAlign": "center", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-04-10T15:17:25.549Z", "createAt": "2024-03-21T08:29:46.299Z", "remark": "案例", "components": [{"componentId": "47b15e65-37df-4a9c-b68e-7ab35925eaaa"}, {"componentId": "7f6a33e3-ea37-4134-8232-bc0ae22aadc9"}]}, {"id": "47b15e65-37df-4a9c-b68e-7ab35925eaaa", "type": "markdown", "titleAlign": "center", "maxWidthRatio": 50.2906976744186, "backgroundStyle": "none", "maxHeight": 300, "contentAlign": "left", "markdownsSource": "content", "markdownValue": "> [!NOTE] 😊 快速通道\n> - 🏡 [[../../../Home|Home]]\n> - 📒 [[../../../space/Obsidian|Obsidian]]\n> - ✈️ [[../../../space/Tourism|Tourism]]\n> - 💻 [[../../../space/Engineering|Engineering]]"}, {"id": "7f6a33e3-ea37-4134-8232-bc0ae22aadc9", "type": "markdown", "titleAlign": "center", "maxWidthRatio": 49.7093023255814, "backgroundStyle": "none", "maxHeight": 300, "contentAlign": "left", "markdownsSource": "content", "markdownValue": "> [!warning] 🐟 摸鱼通道\n> - 📷 [[Ins]]\n> - 📚 [[小红书]]\n> - 🧀 [[知乎]]\n> - ❓[[ProductHunt]]"}, {"id": "d528c2d1-a9a6-434e-9028-963b6203bc5e", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-04-20T03:36:41.003Z", "createAt": "2024-03-23T07:22:02.732Z", "remark": "", "components": [{"componentId": "9467999e-4dc3-43ee-b591-8c75748ec240", "widthRatio": 22.971428571428564}, {"componentId": "3ba70598-f4e9-4ff2-8a9b-2f4dc66d6b60", "widthRatio": 21.447619047619046}, {"componentId": "d73ec258-6f1f-4386-8d64-f9f2bd5adfe5", "widthRatio": 19.580952380952372}, {"componentId": "dcd31f4a-b152-426d-85a5-8dfd961a13c2", "widthRatio": 17.561904761904767}, {"componentId": "a60095a2-5a9f-492c-bdf8-4b890c1d7c2b", "widthRatio": 18.438095238095254}]}, {"id": "9467999e-4dc3-43ee-b591-8c75748ec240", "type": "count", "titleAlign": "center", "tabTitle": "总项目", "maxWidthRatio": -1, "backgroundStyle": "none", "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "20ea49b4-5bee-4b5b-94b1-335fb1ee1257", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian]]", "conditions": []}, {"id": "c071141b-c707-4623-8557-649b35ac350d", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}], "operator": "and"}, "type": "page"}, "title": "总项目", "backgroundColor": "#DDEBF1", "fontColor": "#0B6E99", "updateAt": "2024-04-18T12:42:22.448Z", "countType": "number"}, {"id": "3ba70598-f4e9-4ff2-8a9b-2f4dc66d6b60", "type": "count", "titleAlign": "center", "tabTitle": "待办", "maxWidthRatio": -1, "backgroundStyle": "none", "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "1ff17cf9-2195-4ea6-b473-c42e36645032", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian]]", "conditions": []}, {"id": "b8a2bd04-3aff-4b1e-a1dd-a75fe66f64e3", "type": "filter", "operator": "contains", "property": "status", "value": "TODO", "conditions": []}], "operator": "and"}, "type": "page"}, "title": "待办", "backgroundColor": "hsl(var(--interactive-accent-hsl), 0.1)", "fontColor": "hsl(var(--interactive-accent-hsl), 1)", "updateAt": "2024-04-18T12:43:12.093Z"}, {"id": "dcd31f4a-b152-426d-85a5-8dfd961a13c2", "type": "count", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "card", "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "5e4904c3-1b75-4c08-b729-41f69a0fafc1", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian]]", "conditions": []}, {"id": "3b2d0d33-f1e8-4b4d-91cc-123961722ddd", "type": "filter", "operator": "contains", "property": "status", "value": "CANCELED", "conditions": []}, {"id": "5f488386-2047-4524-ad92-3e5a77218710", "type": "filter", "operator": "contains", "property": "tags", "value": "project", "conditions": []}], "operator": "and"}, "type": "page"}, "title": "已取消", "backgroundColor": "#FBF3DB", "fontColor": "#DFAB01", "updateAt": "2024-04-18T12:44:10.191Z"}, {"id": "c164c102-8ba5-4477-aa54-2dd951c2eff5", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-04-17T02:42:12.528Z", "createAt": "2024-03-26T03:09:48.165Z", "remark": "资源集合", "components": [{"componentId": "01233e2a-8813-4033-bfcb-5078bc9f6bb7"}]}, {"id": "01233e2a-8813-4033-bfcb-5078bc9f6bb7", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "2a80e6f4-8a33-4c18-b36e-60e493454c28", "type": "filter", "operator": "contains", "property": "${file.tags}", "value": "", "conditions": []}]}, "dataLimit": 50, "storage": {"location": "resource"}, "sort": {"orders": []}}, "properties": [], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-small", "showPropertyName": false, "cover": {"type": "pageFirstImage", "value": "", "fit": "contains"}}, "updateAt": "2024-04-21T14:12:45.370Z"}, {"id": "c731271e-b297-492f-8d8a-8c48399f04ae", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-03-29T08:47:40.608Z", "createAt": "2024-03-29T08:47:17.838Z", "remark": "", "components": [{"componentId": "28ebb7e1-cd00-4342-9ff5-7dadd883d65f"}]}, {"id": "28ebb7e1-cd00-4342-9ff5-7dadd883d65f", "type": "chart", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "chartType": "pie", "backgroundStyle": "card", "maxHeight": 300, "labelProperty": "area", "labelFormat": "$none", "valueProperty": "$file_count", "filter": {"conditions": []}, "chartColorSet": [], "chartLabelPosition": "top"}, {"id": "c14fe5e6-1d7e-4c01-b226-510cd3346833", "type": "multi", "titleAlign": "center", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-03-29T09:10:55.594Z", "createAt": "2024-03-29T09:10:23.319Z", "remark": "", "components": [{"componentId": "b7531aa7-5085-420b-8436-927d15718fb3"}, {"componentId": "67fbd3e7-6d41-4109-aa54-0b9e84d2ee9d"}]}, {"id": "b7531aa7-5085-420b-8436-927d15718fb3", "type": "quote", "titleAlign": "center", "maxWidthRatio": 67.09302325581396, "backgroundStyle": "card", "contentType": "block", "coverType": "pageFirstImage", "filter": {"conditions": [{"type": "file_path", "value": "阅读", "operator": "contains", "property": "${file.path}", "id": "2dc22ff7-b92b-4cfe-a612-8636c8d0e872"}, {"type": "property", "value": "1", "operator": "greater_than", "property": "noteCount", "id": "91ecff18-c0be-4b94-a6e2-8b1a0c9136f3"}]}, "maxHeight": 300}, {"id": "67fbd3e7-6d41-4109-aa54-0b9e84d2ee9d", "type": "chart", "titleAlign": "center", "maxWidthRatio": 32.90697674418604, "chartType": "doughnut", "backgroundStyle": "card", "maxHeight": 300, "labelProperty": "area", "labelFormat": "$none", "valueProperty": "$file_count", "filter": {"conditions": []}, "chartColorSet": [], "chartLabelPosition": "hidden"}, {"id": "7ce42f1a-8c1d-4a1e-8c9c-26825e73c0cc", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "layoutType": "column", "updateAt": "2024-03-30T05:32:24.910Z", "createAt": "2024-03-30T05:31:56.138Z", "remark": "", "components": [{"componentId": "387c7ffd-68d6-4273-b735-c66ccd89e87f"}]}, {"id": "387c7ffd-68d6-4273-b735-c66ccd89e87f", "type": "dataview", "titleAlign": "center", "maxWidthRatio": -1, "query": "// 日期格式化\nconst dateFormat = \"YYYY-MM-DD\"\n// 表头：期望的展示的属性列表\nconst headers = [\"status\"]\n// 当前页码\nconst pageNum = {{pageNum}};\n// 单页数量\nconst pageSize = {{pageSize}};\n// 文件名称\nconst pageTitleLike = \"{{pageTitle}}\"\n// 标签\nconst tagLike = \"{{tag}}\"\n// 最大标题级别\nconst maxHeadingLevel = {{maxHeadingLevel}}\n// 标题查询\nconst heading = \"{{heading}}\"\n\nconst headingContent = (path, maxLvel) => {\n\tconst fileCache = this.app.metadataCache.getCache(path);\n\tif (!fileCache) {\n\t\treturn \"\";\n\t}\n\tconst headings = fileCache.headings || [];\n\tconst headingContent = headings\n\t\t.filter((h) => h.level <= maxLvel)\n\t\t.map((h) => {\n\t\t\tconst tab = \"\\\\#\";\n\t\t\treturn tab.repeat(h.level) + \" \" + h.heading;\n\t\t})\n\t\t.join(\"\\n\");\n\treturn headingContent;\n};\n\nconst data = dv.pages(\"\")\n  .where(p => p.file.name.includes(pageTitleLike))\n  .where(p => p.file.tags.some(t => t.includes(tagLike)))\n  .sort(p =>p.file.ctime, \"{{sort}}\")\n  .map(p => {\n    const heading = headingContent(p.file.path, maxHeadingLevel);\n    return [p.file.link, heading, ...headers.map(property => p[property]), moment(Number(p.file.ctime)).format(dateFormat)];\n  })\n  .filter(data => {\n\t  return data[1].includes(heading)\n  })\n  \nconst pageData = data.slice((pageNum - 1) * pageSize, pageNum * pageSize)\n  .limit(pageSize)\ndv.paragraph(\"当前共有 \" + pageData.length +\" 条数据\")\ndv.table([\"name\", \"heading\", ...headers, \"ctime\"], pageData)", "queryType": "dataviewjs", "backgroundStyle": "card", "maxHeight": -1, "dynamicParamComponents": [{"id": "93051e03-f464-4c32-8108-d76e88d2c228", "type": "text", "name": "pageTitle", "defaultValue": "", "placeholder": "", "fromProperty": "", "label": "标题"}, {"id": "9c15e095-f9ee-4e10-ace0-033576c7c678", "type": "tagSuggestions", "name": "tag", "defaultValue": "#journal", "placeholder": "", "fromProperty": "", "label": "标签"}, {"id": "01100e6e-42b5-4e12-91ad-823b903077f0", "type": "text", "name": "heading", "defaultValue": "", "placeholder": "", "fromProperty": "", "label": "标题查询"}, {"id": "905a2b40-1dc6-47e7-8c33-48887376e30c", "type": "number", "name": "maxHeadingLevel", "defaultValue": "2", "placeholder": "", "fromProperty": "", "label": "最大标题层级"}, {"id": "0b0999b1-2abe-4cba-90f3-407bff00e53e", "type": "number", "name": "pageNum", "defaultValue": "1", "placeholder": "", "fromProperty": "", "label": "页码"}, {"id": "07739f36-696d-4d8e-ba6b-52449507de5a", "type": "number", "name": "pageSize", "defaultValue": "10", "placeholder": "", "fromProperty": "", "label": "数量"}, {"id": "0e5f37e4-162f-4698-9442-07b81017f62b", "type": "select", "name": "sort", "defaultValue": "desc", "placeholder": "", "fromProperty": "", "options": [{"id": "a0c29755-1fbf-4750-b5bd-79c99982dc3d", "label": "创建时间降序", "value": "desc"}, {"id": "bac80e8a-3260-4502-858e-f7b004e3b8d6", "label": "创建时间升序", "value": "asc"}], "label": "排序"}], "title": ""}, {"id": "942d8003-dda0-46c9-96d3-2716bcd85ddc", "type": "checkIn", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "cellShape": "round", "backgroundStyle": "none", "tasks": [{"id": "34ecd6cb-044c-475d-8c03-1ea5c9c83863", "checkInRecordType": "task", "tag": "#阅读", "name": ""}, {"id": "085915e0-e3c4-4fdf-8a17-9794da031dd1", "checkInRecordType": "task", "tag": "#健身", "name": ""}, {"id": "425ad182-6c11-4e91-b063-d249cb7a2b64", "checkInRecordType": "task", "tag": "#插件开发", "name": ""}], "updateAt": "2024-04-06T03:22:01.302Z"}, {"id": "401fee6b-7841-4bf4-be3f-ea6231b4e512", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "06a6755a-dc83-49ae-af8c-a680bcd46445"}], "layoutType": "column", "updateAt": "2024-04-17T02:41:44.034Z", "remark": ""}, {"id": "06a6755a-dc83-49ae-af8c-a680bcd46445", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"value": "#post", "operator": "contains", "property": "${file.tags}", "id": "bac75a8d-c9b9-4b58-aa72-af8b93d71c34"}]}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": []}}, "properties": [{"id": "504c1851-7cf0-421e-8aa7-a3858429a733", "name": "tags", "isShow": true, "defaultValue": [], "type": "multiSelect"}], "templates": [], "viewOptions": {"openPageIn": "tab", "itemSize": "gallary-small", "cover": {"type": "pageFirstImage", "value": "", "fit": "contains"}}, "createAt": "2024-04-01T12:50:58.728Z", "updateAt": "2024-04-02T05:40:00.080Z"}, {"id": "ca1fea20-229a-46d0-8050-3b9f42771b5d", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "f4322d13-318d-4e47-a962-649fb34521f7"}], "layoutType": "tab", "updateAt": "2024-04-17T02:42:02.486Z", "remark": "书库"}, {"id": "f4322d13-318d-4e47-a962-649fb34521f7", "type": "dynamicDataView", "titleAlign": "center", "tabTitle": "书库", "maxWidthRatio": -1, "backgroundStyle": "none", "viewType": "gallary", "datasource": {"filter": {"conditions": [{"id": "3878da2b-8f6f-42d7-95b1-3cc17370580f", "type": "filter", "operator": "contains", "property": "${file.path}", "value": "resource/阅读", "conditions": []}], "operator": "and"}, "dataLimit": 50, "storage": {"location": ""}, "sort": {"orders": [{"id": "7521a6ca-2397-4e47-94e7-2a8750df35a4", "property": "${file.ctime}", "direction": "asc"}]}}, "properties": [{"id": "c3a4fffb-5ae8-4fd1-a1eb-8520f5c7587c", "name": "author", "isShow": true, "defaultValue": "", "type": "select"}, {"id": "034a4ba0-e8e7-4f8c-bd2d-4ef01308bd88", "name": "noteCount", "isShow": true, "defaultValue": 0, "type": "number", "options": {"suffix": "条笔记"}}, {"id": "5ef58e71-7f1d-47c4-820a-f8707eed278d", "name": "bookId", "isShow": false, "defaultValue": 0, "type": "number"}, {"id": "1ec8926c-2b0e-4713-89c2-81a0ebc98a97", "name": "category", "isShow": true, "defaultValue": "", "type": "select"}], "templates": [], "viewOptions": {"openPageIn": "tab", "itemSize": "components--project-card-medium", "showPropertyName": false, "cover": {"type": "pageProperty", "value": "cover", "fit": "contains"}}, "createAt": "2024-04-03T01:42:51.227Z", "updateAt": "2024-04-21T03:01:19.664Z"}, {"id": "54e2ab0e-c0dc-4142-979e-d816ac0dfce4", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "0129b8f1-bf6c-452f-990b-8be2b7c867ce"}], "layoutType": "column", "updateAt": "2024-04-03T01:44:36.242Z"}, {"id": "0129b8f1-bf6c-452f-990b-8be2b7c867ce", "type": "button", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-03T01:44:36.241Z", "updateAt": "2024-04-03T01:45:33.653Z", "text": "🏳‍🌈同步读书笔记", "buttonType": "common", "clickActions": [{"type": "CallCommand", "id": "7ccf577b-c53a-4f19-9fec-eb0409f1d8b1", "options": {"commandId": "obsidian-weread-plugin:sync-weread-notes-command", "commandName": "Weread: 同步微信读书笔记"}}], "checkActions": [], "uncheckActions": [], "isChecked": false, "backgroundStyle": "none"}, {"id": "e5a6c4d3-46ec-4c82-8fe3-3b0ff60386ef", "type": "dataview", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-04T02:51:56.822Z", "updateAt": "2024-04-04T02:52:10.056Z", "query": "dv.paragraph('1')", "queryType": "dataviewjs", "backgroundStyle": "card", "maxHeight": 300, "dynamicParamComponents": []}, {"id": "d067b02e-8e91-4b94-9094-b282a6ef27e8", "type": "dateProgress", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-04T06:17:05.470Z", "updateAt": "2024-04-04T06:30:45.379Z", "backgroundStyle": "card", "showDateIndicator": true, "showProgressIndicator": true, "startDateTimeType": "$startOfMonth", "endDateTimeType": "$endOfMonth", "uiType": "bar"}, {"id": "d73ec258-6f1f-4386-8d64-f9f2bd5adfe5", "type": "count", "titleAlign": "center", "tabTitle": "进行中", "maxWidthRatio": -1, "backgroundStyle": "none", "query": {"valueType": "query", "value": 100, "filter": {"conditions": [{"id": "c22cb94b-23f9-4bfb-bb62-8c51117ea149", "type": "filter", "operator": "contains", "property": "area", "value": "[[Obsidian]]", "conditions": []}, {"id": "4989fe91-4ba6-457a-b2c2-bbda3cd5ae92", "type": "filter", "operator": "contains", "property": "status", "value": "DOING", "conditions": []}], "operator": "and"}, "type": "page"}, "title": "进行中", "backgroundColor": "hsl(var(--interactive-accent-hsl), 0.1)", "fontColor": "hsl(var(--interactive-accent-hsl), 1)", "updateAt": "2024-04-18T12:43:39.771Z", "createAt": "2024-04-04T11:46:12.497Z"}, {"id": "8dfd9664-9d1d-4125-be19-1ec641ec73e4", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-12T13:09:28.843Z", "updateAt": "2024-04-17T02:41:52.074Z", "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "1bb20e34-ea12-4f90-8cc0-e0679526c5b4"}], "layoutType": "column", "remark": "图表库"}, {"id": "1b20a49d-0a9a-4e1d-be93-5a95080e284b", "type": "multi", "titleAlign": "center", "tabTitle": "", "maxWidthRatio": -1, "createAt": "2024-04-21T07:34:02.897Z", "updateAt": "2024-04-21T08:14:54.859Z", "backgroundStyle": "none", "widgets": [], "components": [{"componentId": "95c98ca3-0c22-447b-9057-3d8583b97161"}], "layoutType": "tab"}], "widgets": [], "DEFAULT_SETTINGS": {"folder": "", "createComponentSilent": false, "scriptFolder": "components/scripts"}}