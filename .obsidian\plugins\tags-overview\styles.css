.tags-overview input[type=text]:focus {
  box-shadow: initial;
}
.tags-overview .tags-container {
  margin-bottom: 30px;
}
.tags-overview .tags-info-container {
  display: flex;
  justify-content: space-between;
  padding: 3px;
}
.tags-overview .count-label {
  font-size: var(--font-smallest);
  padding: 4px;
}
.tags-overview .tags-info-container .icons {
  display: flex;
}
.tags-overview .display-type-compact .file-link {
  display: inline-block;
  background-color: var(--color-base-40);
  border-radius: 10px;
  font-size: var(--font-ui-smaller);
  padding: 4px 8px;
  color: var(--color-base-100);
  margin: 4px 4px 0 0;
  cursor: pointer;
}
.tags-overview .display-type-compact .file-link:hover {
  background-color: var(--color-base-100);
  color: var(--color-base-40);
}
.tags-overview .tag-title-row {
  margin: 10px 0 5px;
}
.tags-level-0 > .tags-overview .tag-title-row {
  margin-top: 20px;
}
.tags-overview .tag-title-row span {
  margin: 6px 0 0 8px;
  vertical-align: top;
  display: inline-block;
  font-size: 12px;
  color: #aaa;
}
.tags-overview .tag-title {
  margin: 0;
  display: inline-block;
}
.tags-overview .tags-table-container .tag-title-row {
  margin: 8px 0 1px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.tags-overview .tags-table-container .sub-tags-row .tag-title-row {
  margin-top: 2px;
}
.tags-overview .tags-table-container .tag-title-row span {
  margin: 10px 5px 0 0;
  vertical-align: top;
  display: inline-block;
  font-size: var(--font-ui-smaller);
  color: var(--color-base-70);
  white-space: nowrap;
  padding-left: 5px;
}
.tags-overview .tags-table-container .tag-title-row .tag-title {
  margin: 0;
  display: inline-block;
}
.tags-overview .tags-table-container {
  position: relative;
}
.tags-overview .tags-table-container.tags-level-0 {
  margin-top: 20px;
}
.tags-overview .tags-table-container table {
  width: 100%;
  border-spacing: 0;
  border-collapse: initial;
  border: 0 !important;
}
.tags-overview .tags-table-container th {
  text-align: left;
  font-size: var(--font-ui-smaller);
  color: var(--color-base-70);
}
.tags-overview .tags-table-container th.align-center {
  text-align: center;
}
.tags-overview .tags-table-container th.align-right {
  text-align: right;
}
.tags-overview .tags-table-container tbody td {
  border-top: 1px solid var(--color-base-40);
  border-left: 0 !important;
  border-right: 0 !important;
  border-bottom: 0 !important;
  font-size: var(--font-ui-small);
  padding: 4px;
  margin: 0;
}
.tags-overview .tags-table-container tbody tr.sub-tags-row > td {
  border-top: 0 !important;
}
.tags-overview .align-left {
  text-align: left;
}
.tags-overview .align-center {
  text-align: center;
}
.tags-overview .align-right {
  text-align: right;
}
.tags-overview .col-modified, .tags-overview .col-created {
  white-space: nowrap;
}
.tags-overview .col-size {
  width: 80px;
}
.tags-overview .tags-table-container tbody tr > td:first-child {
  color: var(--link-color);
}
.tags-overview .has-sub-tags > .tag-content {
  margin-left: -12px;
  padding-left: 12px;
  border-left: 1px solid var(--color-base-50);
  transition: transform 1000ms ease-in-out;
}
.tags-overview .tags-level-0.has-sub-tags > .tag-content {
  border-color: var(--color-base-40);
}
.tags-overview .tags-table-container.has-sub-tags {
  margin-left: 10px;
}
.tags-overview .tags-table-container tbody tr.file-row:hover {
  background-color: var(--nav-item-background-hover);
  cursor: pointer;
}
.tags-overview .tags-table-container tbody tr.file-row:hover > :first-child {
  color: var(--link-color-hover);
}
.tags-overview .tags-filter-select {
  color: #222;
  margin-bottom: 10px;
  font-size: var(--font-ui-small);
}
.tags-overview .tags-filter-text {
  background-color: hsl(0, 0%, 100%);
  border-color: hsl(0, 0%, 80%);
  border-radius: 4px;
  width: 100%;
  padding: 20px 10px;
  color: hsl(0, 0%, 20%);
}
.tags-overview .tags-filter-text::placeholder {
  color: hsl(0, 0%, 50%);
  font-size: var(--font-ui-small);
}
.tags-overview .tags-filter-text.tags-filter-number {
  margin-top: 1px;
}
.tags-overview .tags-filter-text.tags-filter-number:hover {
  border-color: hsl(0, 0%, 80%);
}
.tags-overview .header-with-settings {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.tags-overview .header-with-settings .title {
  margin: 3px 0 0;
  color: var(--color-base-70);
  font-size: var(--font-ui-small);
}
.tags-overview .header-with-settings.slim {
  margin: 10px 3px 2px;
}
.tags-overview .header-with-settings:not(.slim) {
  border-bottom: 1px solid var(--color-base-40);
  margin-top: 40px;
  margin-bottom: 2px;
}
.tags-overview .header-with-settings:not(.slim) .title {
  margin: 3px 0 1px;
}
.tags-overview .settings-switch span {
  font-size: 10px;
  border: 1px solid var(--color-base-40);
  padding: 3px 6px 4px;
  bottom: 0px;
  position: relative;
  display: inline;
}
.tags-overview .settings-switch span.active {
  background-color: var(--color-base-40);
}
.tags-overview .settings-switch span.inactive {
  color: var(--color-base-60);
  cursor: pointer;
}
.tags-overview .settings-switch span.inactive:hover {
  background-color: var(--color-base-20);
}
.tags-overview .settings-switch span:first-child {
  border-top-left-radius: 5px;
}
.tags-overview .settings-switch span:last-child {
  border-top-right-radius: 5px;
}
.tags-overview .has-sub-tags > .nested-container {
  margin-left: -12px;
  padding-left: 12px;
  border-left: 1px solid var(--color-base-40);
  transition: transform 100ms ease-in-out;
}
.tags-overview .tags-level-0.has-sub-tags > .nested-container {
  border-color: var(--background-secondary-alt);
}
.tags-overview .has-sub-tags.is-collapsed > .nested-container {
  border-left: 1px solid #333;
}
.tags-overview .nested-tags-container {
  position: relative;
  margin-left: 5px;
}
.tags-overview .nested-tags-container.has-sub-tags {
  margin-left: 15px;
}
.tags-overview .custom-icon {
  color: var(--icon-color);
  opacity: var(--icon-opacity);
}
.tags-overview .collapse-icon {
  position: absolute;
  top: 4px;
  margin-left: -17px;
  width: 16px;
}
.tags-overview .collapse-icon svg {
  stroke-width: 4px;
  width: 10px;
  height: 10px;
  transition: transform 100ms ease-in-out;
}
.tags-overview .is-collapsed > .collapse-icon svg {
  transform: rotate(-90deg);
}
.tags-overview .tags-info-container .custom-icon {
  display: flex;
  padding: var(--size-2-2) var(--size-2-3);
}
.tags-overview .custom-icon {
  -webkit-app-region: no-drag;
  background-color: transparent;
  align-items: center;
  justify-content: center;
  cursor: var(--cursor);
  border-radius: var(--clickable-icon-radius);
  transition: opacity 0.15s ease-in-out;
  height: auto;
}
.tags-overview .custom-icon:hover {
  box-shadow: none;
  opacity: var(--icon-opacity-hover);
  color: var(--icon-color-hover);
  background-color: var(--background-modifier-hover);
}
.tags-overview .custom-icon svg {
  height: var(--icon-size);
  width: var(--icon-size);
  stroke-width: var(--icon-stroke);
}
.tags-overview .custom-icon.is-active {
  opacity: var(--icon-opacity-hover);
  color: var(--icon-color-active);
  background-color: var(--background-modifier-active-hover);
}
.tags-overview .custom-icon.is-disabled, .tags-overview .custom-icon.is-disabled:hover {
  background-color: unset;
  color: var(--text-muted);
  opacity: 0.4;
}
.tags-overview .custom-icon.is-disabled:hover {
  background-color: unset;
}

.tags-overview-table-settings {
  padding: 0.75em 0;
  border-top: 1px solid var(--background-modifier-border);
}

.tags-overview-settings-table {
  width: 100%;
  margin-bottom: 5px;
  border: 0 !important;
}
.tags-overview-settings-table th, .tags-overview-settings-table td {
  border: 0 !important;
}
.tags-overview-settings-table th {
  text-align: left;
  padding: 8px 10px;
  font-size: var(--font-ui-smaller);
}
.tags-overview-settings-table .no-columns-added {
  padding: 15px;
  text-align: center;
  background-color: var(--background-secondary);
}
.tags-overview-settings-table .no-columns-added p {
  margin: 4px;
  font-size: 10px;
}
.tags-overview-settings-table thead {
  border-bottom: 1px solid var(--color-base-40) !important;
}
.tags-overview-settings-table tbody tr {
  background-color: var(--background-secondary);
}
.tags-overview-settings-table tbody tr:nth-child(odd) {
  background-color: var(--background-secondary-alt);
}
.tags-overview-settings-table tbody td {
  padding: 4px 8px;
  font-size: var(--font-ui-small);
}
.tags-overview-settings-table tfoot td {
  padding: 8px 10px;
}
.tags-overview-settings-table .front-matter-property-select {
  margin-left: 20px;
}
.tags-overview-settings-table .custom-icon {
  display: inline-block;
  margin: 2px 2px 0 2px;
}
.tags-overview-settings-table .custom-icon svg {
  width: 18px !important;
  height: 18px;
}
.tags-overview-settings-table .custom-icon:hover {
  color: var(--icon-color-hover);
  cursor: pointer;
}

.save-load-filters-icon {
  display: inline-block;
  padding: var(--size-2-2) var(--size-2-3);
}

.tag-view-popover {
  position: absolute;
  top: 28px;
  left: 0;
  background-color: var(--background-primary);
  padding: 0px 10px 15px 10px;
  border: 1px solid var(--background-modifier-border-focus);
  border-radius: 10px;
  z-index: 1000;
}
.tag-view-popover h4 {
  padding: 0 10px;
  margin-bottom: 4px;
}
.tag-view-popover ul {
  list-style: none;
  padding: 0;
  margin: 0 0 10px;
}
.tag-view-popover ul li {
  display: flex;
  justify-content: space-between;
  padding: 4px 10px;
}
.tag-view-popover ul li > span {
  flex-grow: 1;
  display: inline-block;
  padding-right: 20px;
  font-size: var(--font-smaller);
  cursor: pointer;
}
.tag-view-popover ul li > span:hover {
  color: var(--link-color-hover);
}
.tag-view-popover ul li .trash-icon {
  width: 16px;
  height: 16px;
}
.tag-view-popover ul li .trash-icon svg {
  width: 16px;
  height: 16px;
}
.tag-view-popover ul li:nth-child(even) {
  background-color: var(--background-secondary);
}
.tag-view-popover hr {
  margin: 15px 0;
}
.tag-view-popover .save-link {
  margin-left: 10px;
}
.tag-view-popover .save-link .save-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  vertical-align: top;
  margin-right: 8px;
  color: var(--link-color);
}
.tag-view-popover .save-link .save-icon svg {
  width: 18px;
  height: 18px;
}
.tag-view-popover .save-link:hover .save-icon {
  color: var(--link-color-hover);
}
.tag-view-popover .no-saved-filters {
  padding: 5px 35px;
  text-align: center;
  color: var(--color-base-70);
  font-size: var(--font-smaller);
}
