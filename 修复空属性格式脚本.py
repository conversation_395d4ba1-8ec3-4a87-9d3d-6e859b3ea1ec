#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复空属性格式脚本
所有属性值留空，供用户后续填写
"""

import os
from pathlib import Path

def generate_empty_format():
    """生成空属性格式的文件内容"""
    content = """---
省份:
级别:
地址:
电话:
名称:
负责人:
负责人联系方式:
地区政策:
注意点:
---

"""
    return content

def main():
    """主函数"""
    print("开始修复为空属性格式...")
    
    netinfo_dir = Path('数据出境合规实务50问汇总/网信办联系方式')
    
    # 获取所有网信办文件
    md_files = list(netinfo_dir.glob('*.md'))
    
    # 修复每个文件
    for file_path in md_files:
        content = generate_empty_format()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"修复文件: {file_path.name}")
    
    print(f"\n修复完成！共处理 {len(md_files)} 个文件")
    print("所有属性值已清空，供您后续填写")

if __name__ == "__main__":
    main()
