/* Homepage Plugin Styles */

.homepage-setting-item {
    padding: 1em 0;
    border-bottom: 1px solid var(--background-modifier-border);
}

.homepage-setting-item:last-child {
    border-bottom: none;
}

.homepage-setting-name {
    font-weight: 600;
    margin-bottom: 0.5em;
}

.homepage-setting-desc {
    color: var(--text-muted);
    font-size: 0.9em;
    margin-bottom: 1em;
}

.homepage-path-input {
    width: 100%;
    padding: 0.5em;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
}

.homepage-path-input:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

/* 命令面板中的首页命令图标 */
.suggestion-item[data-id="homepage-plugin:open-homepage"] .suggestion-icon {
    color: var(--interactive-accent);
}

/* 设置页面标题样式 */
.homepage-plugin-settings h2 {
    color: var(--text-accent);
    border-bottom: 2px solid var(--interactive-accent);
    padding-bottom: 0.5em;
    margin-bottom: 1.5em;
}