"use strict";var t=require("obsidian");function e(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function n(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function r(t){let r,a,l;function o(t,e,n=0,i=t.length){if(n<i){if(0!==r(e,e))return i;do{const r=n+i>>>1;a(t[r],e)<0?n=r+1:i=r}while(n<i)}return n}return 2!==t.length?(r=e,a=(n,r)=>e(t(n),r),l=(e,n)=>t(e)-n):(r=t===e||t===n?t:i,a=t,l=t),{left:o,center:function(t,e,n=0,r=t.length){const i=o(t,e,n,r-1);return i>n&&l(t[i-1],e)>-l(t[i],e)?i-1:i},right:function(t,e,n=0,i=t.length){if(n<i){if(0!==r(e,e))return i;do{const r=n+i>>>1;a(t[r],e)<=0?n=r+1:i=r}while(n<i)}return n}}}function i(){return 0}const a=r(e).right;r((function(t){return null===t?NaN:+t})).center;class l extends Map{constructor(t,e=s){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[e,n]of t)this.set(e,n)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):(t.set(r,n),n)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},n){const r=e(n);t.has(r)&&(n=t.get(r),t.delete(r));return n}(this,t))}}function o({_intern:t,_key:e},n){const r=e(n);return t.has(r)?t.get(r):n}function s(t){return null!==t&&"object"==typeof t?t.valueOf():t}function u(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}const f=Math.sqrt(50),c=Math.sqrt(10),h=Math.sqrt(2);function d(t,e,n){const r=(e-t)/Math.max(0,n),i=Math.floor(Math.log10(r)),a=r/Math.pow(10,i),l=a>=f?10:a>=c?5:a>=h?2:1;let o,s,u;return i<0?(u=Math.pow(10,-i)/l,o=Math.round(t*u),s=Math.round(e*u),o/u<t&&++o,s/u>e&&--s,u=-u):(u=Math.pow(10,i)*l,o=Math.round(t/u),s=Math.round(e/u),o*u<t&&++o,s*u>e&&--s),s<o&&.5<=n&&n<2?d(t,e,2*n):[o,s,u]}function g(t,e,n){return d(t=+t,e=+e,n=+n)[2]}function p(t,e,n){n=+n;const r=(e=+e)<(t=+t),i=r?g(e,t,n):g(t,e,n);return(r?-1:1)*(i<0?1/-i:i)}function y(t,e){let n;for(const e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);return n}function m(t,e){let n;for(const e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);return n}function v(t,n,r=0,i=1/0,a){if(n=Math.floor(n),r=Math.floor(Math.max(0,r)),i=Math.floor(Math.min(t.length-1,i)),!(r<=n&&n<=i))return t;for(a=void 0===a?u:function(t=e){if(t===e)return u;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,n)=>{const r=t(e,n);return r||0===r?r:(0===t(n,n))-(0===t(e,e))}}(a);i>r;){if(i-r>600){const e=i-r+1,l=n-r+1,o=Math.log(e),s=.5*Math.exp(2*o/3),u=.5*Math.sqrt(o*s*(e-s)/e)*(l-e/2<0?-1:1);v(t,n,Math.max(r,Math.floor(n-l*s/e+u)),Math.min(i,Math.floor(n+(e-l)*s/e+u)),a)}const e=t[n];let l=r,o=i;for(x(t,r,n),a(t[i],e)>0&&x(t,r,i);l<o;){for(x(t,l,o),++l,--o;a(t[l],e)<0;)++l;for(;a(t[o],e)>0;)--o}0===a(t[r],e)?x(t,r,o):(++o,x(t,o,i)),o<=n&&(r=o+1),n<=o&&(i=o-1)}return t}function x(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function A(t,e){return function(t,e){if((n=(t=Float64Array.from(function*(t){for(let e of t)null!=e&&(e=+e)>=e&&(yield e)}(t))).length)&&!isNaN(e=+e)){if(e<=0||n<2)return m(t);if(e>=1)return y(t);var n,r=(n-1)*e,i=Math.floor(r),a=y(v(t,i).subarray(0,i+1));return a+(m(t.subarray(i+1))-a)*(r-i)}}(t,.5)}function w(t,e,n){t=+t,e=+e,n=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+n;for(var r=-1,i=0|Math.max(0,Math.ceil((e-t)/n)),a=new Array(i);++r<i;)a[r]=t+r*n;return a}function b(t,e){let n=0;if(void 0===e)for(let e of t)(e=+e)&&(n+=e);else{let r=-1;for(let i of t)(i=+e(i,++r,t))&&(n+=i)}return n}function D(t){return t}var k=1e-6;function C(t){return"translate("+t+",0)"}function M(t){return"translate(0,"+t+")"}function T(t){return e=>+t(e)}function _(t,e){return e=Math.max(0,t.bandwidth()-2*e)/2,t.round()&&(e=Math.round(e)),n=>+t(n)+e}function E(){return!this.__axis}function F(t,e){var n=[],r=null,i=null,a=6,l=6,o=3,s="undefined"!=typeof window&&window.devicePixelRatio>1?0:.5,u=1===t||4===t?-1:1,f=4===t||2===t?"x":"y",c=1===t||3===t?C:M;function h(h){var d=null==r?e.ticks?e.ticks.apply(e,n):e.domain():r,g=null==i?e.tickFormat?e.tickFormat.apply(e,n):D:i,p=Math.max(a,0)+o,y=e.range(),m=+y[0]+s,v=+y[y.length-1]+s,x=(e.bandwidth?_:T)(e.copy(),s),A=h.selection?h.selection():h,w=A.selectAll(".domain").data([null]),b=A.selectAll(".tick").data(d,e).order(),C=b.exit(),M=b.enter().append("g").attr("class","tick"),F=b.select("line"),S=b.select("text");w=w.merge(w.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),b=b.merge(M),F=F.merge(M.append("line").attr("stroke","currentColor").attr(f+"2",u*a)),S=S.merge(M.append("text").attr("fill","currentColor").attr(f,u*p).attr("dy",1===t?"0em":3===t?"0.71em":"0.32em")),h!==A&&(w=w.transition(h),b=b.transition(h),F=F.transition(h),S=S.transition(h),C=C.transition(h).attr("opacity",k).attr("transform",(function(t){return isFinite(t=x(t))?c(t+s):this.getAttribute("transform")})),M.attr("opacity",k).attr("transform",(function(t){var e=this.parentNode.__axis;return c((e&&isFinite(e=e(t))?e:x(t))+s)}))),C.remove(),w.attr("d",4===t||2===t?l?"M"+u*l+","+m+"H"+s+"V"+v+"H"+u*l:"M"+s+","+m+"V"+v:l?"M"+m+","+u*l+"V"+s+"H"+v+"V"+u*l:"M"+m+","+s+"H"+v),b.attr("opacity",1).attr("transform",(function(t){return c(x(t)+s)})),F.attr(f+"2",u*a),S.attr(f,u*p).text(g),A.filter(E).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",2===t?"start":4===t?"end":"middle"),A.each((function(){this.__axis=x}))}return h.scale=function(t){return arguments.length?(e=t,h):e},h.ticks=function(){return n=Array.from(arguments),h},h.tickArguments=function(t){return arguments.length?(n=null==t?[]:Array.from(t),h):n.slice()},h.tickValues=function(t){return arguments.length?(r=null==t?null:Array.from(t),h):r&&r.slice()},h.tickFormat=function(t){return arguments.length?(i=t,h):i},h.tickSize=function(t){return arguments.length?(a=l=+t,h):a},h.tickSizeInner=function(t){return arguments.length?(a=+t,h):a},h.tickSizeOuter=function(t){return arguments.length?(l=+t,h):l},h.tickPadding=function(t){return arguments.length?(o=+t,h):o},h.offset=function(t){return arguments.length?(s=+t,h):s},h}function S(t){return F(3,t)}function B(t){return F(4,t)}var N={value:()=>{}};function L(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new I(r)}function I(t){this._=t}function O(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}function V(t,e,n){for(var r=0,i=t.length;r<i;++r)if(t[r].name===e){t[r]=N,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=n&&t.push({name:e,value:n}),t}I.prototype=L.prototype={constructor:I,on:function(t,e){var n,r,i=this._,a=(r=i,(t+"").trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))),l=-1,o=a.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++l<o;)if(n=(t=a[l]).type)i[n]=V(i[n],t.name,e);else if(null==e)for(n in i)i[n]=V(i[n],t.name,null);return this}for(;++l<o;)if((n=(t=a[l]).type)&&(n=O(i[n],t.name)))return n},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new I(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=new Array(n),a=0;a<n;++a)i[a]=arguments[a+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=0,n=(r=this._[t]).length;a<n;++a)r[a].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,a=r.length;i<a;++i)r[i].value.apply(e,n)}};var P="http://www.w3.org/1999/xhtml",$={svg:"http://www.w3.org/2000/svg",xhtml:P,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Y(t){var e=t+="",n=e.indexOf(":");return n>=0&&"xmlns"!==(e=t.slice(0,n))&&(t=t.slice(n+1)),$.hasOwnProperty(e)?{space:$[e],local:t}:t}function R(t){return function(){var e=this.ownerDocument,n=this.namespaceURI;return n===P&&e.documentElement.namespaceURI===P?e.createElement(t):e.createElementNS(n,t)}}function z(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function U(t){var e=Y(t);return(e.local?z:R)(e)}function H(){}function X(t){return null==t?H:function(){return this.querySelector(t)}}function W(){return[]}function j(t){return null==t?W:function(){return this.querySelectorAll(t)}}function q(t){return function(){return function(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}(t.apply(this,arguments))}}function G(t){return function(){return this.matches(t)}}function Q(t){return function(e){return e.matches(t)}}var Z=Array.prototype.find;function K(){return this.firstElementChild}var J=Array.prototype.filter;function tt(){return Array.from(this.children)}function et(t){return new Array(t.length)}function nt(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function rt(t,e,n,r,i,a){for(var l,o=0,s=e.length,u=a.length;o<u;++o)(l=e[o])?(l.__data__=a[o],r[o]=l):n[o]=new nt(t,a[o]);for(;o<s;++o)(l=e[o])&&(i[o]=l)}function it(t,e,n,r,i,a,l){var o,s,u,f=new Map,c=e.length,h=a.length,d=new Array(c);for(o=0;o<c;++o)(s=e[o])&&(d[o]=u=l.call(s,s.__data__,o,e)+"",f.has(u)?i[o]=s:f.set(u,s));for(o=0;o<h;++o)u=l.call(t,a[o],o,a)+"",(s=f.get(u))?(r[o]=s,s.__data__=a[o],f.delete(u)):n[o]=new nt(t,a[o]);for(o=0;o<c;++o)(s=e[o])&&f.get(d[o])===s&&(i[o]=s)}function at(t){return t.__data__}function lt(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function ot(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function st(t){return function(){this.removeAttribute(t)}}function ut(t){return function(){this.removeAttributeNS(t.space,t.local)}}function ft(t,e){return function(){this.setAttribute(t,e)}}function ct(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function ht(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttribute(t):this.setAttribute(t,n)}}function dt(t,e){return function(){var n=e.apply(this,arguments);null==n?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,n)}}function gt(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function pt(t){return function(){this.style.removeProperty(t)}}function yt(t,e,n){return function(){this.style.setProperty(t,e,n)}}function mt(t,e,n){return function(){var r=e.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,n)}}function vt(t,e){return t.style.getPropertyValue(e)||gt(t).getComputedStyle(t,null).getPropertyValue(e)}function xt(t){return function(){delete this[t]}}function At(t,e){return function(){this[t]=e}}function wt(t,e){return function(){var n=e.apply(this,arguments);null==n?delete this[t]:this[t]=n}}function bt(t){return t.trim().split(/^|\s+/)}function Dt(t){return t.classList||new kt(t)}function kt(t){this._node=t,this._names=bt(t.getAttribute("class")||"")}function Ct(t,e){for(var n=Dt(t),r=-1,i=e.length;++r<i;)n.add(e[r])}function Mt(t,e){for(var n=Dt(t),r=-1,i=e.length;++r<i;)n.remove(e[r])}function Tt(t){return function(){Ct(this,t)}}function _t(t){return function(){Mt(this,t)}}function Et(t,e){return function(){(e.apply(this,arguments)?Ct:Mt)(this,t)}}function Ft(){this.textContent=""}function St(t){return function(){this.textContent=t}}function Bt(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}function Nt(){this.innerHTML=""}function Lt(t){return function(){this.innerHTML=t}}function It(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}function Ot(){this.nextSibling&&this.parentNode.appendChild(this)}function Vt(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Pt(){return null}function $t(){var t=this.parentNode;t&&t.removeChild(this)}function Yt(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Rt(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function zt(t){return function(){var e=this.__on;if(e){for(var n,r=0,i=-1,a=e.length;r<a;++r)n=e[r],t.type&&n.type!==t.type||n.name!==t.name?e[++i]=n:this.removeEventListener(n.type,n.listener,n.options);++i?e.length=i:delete this.__on}}}function Ut(t,e,n){return function(){var r,i=this.__on,a=function(t){return function(e){t.call(this,e,this.__data__)}}(e);if(i)for(var l=0,o=i.length;l<o;++l)if((r=i[l]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=a,r.options=n),void(r.value=e);this.addEventListener(t.type,a,n),r={type:t.type,name:t.name,value:e,listener:a,options:n},i?i.push(r):this.__on=[r]}}function Ht(t,e,n){var r=gt(t),i=r.CustomEvent;"function"==typeof i?i=new i(e,n):(i=r.document.createEvent("Event"),n?(i.initEvent(e,n.bubbles,n.cancelable),i.detail=n.detail):i.initEvent(e,!1,!1)),t.dispatchEvent(i)}function Xt(t,e){return function(){return Ht(this,t,e)}}function Wt(t,e){return function(){return Ht(this,t,e.apply(this,arguments))}}nt.prototype={constructor:nt,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},kt.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var jt=[null];function qt(t,e){this._groups=t,this._parents=e}function Gt(){return new qt([[document.documentElement]],jt)}function Qt(t){return"string"==typeof t?new qt([[document.querySelector(t)]],[document.documentElement]):new qt([[t]],jt)}function Zt(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function Kt(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function Jt(){}qt.prototype=Gt.prototype={constructor:qt,select:function(t){"function"!=typeof t&&(t=X(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var a,l,o=e[i],s=o.length,u=r[i]=new Array(s),f=0;f<s;++f)(a=o[f])&&(l=t.call(a,a.__data__,f,o))&&("__data__"in a&&(l.__data__=a.__data__),u[f]=l);return new qt(r,this._parents)},selectAll:function(t){t="function"==typeof t?q(t):j(t);for(var e=this._groups,n=e.length,r=[],i=[],a=0;a<n;++a)for(var l,o=e[a],s=o.length,u=0;u<s;++u)(l=o[u])&&(r.push(t.call(l,l.__data__,u,o)),i.push(l));return new qt(r,i)},selectChild:function(t){return this.select(null==t?K:function(t){return function(){return Z.call(this.children,t)}}("function"==typeof t?t:Q(t)))},selectChildren:function(t){return this.selectAll(null==t?tt:function(t){return function(){return J.call(this.children,t)}}("function"==typeof t?t:Q(t)))},filter:function(t){"function"!=typeof t&&(t=G(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var a,l=e[i],o=l.length,s=r[i]=[],u=0;u<o;++u)(a=l[u])&&t.call(a,a.__data__,u,l)&&s.push(a);return new qt(r,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,at);var n=e?it:rt,r=this._parents,i=this._groups;"function"!=typeof t&&(t=function(t){return function(){return t}}(t));for(var a=i.length,l=new Array(a),o=new Array(a),s=new Array(a),u=0;u<a;++u){var f=r[u],c=i[u],h=c.length,d=lt(t.call(f,f&&f.__data__,u,r)),g=d.length,p=o[u]=new Array(g),y=l[u]=new Array(g);n(f,c,p,y,s[u]=new Array(h),d,e);for(var m,v,x=0,A=0;x<g;++x)if(m=p[x]){for(x>=A&&(A=x+1);!(v=y[A])&&++A<g;);m._next=v||null}}return(l=new qt(l,r))._enter=o,l._exit=s,l},enter:function(){return new qt(this._enter||this._groups.map(et),this._parents)},exit:function(){return new qt(this._exit||this._groups.map(et),this._parents)},join:function(t,e,n){var r=this.enter(),i=this,a=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=e&&(i=e(i))&&(i=i.selection()),null==n?a.remove():n(a),r&&i?r.merge(i).order():i},merge:function(t){for(var e=t.selection?t.selection():t,n=this._groups,r=e._groups,i=n.length,a=r.length,l=Math.min(i,a),o=new Array(i),s=0;s<l;++s)for(var u,f=n[s],c=r[s],h=f.length,d=o[s]=new Array(h),g=0;g<h;++g)(u=f[g]||c[g])&&(d[g]=u);for(;s<i;++s)o[s]=n[s];return new qt(o,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,n=t.length;++e<n;)for(var r,i=t[e],a=i.length-1,l=i[a];--a>=0;)(r=i[a])&&(l&&4^r.compareDocumentPosition(l)&&l.parentNode.insertBefore(r,l),l=r);return this},sort:function(t){function e(e,n){return e&&n?t(e.__data__,n.__data__):!e-!n}t||(t=ot);for(var n=this._groups,r=n.length,i=new Array(r),a=0;a<r;++a){for(var l,o=n[a],s=o.length,u=i[a]=new Array(s),f=0;f<s;++f)(l=o[f])&&(u[f]=l);u.sort(e)}return new qt(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r=t[e],i=0,a=r.length;i<a;++i){var l=r[i];if(l)return l}return null},size:function(){let t=0;for(const e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,n=0,r=e.length;n<r;++n)for(var i,a=e[n],l=0,o=a.length;l<o;++l)(i=a[l])&&t.call(i,i.__data__,l,a);return this},attr:function(t,e){var n=Y(t);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((null==e?n.local?ut:st:"function"==typeof e?n.local?dt:ht:n.local?ct:ft)(n,e))},style:function(t,e,n){return arguments.length>1?this.each((null==e?pt:"function"==typeof e?mt:yt)(t,e,null==n?"":n)):vt(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?xt:"function"==typeof e?wt:At)(t,e)):this.node()[t]},classed:function(t,e){var n=bt(t+"");if(arguments.length<2){for(var r=Dt(this.node()),i=-1,a=n.length;++i<a;)if(!r.contains(n[i]))return!1;return!0}return this.each(("function"==typeof e?Et:e?Tt:_t)(n,e))},text:function(t){return arguments.length?this.each(null==t?Ft:("function"==typeof t?Bt:St)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?Nt:("function"==typeof t?It:Lt)(t)):this.node().innerHTML},raise:function(){return this.each(Ot)},lower:function(){return this.each(Vt)},append:function(t){var e="function"==typeof t?t:U(t);return this.select((function(){return this.appendChild(e.apply(this,arguments))}))},insert:function(t,e){var n="function"==typeof t?t:U(t),r=null==e?Pt:"function"==typeof e?e:X(e);return this.select((function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)}))},remove:function(){return this.each($t)},clone:function(t){return this.select(t?Rt:Yt)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,n){var r,i,a=function(t){return t.trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");return n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),{type:t,name:e}}))}(t+""),l=a.length;if(!(arguments.length<2)){for(o=e?Ut:zt,r=0;r<l;++r)this.each(o(a[r],e,n));return this}var o=this.node().__on;if(o)for(var s,u=0,f=o.length;u<f;++u)for(r=0,s=o[u];r<l;++r)if((i=a[r]).type===s.type&&i.name===s.name)return s.value},dispatch:function(t,e){return this.each(("function"==typeof e?Wt:Xt)(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,n=t.length;e<n;++e)for(var r,i=t[e],a=0,l=i.length;a<l;++a)(r=i[a])&&(yield r)}};var te=.7,ee=1/te,ne="\\s*([+-]?\\d+)\\s*",re="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ie="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",ae=/^#([0-9a-f]{3,8})$/,le=new RegExp(`^rgb\\(${ne},${ne},${ne}\\)$`),oe=new RegExp(`^rgb\\(${ie},${ie},${ie}\\)$`),se=new RegExp(`^rgba\\(${ne},${ne},${ne},${re}\\)$`),ue=new RegExp(`^rgba\\(${ie},${ie},${ie},${re}\\)$`),fe=new RegExp(`^hsl\\(${re},${ie},${ie}\\)$`),ce=new RegExp(`^hsla\\(${re},${ie},${ie},${re}\\)$`),he={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function de(){return this.rgb().formatHex()}function ge(){return this.rgb().formatRgb()}function pe(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=ae.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?ye(e):3===n?new Ae(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?me(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?me(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=le.exec(t))?new Ae(e[1],e[2],e[3],1):(e=oe.exec(t))?new Ae(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=se.exec(t))?me(e[1],e[2],e[3],e[4]):(e=ue.exec(t))?me(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=fe.exec(t))?Me(e[1],e[2]/100,e[3]/100,1):(e=ce.exec(t))?Me(e[1],e[2]/100,e[3]/100,e[4]):he.hasOwnProperty(t)?ye(he[t]):"transparent"===t?new Ae(NaN,NaN,NaN,0):null}function ye(t){return new Ae(t>>16&255,t>>8&255,255&t,1)}function me(t,e,n,r){return r<=0&&(t=e=n=NaN),new Ae(t,e,n,r)}function ve(t){return t instanceof Jt||(t=pe(t)),t?new Ae((t=t.rgb()).r,t.g,t.b,t.opacity):new Ae}function xe(t,e,n,r){return 1===arguments.length?ve(t):new Ae(t,e,n,null==r?1:r)}function Ae(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function we(){return`#${Ce(this.r)}${Ce(this.g)}${Ce(this.b)}`}function be(){const t=De(this.opacity);return`${1===t?"rgb(":"rgba("}${ke(this.r)}, ${ke(this.g)}, ${ke(this.b)}${1===t?")":`, ${t})`}`}function De(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function ke(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function Ce(t){return((t=ke(t))<16?"0":"")+t.toString(16)}function Me(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new _e(t,e,n,r)}function Te(t){if(t instanceof _e)return new _e(t.h,t.s,t.l,t.opacity);if(t instanceof Jt||(t=pe(t)),!t)return new _e;if(t instanceof _e)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),a=Math.max(e,n,r),l=NaN,o=a-i,s=(a+i)/2;return o?(l=e===a?(n-r)/o+6*(n<r):n===a?(r-e)/o+2:(e-n)/o+4,o/=s<.5?a+i:2-a-i,l*=60):o=s>0&&s<1?0:l,new _e(l,o,s,t.opacity)}function _e(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function Ee(t){return(t=(t||0)%360)<0?t+360:t}function Fe(t){return Math.max(0,Math.min(1,t||0))}function Se(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}Zt(Jt,pe,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:de,formatHex:de,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Te(this).formatHsl()},formatRgb:ge,toString:ge}),Zt(Ae,xe,Kt(Jt,{brighter(t){return t=null==t?ee:Math.pow(ee,t),new Ae(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?te:Math.pow(te,t),new Ae(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Ae(ke(this.r),ke(this.g),ke(this.b),De(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:we,formatHex:we,formatHex8:function(){return`#${Ce(this.r)}${Ce(this.g)}${Ce(this.b)}${Ce(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:be,toString:be})),Zt(_e,(function(t,e,n,r){return 1===arguments.length?Te(t):new _e(t,e,n,null==r?1:r)}),Kt(Jt,{brighter(t){return t=null==t?ee:Math.pow(ee,t),new _e(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?te:Math.pow(te,t),new _e(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new Ae(Se(t>=240?t-240:t+120,i,r),Se(t,i,r),Se(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new _e(Ee(this.h),Fe(this.s),Fe(this.l),De(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=De(this.opacity);return`${1===t?"hsl(":"hsla("}${Ee(this.h)}, ${100*Fe(this.s)}%, ${100*Fe(this.l)}%${1===t?")":`, ${t})`}`}}));const Be=Math.PI/180,Ne=180/Math.PI,Le=.96422,Ie=.82521,Oe=4/29,Ve=6/29,Pe=3*Ve*Ve,$e=Ve*Ve*Ve;function Ye(t){if(t instanceof ze)return new ze(t.l,t.a,t.b,t.opacity);if(t instanceof je)return qe(t);t instanceof Ae||(t=ve(t));var e,n,r=We(t.r),i=We(t.g),a=We(t.b),l=Ue((.2225045*r+.7168786*i+.0606169*a)/1);return r===i&&i===a?e=n=l:(e=Ue((.4360747*r+.3850649*i+.1430804*a)/Le),n=Ue((.0139322*r+.0971045*i+.7141733*a)/Ie)),new ze(116*l-16,500*(e-l),200*(l-n),t.opacity)}function Re(t,e,n,r){return 1===arguments.length?Ye(t):new ze(t,e,n,null==r?1:r)}function ze(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}function Ue(t){return t>$e?Math.pow(t,1/3):t/Pe+Oe}function He(t){return t>Ve?t*t*t:Pe*(t-Oe)}function Xe(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function We(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function je(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function qe(t){if(isNaN(t.h))return new ze(t.l,0,0,t.opacity);var e=t.h*Be;return new ze(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}Zt(ze,Re,Kt(Jt,{brighter(t){return new ze(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new ze(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return new Ae(Xe(3.1338561*(e=Le*He(e))-1.6168667*(t=1*He(t))-.4906146*(n=Ie*He(n))),Xe(-.9787684*e+1.9161415*t+.033454*n),Xe(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}})),Zt(je,(function(t,e,n,r){return 1===arguments.length?function(t){if(t instanceof je)return new je(t.h,t.c,t.l,t.opacity);if(t instanceof ze||(t=Ye(t)),0===t.a&&0===t.b)return new je(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*Ne;return new je(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new je(t,e,n,null==r?1:r)}),Kt(Jt,{brighter(t){return new je(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new je(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return qe(this).rgb()}}));var Ge=t=>()=>t;function Qe(t){return 1==(t=+t)?Ze:function(e,n){return n-e?function(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}(e,n,t):Ge(isNaN(e)?n:e)}}function Ze(t,e){var n=e-t;return n?function(t,e){return function(n){return t+n*e}}(t,n):Ge(isNaN(t)?e:t)}var Ke=function t(e){var n=Qe(e);function r(t,e){var r=n((t=xe(t)).r,(e=xe(e)).r),i=n(t.g,e.g),a=n(t.b,e.b),l=Ze(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=a(e),t.opacity=l(e),t+""}}return r.gamma=t,r}(1);var Je,tn=(Je=function(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],a=t[r+1],l=r>0?t[r-1]:2*i-a,o=r<e-1?t[r+2]:2*a-i;return function(t,e,n,r,i){var a=t*t,l=a*t;return((1-3*t+3*a-l)*e+(4-6*a+3*l)*n+(1+3*t+3*a-3*l)*r+l*i)/6}((n-r/e)*e,l,i,a,o)}},function(t){var e,n,r=t.length,i=new Array(r),a=new Array(r),l=new Array(r);for(e=0;e<r;++e)n=xe(t[e]),i[e]=n.r||0,a[e]=n.g||0,l[e]=n.b||0;return i=Je(i),a=Je(a),l=Je(l),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=l(t),n+""}});function en(t,e){e||(e=[]);var n,r=t?Math.min(e.length,t.length):0,i=e.slice();return function(a){for(n=0;n<r;++n)i[n]=t[n]*(1-a)+e[n]*a;return i}}function nn(t,e){var n,r=e?e.length:0,i=t?Math.min(r,t.length):0,a=new Array(i),l=new Array(r);for(n=0;n<i;++n)a[n]=fn(t[n],e[n]);for(;n<r;++n)l[n]=e[n];return function(t){for(n=0;n<i;++n)l[n]=a[n](t);return l}}function rn(t,e){var n=new Date;return t=+t,e=+e,function(r){return n.setTime(t*(1-r)+e*r),n}}function an(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}function ln(t,e){var n,r={},i={};for(n in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)n in t?r[n]=fn(t[n],e[n]):i[n]=e[n];return function(t){for(n in r)i[n]=r[n](t);return i}}var on=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,sn=new RegExp(on.source,"g");function un(t,e){var n,r,i,a=on.lastIndex=sn.lastIndex=0,l=-1,o=[],s=[];for(t+="",e+="";(n=on.exec(t))&&(r=sn.exec(e));)(i=r.index)>a&&(i=e.slice(a,i),o[l]?o[l]+=i:o[++l]=i),(n=n[0])===(r=r[0])?o[l]?o[l]+=r:o[++l]=r:(o[++l]=null,s.push({i:l,x:an(n,r)})),a=sn.lastIndex;return a<e.length&&(i=e.slice(a),o[l]?o[l]+=i:o[++l]=i),o.length<2?s[0]?function(t){return function(e){return t(e)+""}}(s[0].x):function(t){return function(){return t}}(e):(e=s.length,function(t){for(var n,r=0;r<e;++r)o[(n=s[r]).i]=n.x(t);return o.join("")})}function fn(t,e){var n,r=typeof e;return null==e||"boolean"===r?Ge(e):("number"===r?an:"string"===r?(n=pe(e))?(e=n,Ke):un:e instanceof pe?Ke:e instanceof Date?rn:function(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}(e)?en:Array.isArray(e)?nn:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?ln:an)(t,e)}function cn(t,e){return t=+t,e=+e,function(n){return Math.round(t*(1-n)+e*n)}}var hn,dn=180/Math.PI,gn={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function pn(t,e,n,r,i,a){var l,o,s;return(l=Math.sqrt(t*t+e*e))&&(t/=l,e/=l),(s=t*n+e*r)&&(n-=t*s,r-=e*s),(o=Math.sqrt(n*n+r*r))&&(n/=o,r/=o,s/=o),t*r<e*n&&(t=-t,e=-e,s=-s,l=-l),{translateX:i,translateY:a,rotate:Math.atan2(e,t)*dn,skewX:Math.atan(s)*dn,scaleX:l,scaleY:o}}function yn(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(a,l){var o=[],s=[];return a=t(a),l=t(l),function(t,r,i,a,l,o){if(t!==i||r!==a){var s=l.push("translate(",null,e,null,n);o.push({i:s-4,x:an(t,i)},{i:s-2,x:an(r,a)})}else(i||a)&&l.push("translate("+i+e+a+n)}(a.translateX,a.translateY,l.translateX,l.translateY,o,s),function(t,e,n,a){t!==e?(t-e>180?e+=360:e-t>180&&(t+=360),a.push({i:n.push(i(n)+"rotate(",null,r)-2,x:an(t,e)})):e&&n.push(i(n)+"rotate("+e+r)}(a.rotate,l.rotate,o,s),function(t,e,n,a){t!==e?a.push({i:n.push(i(n)+"skewX(",null,r)-2,x:an(t,e)}):e&&n.push(i(n)+"skewX("+e+r)}(a.skewX,l.skewX,o,s),function(t,e,n,r,a,l){if(t!==n||e!==r){var o=a.push(i(a)+"scale(",null,",",null,")");l.push({i:o-4,x:an(t,n)},{i:o-2,x:an(e,r)})}else 1===n&&1===r||a.push(i(a)+"scale("+n+","+r+")")}(a.scaleX,a.scaleY,l.scaleX,l.scaleY,o,s),a=l=null,function(t){for(var e,n=-1,r=s.length;++n<r;)o[(e=s[n]).i]=e.x(t);return o.join("")}}}var mn=yn((function(t){const e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?gn:pn(e.a,e.b,e.c,e.d,e.e,e.f)}),"px, ","px)","deg)"),vn=yn((function(t){return null==t?gn:(hn||(hn=document.createElementNS("http://www.w3.org/2000/svg","g")),hn.setAttribute("transform",t),(t=hn.transform.baseVal.consolidate())?pn((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):gn)}),", ",")",")");function xn(t,e){var n=Ze((t=Re(t)).l,(e=Re(e)).l),r=Ze(t.a,e.a),i=Ze(t.b,e.b),a=Ze(t.opacity,e.opacity);return function(e){return t.l=n(e),t.a=r(e),t.b=i(e),t.opacity=a(e),t+""}}var An,wn,bn=0,Dn=0,kn=0,Cn=0,Mn=0,Tn=0,_n="object"==typeof performance&&performance.now?performance:Date,En="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Fn(){return Mn||(En(Sn),Mn=_n.now()+Tn)}function Sn(){Mn=0}function Bn(){this._call=this._time=this._next=null}function Nn(t,e,n){var r=new Bn;return r.restart(t,e,n),r}function Ln(){Mn=(Cn=_n.now())+Tn,bn=Dn=0;try{!function(){Fn(),++bn;for(var t,e=An;e;)(t=Mn-e._time)>=0&&e._call.call(void 0,t),e=e._next;--bn}()}finally{bn=0,function(){var t,e,n=An,r=1/0;for(;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:An=e);wn=t,On(r)}(),Mn=0}}function In(){var t=_n.now(),e=t-Cn;e>1e3&&(Tn-=e,Cn=t)}function On(t){bn||(Dn&&(Dn=clearTimeout(Dn)),t-Mn>24?(t<1/0&&(Dn=setTimeout(Ln,t-_n.now()-Tn)),kn&&(kn=clearInterval(kn))):(kn||(Cn=_n.now(),kn=setInterval(In,1e3)),bn=1,En(Ln)))}function Vn(t,e,n){var r=new Bn;return e=null==e?0:+e,r.restart((n=>{r.stop(),t(n+e)}),e,n),r}Bn.prototype=Nn.prototype={constructor:Bn,restart:function(t,e,n){if("function"!=typeof t)throw new TypeError("callback is not a function");n=(null==n?Fn():+n)+(null==e?0:+e),this._next||wn===this||(wn?wn._next=this:An=this,wn=this),this._call=t,this._time=n,On()},stop:function(){this._call&&(this._call=null,this._time=1/0,On())}};var Pn=L("start","end","cancel","interrupt"),$n=[];function Yn(t,e,n,r,i,a){var l=t.__transition;if(l){if(n in l)return}else t.__transition={};!function(t,e,n){var r,i=t.__transition;function a(t){n.state=1,n.timer.restart(l,n.delay,n.time),n.delay<=t&&l(t-n.delay)}function l(a){var u,f,c,h;if(1!==n.state)return s();for(u in i)if((h=i[u]).name===n.name){if(3===h.state)return Vn(l);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[u]):+u<e&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[u])}if(Vn((function(){3===n.state&&(n.state=4,n.timer.restart(o,n.delay,n.time),o(a))})),n.state=2,n.on.call("start",t,t.__data__,n.index,n.group),2===n.state){for(n.state=3,r=new Array(c=n.tween.length),u=0,f=-1;u<c;++u)(h=n.tween[u].value.call(t,t.__data__,n.index,n.group))&&(r[++f]=h);r.length=f+1}}function o(e){for(var i=e<n.duration?n.ease.call(null,e/n.duration):(n.timer.restart(s),n.state=5,1),a=-1,l=r.length;++a<l;)r[a].call(t,i);5===n.state&&(n.on.call("end",t,t.__data__,n.index,n.group),s())}function s(){for(var r in n.state=6,n.timer.stop(),delete i[e],i)return;delete t.__transition}i[e]=n,n.timer=Nn(a,0,n.time)}(t,n,{name:e,index:r,group:i,on:Pn,tween:$n,time:a.time,delay:a.delay,duration:a.duration,ease:a.ease,timer:null,state:0})}function Rn(t,e){var n=Un(t,e);if(n.state>0)throw new Error("too late; already scheduled");return n}function zn(t,e){var n=Un(t,e);if(n.state>3)throw new Error("too late; already running");return n}function Un(t,e){var n=t.__transition;if(!n||!(n=n[e]))throw new Error("transition not found");return n}function Hn(t,e){var n,r;return function(){var i=zn(this,t),a=i.tween;if(a!==n)for(var l=0,o=(r=n=a).length;l<o;++l)if(r[l].name===e){(r=r.slice()).splice(l,1);break}i.tween=r}}function Xn(t,e,n){var r,i;if("function"!=typeof n)throw new Error;return function(){var a=zn(this,t),l=a.tween;if(l!==r){i=(r=l).slice();for(var o={name:e,value:n},s=0,u=i.length;s<u;++s)if(i[s].name===e){i[s]=o;break}s===u&&i.push(o)}a.tween=i}}function Wn(t,e,n){var r=t._id;return t.each((function(){var t=zn(this,r);(t.value||(t.value={}))[e]=n.apply(this,arguments)})),function(t){return Un(t,r).value[e]}}function jn(t,e){var n;return("number"==typeof e?an:e instanceof pe?Ke:(n=pe(e))?(e=n,Ke):un)(t,e)}function qn(t){return function(){this.removeAttribute(t)}}function Gn(t){return function(){this.removeAttributeNS(t.space,t.local)}}function Qn(t,e,n){var r,i,a=n+"";return function(){var l=this.getAttribute(t);return l===a?null:l===r?i:i=e(r=l,n)}}function Zn(t,e,n){var r,i,a=n+"";return function(){var l=this.getAttributeNS(t.space,t.local);return l===a?null:l===r?i:i=e(r=l,n)}}function Kn(t,e,n){var r,i,a;return function(){var l,o,s=n(this);if(null!=s)return(l=this.getAttribute(t))===(o=s+"")?null:l===r&&o===i?a:(i=o,a=e(r=l,s));this.removeAttribute(t)}}function Jn(t,e,n){var r,i,a;return function(){var l,o,s=n(this);if(null!=s)return(l=this.getAttributeNS(t.space,t.local))===(o=s+"")?null:l===r&&o===i?a:(i=o,a=e(r=l,s));this.removeAttributeNS(t.space,t.local)}}function tr(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(t,e){return function(n){this.setAttributeNS(t.space,t.local,e.call(this,n))}}(t,i)),n}return i._value=e,i}function er(t,e){var n,r;function i(){var i=e.apply(this,arguments);return i!==r&&(n=(r=i)&&function(t,e){return function(n){this.setAttribute(t,e.call(this,n))}}(t,i)),n}return i._value=e,i}function nr(t,e){return function(){Rn(this,t).delay=+e.apply(this,arguments)}}function rr(t,e){return e=+e,function(){Rn(this,t).delay=e}}function ir(t,e){return function(){zn(this,t).duration=+e.apply(this,arguments)}}function ar(t,e){return e=+e,function(){zn(this,t).duration=e}}var lr=Gt.prototype.constructor;function or(t){return function(){this.style.removeProperty(t)}}var sr=0;function ur(t,e,n,r){this._groups=t,this._parents=e,this._name=n,this._id=r}function fr(){return++sr}var cr=Gt.prototype;ur.prototype={constructor:ur,select:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=X(t));for(var r=this._groups,i=r.length,a=new Array(i),l=0;l<i;++l)for(var o,s,u=r[l],f=u.length,c=a[l]=new Array(f),h=0;h<f;++h)(o=u[h])&&(s=t.call(o,o.__data__,h,u))&&("__data__"in o&&(s.__data__=o.__data__),c[h]=s,Yn(c[h],e,n,h,c,Un(o,n)));return new ur(a,this._parents,e,n)},selectAll:function(t){var e=this._name,n=this._id;"function"!=typeof t&&(t=j(t));for(var r=this._groups,i=r.length,a=[],l=[],o=0;o<i;++o)for(var s,u=r[o],f=u.length,c=0;c<f;++c)if(s=u[c]){for(var h,d=t.call(s,s.__data__,c,u),g=Un(s,n),p=0,y=d.length;p<y;++p)(h=d[p])&&Yn(h,e,n,p,d,g);a.push(d),l.push(s)}return new ur(a,l,e,n)},selectChild:cr.selectChild,selectChildren:cr.selectChildren,filter:function(t){"function"!=typeof t&&(t=G(t));for(var e=this._groups,n=e.length,r=new Array(n),i=0;i<n;++i)for(var a,l=e[i],o=l.length,s=r[i]=[],u=0;u<o;++u)(a=l[u])&&t.call(a,a.__data__,u,l)&&s.push(a);return new ur(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var e=this._groups,n=t._groups,r=e.length,i=n.length,a=Math.min(r,i),l=new Array(r),o=0;o<a;++o)for(var s,u=e[o],f=n[o],c=u.length,h=l[o]=new Array(c),d=0;d<c;++d)(s=u[d]||f[d])&&(h[d]=s);for(;o<r;++o)l[o]=e[o];return new ur(l,this._parents,this._name,this._id)},selection:function(){return new lr(this._groups,this._parents)},transition:function(){for(var t=this._name,e=this._id,n=fr(),r=this._groups,i=r.length,a=0;a<i;++a)for(var l,o=r[a],s=o.length,u=0;u<s;++u)if(l=o[u]){var f=Un(l,e);Yn(l,t,n,u,o,{time:f.time+f.delay+f.duration,delay:0,duration:f.duration,ease:f.ease})}return new ur(r,this._parents,t,n)},call:cr.call,nodes:cr.nodes,node:cr.node,size:cr.size,empty:cr.empty,each:cr.each,on:function(t,e){var n=this._id;return arguments.length<2?Un(this.node(),n).on.on(t):this.each(function(t,e,n){var r,i,a=function(t){return(t+"").trim().split(/^|\s+/).every((function(t){var e=t.indexOf(".");return e>=0&&(t=t.slice(0,e)),!t||"start"===t}))}(e)?Rn:zn;return function(){var l=a(this,t),o=l.on;o!==r&&(i=(r=o).copy()).on(e,n),l.on=i}}(n,t,e))},attr:function(t,e){var n=Y(t),r="transform"===n?vn:jn;return this.attrTween(t,"function"==typeof e?(n.local?Jn:Kn)(n,r,Wn(this,"attr."+t,e)):null==e?(n.local?Gn:qn)(n):(n.local?Zn:Qn)(n,r,e))},attrTween:function(t,e){var n="attr."+t;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==e)return this.tween(n,null);if("function"!=typeof e)throw new Error;var r=Y(t);return this.tween(n,(r.local?tr:er)(r,e))},style:function(t,e,n){var r="transform"==(t+="")?mn:jn;return null==e?this.styleTween(t,function(t,e){var n,r,i;return function(){var a=vt(this,t),l=(this.style.removeProperty(t),vt(this,t));return a===l?null:a===n&&l===r?i:i=e(n=a,r=l)}}(t,r)).on("end.style."+t,or(t)):"function"==typeof e?this.styleTween(t,function(t,e,n){var r,i,a;return function(){var l=vt(this,t),o=n(this),s=o+"";return null==o&&(this.style.removeProperty(t),s=o=vt(this,t)),l===s?null:l===r&&s===i?a:(i=s,a=e(r=l,o))}}(t,r,Wn(this,"style."+t,e))).each(function(t,e){var n,r,i,a,l="style."+e,o="end."+l;return function(){var s=zn(this,t),u=s.on,f=null==s.value[l]?a||(a=or(e)):void 0;u===n&&i===f||(r=(n=u).copy()).on(o,i=f),s.on=r}}(this._id,t)):this.styleTween(t,function(t,e,n){var r,i,a=n+"";return function(){var l=vt(this,t);return l===a?null:l===r?i:i=e(r=l,n)}}(t,r,e),n).on("end.style."+t,null)},styleTween:function(t,e,n){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw new Error;return this.tween(r,function(t,e,n){var r,i;function a(){var a=e.apply(this,arguments);return a!==i&&(r=(i=a)&&function(t,e,n){return function(r){this.style.setProperty(t,e.call(this,r),n)}}(t,a,n)),r}return a._value=e,a}(t,e,null==n?"":n))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var e=t(this);this.textContent=null==e?"":e}}(Wn(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var e="text";if(arguments.length<1)return(e=this.tween(e))&&e._value;if(null==t)return this.tween(e,null);if("function"!=typeof t)throw new Error;return this.tween(e,function(t){var e,n;function r(){var r=t.apply(this,arguments);return r!==n&&(e=(n=r)&&function(t){return function(e){this.textContent=t.call(this,e)}}(r)),e}return r._value=t,r}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var e=this.parentNode;for(var n in this.__transition)if(+n!==t)return;e&&e.removeChild(this)}}(this._id))},tween:function(t,e){var n=this._id;if(t+="",arguments.length<2){for(var r,i=Un(this.node(),n).tween,a=0,l=i.length;a<l;++a)if((r=i[a]).name===t)return r.value;return null}return this.each((null==e?Hn:Xn)(n,t,e))},delay:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?nr:rr)(e,t)):Un(this.node(),e).delay},duration:function(t){var e=this._id;return arguments.length?this.each(("function"==typeof t?ir:ar)(e,t)):Un(this.node(),e).duration},ease:function(t){var e=this._id;return arguments.length?this.each(function(t,e){if("function"!=typeof e)throw new Error;return function(){zn(this,t).ease=e}}(e,t)):Un(this.node(),e).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,e){return function(){var n=e.apply(this,arguments);if("function"!=typeof n)throw new Error;zn(this,t).ease=n}}(this._id,t))},end:function(){var t,e,n=this,r=n._id,i=n.size();return new Promise((function(a,l){var o={value:l},s={value:function(){0==--i&&a()}};n.each((function(){var n=zn(this,r),i=n.on;i!==t&&((e=(t=i).copy())._.cancel.push(o),e._.interrupt.push(o),e._.end.push(s)),n.on=e})),0===i&&a()}))},[Symbol.iterator]:cr[Symbol.iterator]};var hr={time:null,delay:0,duration:250,ease:function(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}};function dr(t,e){for(var n;!(n=t.__transition)||!(n=n[e]);)if(!(t=t.parentNode))throw new Error(`transition ${e} not found`);return n}Gt.prototype.interrupt=function(t){return this.each((function(){!function(t,e){var n,r,i,a=t.__transition,l=!0;if(a){for(i in e=null==e?null:e+"",a)(n=a[i]).name===e?(r=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(r?"interrupt":"cancel",t,t.__data__,n.index,n.group),delete a[i]):l=!1;l&&delete t.__transition}}(this,t)}))},Gt.prototype.transition=function(t){var e,n;t instanceof ur?(e=t._id,t=t._name):(e=fr(),(n=hr).time=Fn(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,a=0;a<i;++a)for(var l,o=r[a],s=o.length,u=0;u<s;++u)(l=o[u])&&Yn(l,t,e,u,o,n||dr(l,e));return new ur(r,this._parents,t,e)};const gr=Math.PI,pr=2*gr,yr=1e-6,mr=pr-yr;function vr(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=arguments[e]+t[e]}class xr{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?vr:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return vr;const n=10**e;return function(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=Math.round(arguments[e]*n)/n+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,n,r){this._append`Q${+t},${+e},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(t,e,n,r,i,a){this._append`C${+t},${+e},${+n},${+r},${this._x1=+i},${this._y1=+a}`}arcTo(t,e,n,r,i){if(t=+t,e=+e,n=+n,r=+r,(i=+i)<0)throw new Error(`negative radius: ${i}`);let a=this._x1,l=this._y1,o=n-t,s=r-e,u=a-t,f=l-e,c=u*u+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(c>yr)if(Math.abs(f*o-s*u)>yr&&i){let h=n-a,d=r-l,g=o*o+s*s,p=h*h+d*d,y=Math.sqrt(g),m=Math.sqrt(c),v=i*Math.tan((gr-Math.acos((g+c-p)/(2*y*m)))/2),x=v/m,A=v/y;Math.abs(x-1)>yr&&this._append`L${t+x*u},${e+x*f}`,this._append`A${i},${i},0,0,${+(f*h>u*d)},${this._x1=t+A*o},${this._y1=e+A*s}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,n,r,i,a){if(t=+t,e=+e,a=!!a,(n=+n)<0)throw new Error(`negative radius: ${n}`);let l=n*Math.cos(r),o=n*Math.sin(r),s=t+l,u=e+o,f=1^a,c=a?r-i:i-r;null===this._x1?this._append`M${s},${u}`:(Math.abs(this._x1-s)>yr||Math.abs(this._y1-u)>yr)&&this._append`L${s},${u}`,n&&(c<0&&(c=c%pr+pr),c>mr?this._append`A${n},${n},0,1,${f},${t-l},${e-o}A${n},${n},0,1,${f},${this._x1=s},${this._y1=u}`:c>yr&&this._append`A${n},${n},0,${+(c>=gr)},${f},${this._x1=t+n*Math.cos(i)},${this._y1=e+n*Math.sin(i)}`)}rect(t,e,n,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${n=+n}v${+r}h${-n}Z`}toString(){return this._}}function Ar(t,e){if((n=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var n,r=t.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+t.slice(n+1)]}function wr(t){return(t=Ar(Math.abs(t)))?t[1]:NaN}var br,Dr=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function kr(t){if(!(e=Dr.exec(t)))throw new Error("invalid format: "+t);var e;return new Cr({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function Cr(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Mr(t,e){var n=Ar(t,e);if(!n)return t+"";var r=n[0],i=n[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}kr.prototype=Cr.prototype,Cr.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var Tr={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>Mr(100*t,e),r:Mr,s:function(t,e){var n=Ar(t,e);if(!n)return t+"";var r=n[0],i=n[1],a=i-(br=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,l=r.length;return a===l?r:a>l?r+new Array(a-l+1).join("0"):a>0?r.slice(0,a)+"."+r.slice(a):"0."+new Array(1-a).join("0")+Ar(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function _r(t){return t}var Er,Fr,Sr,Br=Array.prototype.map,Nr=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Lr(t){var e,n,r=void 0===t.grouping||void 0===t.thousands?_r:(e=Br.call(t.grouping,Number),n=t.thousands+"",function(t,r){for(var i=t.length,a=[],l=0,o=e[0],s=0;i>0&&o>0&&(s+o+1>r&&(o=Math.max(1,r-s)),a.push(t.substring(i-=o,i+o)),!((s+=o+1)>r));)o=e[l=(l+1)%e.length];return a.reverse().join(n)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",l=void 0===t.decimal?".":t.decimal+"",o=void 0===t.numerals?_r:function(t){return function(e){return e.replace(/[0-9]/g,(function(e){return t[+e]}))}}(Br.call(t.numerals,String)),s=void 0===t.percent?"%":t.percent+"",u=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function c(t){var e=(t=kr(t)).fill,n=t.align,c=t.sign,h=t.symbol,d=t.zero,g=t.width,p=t.comma,y=t.precision,m=t.trim,v=t.type;"n"===v?(p=!0,v="g"):Tr[v]||(void 0===y&&(y=12),m=!0,v="g"),(d||"0"===e&&"="===n)&&(d=!0,e="0",n="=");var x="$"===h?i:"#"===h&&/[boxX]/.test(v)?"0"+v.toLowerCase():"",A="$"===h?a:/[%p]/.test(v)?s:"",w=Tr[v],b=/[defgprs%]/.test(v);function D(t){var i,a,s,h=x,D=A;if("c"===v)D=w(t)+D,t="";else{var k=(t=+t)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),y),m&&(t=function(t){t:for(var e,n=t.length,r=1,i=-1;r<n;++r)switch(t[r]){case".":i=e=r;break;case"0":0===i&&(i=r),e=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),k&&0==+t&&"+"!==c&&(k=!1),h=(k?"("===c?c:u:"-"===c||"("===c?"":c)+h,D=("s"===v?Nr[8+br/3]:"")+D+(k&&"("===c?")":""),b)for(i=-1,a=t.length;++i<a;)if(48>(s=t.charCodeAt(i))||s>57){D=(46===s?l+t.slice(i+1):t.slice(i))+D,t=t.slice(0,i);break}}p&&!d&&(t=r(t,1/0));var C=h.length+t.length+D.length,M=C<g?new Array(g-C+1).join(e):"";switch(p&&d&&(t=r(M+t,M.length?g-D.length:1/0),M=""),n){case"<":t=h+t+D+M;break;case"=":t=h+M+t+D;break;case"^":t=M.slice(0,C=M.length>>1)+h+t+D+M.slice(C);break;default:t=M+h+t+D}return o(t)}return y=void 0===y?6:/[gprs]/.test(v)?Math.max(1,Math.min(21,y)):Math.max(0,Math.min(20,y)),D.toString=function(){return t+""},D}return{format:c,formatPrefix:function(t,e){var n=c(((t=kr(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(wr(e)/3))),i=Math.pow(10,-r),a=Nr[8+r/3];return function(t){return n(i*t)+a}}}}function Ir(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}Er=Lr({thousands:",",grouping:[3],currency:["$",""]}),Fr=Er.format,Sr=Er.formatPrefix;const Or=Symbol("implicit");function Vr(){var t=new l,e=[],n=[],r=Or;function i(i){let a=t.get(i);if(void 0===a){if(r!==Or)return r;t.set(i,a=e.push(i)-1)}return n[a%n.length]}return i.domain=function(n){if(!arguments.length)return e.slice();e=[],t=new l;for(const r of n)t.has(r)||t.set(r,e.push(r)-1);return i},i.range=function(t){return arguments.length?(n=Array.from(t),i):n.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return Vr(e,n).unknown(r)},Ir.apply(i,arguments),i}function Pr(t){return+t}var $r=[0,1];function Yr(t){return t}function Rr(t,e){return(e-=t=+t)?function(n){return(n-t)/e}:function(t){return function(){return t}}(isNaN(e)?NaN:.5)}function zr(t,e,n){var r=t[0],i=t[1],a=e[0],l=e[1];return i<r?(r=Rr(i,r),a=n(l,a)):(r=Rr(r,i),a=n(a,l)),function(t){return a(r(t))}}function Ur(t,e,n){var r=Math.min(t.length,e.length)-1,i=new Array(r),l=new Array(r),o=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++o<r;)i[o]=Rr(t[o],t[o+1]),l[o]=n(e[o],e[o+1]);return function(e){var n=a(t,e,1,r)-1;return l[n](i[n](e))}}function Hr(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function Xr(){var t,e,n,r,i,a,l=$r,o=$r,s=fn,u=Yr;function f(){var t,e,n,s=Math.min(l.length,o.length);return u!==Yr&&(t=l[0],e=l[s-1],t>e&&(n=t,t=e,e=n),u=function(n){return Math.max(t,Math.min(e,n))}),r=s>2?Ur:zr,i=a=null,c}function c(e){return null==e||isNaN(e=+e)?n:(i||(i=r(l.map(t),o,s)))(t(u(e)))}return c.invert=function(n){return u(e((a||(a=r(o,l.map(t),an)))(n)))},c.domain=function(t){return arguments.length?(l=Array.from(t,Pr),f()):l.slice()},c.range=function(t){return arguments.length?(o=Array.from(t),f()):o.slice()},c.rangeRound=function(t){return o=Array.from(t),s=cn,f()},c.clamp=function(t){return arguments.length?(u=!!t||Yr,f()):u!==Yr},c.interpolate=function(t){return arguments.length?(s=t,f()):s},c.unknown=function(t){return arguments.length?(n=t,c):n},function(n,r){return t=n,e=r,f()}}function Wr(){return Xr()(Yr,Yr)}function jr(t,e,n,r){var i,a=p(t,e,n);switch((r=kr(null==r?",f":r)).type){case"s":var l=Math.max(Math.abs(t),Math.abs(e));return null!=r.precision||isNaN(i=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(wr(e)/3)))-wr(Math.abs(t)))}(a,l))||(r.precision=i),Sr(r,l);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,wr(e)-wr(t))+1}(a,Math.max(Math.abs(t),Math.abs(e))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=function(t){return Math.max(0,-wr(Math.abs(t)))}(a))||(r.precision=i-2*("%"===r.type))}return Fr(r)}function qr(t){var e=t.domain;return t.ticks=function(t){var n=e();return function(t,e,n){if(!((n=+n)>0))return[];if((t=+t)==(e=+e))return[t];const r=e<t,[i,a,l]=r?d(e,t,n):d(t,e,n);if(!(a>=i))return[];const o=a-i+1,s=new Array(o);if(r)if(l<0)for(let t=0;t<o;++t)s[t]=(a-t)/-l;else for(let t=0;t<o;++t)s[t]=(a-t)*l;else if(l<0)for(let t=0;t<o;++t)s[t]=(i+t)/-l;else for(let t=0;t<o;++t)s[t]=(i+t)*l;return s}(n[0],n[n.length-1],null==t?10:t)},t.tickFormat=function(t,n){var r=e();return jr(r[0],r[r.length-1],null==t?10:t,n)},t.nice=function(n){null==n&&(n=10);var r,i,a=e(),l=0,o=a.length-1,s=a[l],u=a[o],f=10;for(u<s&&(i=s,s=u,u=i,i=l,l=o,o=i);f-- >0;){if((i=g(s,u,n))===r)return a[l]=s,a[o]=u,e(a);if(i>0)s=Math.floor(s/i)*i,u=Math.ceil(u/i)*i;else{if(!(i<0))break;s=Math.ceil(s*i)/i,u=Math.floor(u*i)/i}r=i}return t},t}function Gr(){var t=Wr();return t.copy=function(){return Hr(t,Gr())},Ir.apply(t,arguments),qr(t)}const Qr=new Date,Zr=new Date;function Kr(t,e,n,r){function i(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=n=>(t(n=new Date(n-1)),e(n,1),t(n),n),i.round=t=>{const e=i(t),n=i.ceil(t);return t-e<n-t?e:n},i.offset=(t,n)=>(e(t=new Date(+t),null==n?1:Math.floor(n)),t),i.range=(n,r,a)=>{const l=[];if(n=i.ceil(n),a=null==a?1:Math.floor(a),!(n<r&&a>0))return l;let o;do{l.push(o=new Date(+n)),e(n,a),t(n)}while(o<n&&n<r);return l},i.filter=n=>Kr((e=>{if(e>=e)for(;t(e),!n(e);)e.setTime(e-1)}),((t,r)=>{if(t>=t)if(r<0)for(;++r<=0;)for(;e(t,-1),!n(t););else for(;--r>=0;)for(;e(t,1),!n(t););})),n&&(i.count=(e,r)=>(Qr.setTime(+e),Zr.setTime(+r),t(Qr),t(Zr),Math.floor(n(Qr,Zr))),i.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?i.filter(r?e=>r(e)%t==0:e=>i.count(0,e)%t==0):i:null)),i}const Jr=Kr((()=>{}),((t,e)=>{t.setTime(+t+e)}),((t,e)=>e-t));Jr.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?Kr((e=>{e.setTime(Math.floor(e/t)*t)}),((e,n)=>{e.setTime(+e+n*t)}),((e,n)=>(n-e)/t)):Jr:null),Jr.range;const ti=1e3,ei=6e4,ni=36e5,ri=864e5,ii=6048e5,ai=2592e6,li=31536e6,oi=Kr((t=>{t.setTime(t-t.getMilliseconds())}),((t,e)=>{t.setTime(+t+e*ti)}),((t,e)=>(e-t)/ti),(t=>t.getUTCSeconds()));oi.range;const si=Kr((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*ti)}),((t,e)=>{t.setTime(+t+e*ei)}),((t,e)=>(e-t)/ei),(t=>t.getMinutes()));si.range;const ui=Kr((t=>{t.setUTCSeconds(0,0)}),((t,e)=>{t.setTime(+t+e*ei)}),((t,e)=>(e-t)/ei),(t=>t.getUTCMinutes()));ui.range;const fi=Kr((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*ti-t.getMinutes()*ei)}),((t,e)=>{t.setTime(+t+e*ni)}),((t,e)=>(e-t)/ni),(t=>t.getHours()));fi.range;const ci=Kr((t=>{t.setUTCMinutes(0,0,0)}),((t,e)=>{t.setTime(+t+e*ni)}),((t,e)=>(e-t)/ni),(t=>t.getUTCHours()));ci.range;const hi=Kr((t=>t.setHours(0,0,0,0)),((t,e)=>t.setDate(t.getDate()+e)),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*ei)/ri),(t=>t.getDate()-1));hi.range;const di=Kr((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/ri),(t=>t.getUTCDate()-1));di.range;const gi=Kr((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/ri),(t=>Math.floor(t/ri)));function pi(t){return Kr((e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)}),((t,e)=>{t.setDate(t.getDate()+7*e)}),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*ei)/ii))}gi.range;const yi=pi(0),mi=pi(1),vi=pi(2),xi=pi(3),Ai=pi(4),wi=pi(5),bi=pi(6);function Di(t){return Kr((e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)}),((t,e)=>(e-t)/ii))}yi.range,mi.range,vi.range,xi.range,Ai.range,wi.range,bi.range;const ki=Di(0),Ci=Di(1),Mi=Di(2),Ti=Di(3),_i=Di(4),Ei=Di(5),Fi=Di(6);ki.range,Ci.range,Mi.range,Ti.range,_i.range,Ei.range,Fi.range;const Si=Kr((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,e)=>{t.setMonth(t.getMonth()+e)}),((t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear())),(t=>t.getMonth()));Si.range;const Bi=Kr((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)}),((t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth()));Bi.range;const Ni=Kr((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,e)=>{t.setFullYear(t.getFullYear()+e)}),((t,e)=>e.getFullYear()-t.getFullYear()),(t=>t.getFullYear()));Ni.every=t=>isFinite(t=Math.floor(t))&&t>0?Kr((e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,n)=>{e.setFullYear(e.getFullYear()+n*t)})):null,Ni.range;const Li=Kr((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)}),((t,e)=>e.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));Li.every=t=>isFinite(t=Math.floor(t))&&t>0?Kr((e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)})):null,Li.range;const[Ii,Oi]=function(t,e,n,i,a,l){const o=[[oi,1,ti],[oi,5,5e3],[oi,15,15e3],[oi,30,3e4],[l,1,ei],[l,5,3e5],[l,15,9e5],[l,30,18e5],[a,1,ni],[a,3,108e5],[a,6,216e5],[a,12,432e5],[i,1,ri],[i,2,1728e5],[n,1,ii],[e,1,ai],[e,3,7776e6],[t,1,li]];function s(e,n,i){const a=Math.abs(n-e)/i,l=r((([,,t])=>t)).right(o,a);if(l===o.length)return t.every(p(e/li,n/li,i));if(0===l)return Jr.every(Math.max(p(e,n,i),1));const[s,u]=o[a/o[l-1][2]<o[l][2]/a?l-1:l];return s.every(u)}return[function(t,e,n){const r=e<t;r&&([t,e]=[e,t]);const i=n&&"function"==typeof n.range?n:s(t,e,n),a=i?i.range(t,+e+1):[];return r?a.reverse():a},s]}(Ni,Si,yi,hi,fi,si);function Vi(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Pi(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function $i(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}var Yi,Ri,zi={"-":"",_:" ",0:"0"},Ui=/^\s*\d+/,Hi=/^%/,Xi=/[\\^$*+?|[\]().{}]/g;function Wi(t,e,n){var r=t<0?"-":"",i=(r?-t:t)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(e)+i:i)}function ji(t){return t.replace(Xi,"\\$&")}function qi(t){return new RegExp("^(?:"+t.map(ji).join("|")+")","i")}function Gi(t){return new Map(t.map(((t,e)=>[t.toLowerCase(),e])))}function Qi(t,e,n){var r=Ui.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function Zi(t,e,n){var r=Ui.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function Ki(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function Ji(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function ta(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function ea(t,e,n){var r=Ui.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function na(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function ra(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function ia(t,e,n){var r=Ui.exec(e.slice(n,n+1));return r?(t.q=3*r[0]-3,n+r[0].length):-1}function aa(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function la(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function oa(t,e,n){var r=Ui.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function sa(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function ua(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function fa(t,e,n){var r=Ui.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function ca(t,e,n){var r=Ui.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function ha(t,e,n){var r=Ui.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function da(t,e,n){var r=Hi.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function ga(t,e,n){var r=Ui.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function pa(t,e,n){var r=Ui.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function ya(t,e){return Wi(t.getDate(),e,2)}function ma(t,e){return Wi(t.getHours(),e,2)}function va(t,e){return Wi(t.getHours()%12||12,e,2)}function xa(t,e){return Wi(1+hi.count(Ni(t),t),e,3)}function Aa(t,e){return Wi(t.getMilliseconds(),e,3)}function wa(t,e){return Aa(t,e)+"000"}function ba(t,e){return Wi(t.getMonth()+1,e,2)}function Da(t,e){return Wi(t.getMinutes(),e,2)}function ka(t,e){return Wi(t.getSeconds(),e,2)}function Ca(t){var e=t.getDay();return 0===e?7:e}function Ma(t,e){return Wi(yi.count(Ni(t)-1,t),e,2)}function Ta(t){var e=t.getDay();return e>=4||0===e?Ai(t):Ai.ceil(t)}function _a(t,e){return t=Ta(t),Wi(Ai.count(Ni(t),t)+(4===Ni(t).getDay()),e,2)}function Ea(t){return t.getDay()}function Fa(t,e){return Wi(mi.count(Ni(t)-1,t),e,2)}function Sa(t,e){return Wi(t.getFullYear()%100,e,2)}function Ba(t,e){return Wi((t=Ta(t)).getFullYear()%100,e,2)}function Na(t,e){return Wi(t.getFullYear()%1e4,e,4)}function La(t,e){var n=t.getDay();return Wi((t=n>=4||0===n?Ai(t):Ai.ceil(t)).getFullYear()%1e4,e,4)}function Ia(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+Wi(e/60|0,"0",2)+Wi(e%60,"0",2)}function Oa(t,e){return Wi(t.getUTCDate(),e,2)}function Va(t,e){return Wi(t.getUTCHours(),e,2)}function Pa(t,e){return Wi(t.getUTCHours()%12||12,e,2)}function $a(t,e){return Wi(1+di.count(Li(t),t),e,3)}function Ya(t,e){return Wi(t.getUTCMilliseconds(),e,3)}function Ra(t,e){return Ya(t,e)+"000"}function za(t,e){return Wi(t.getUTCMonth()+1,e,2)}function Ua(t,e){return Wi(t.getUTCMinutes(),e,2)}function Ha(t,e){return Wi(t.getUTCSeconds(),e,2)}function Xa(t){var e=t.getUTCDay();return 0===e?7:e}function Wa(t,e){return Wi(ki.count(Li(t)-1,t),e,2)}function ja(t){var e=t.getUTCDay();return e>=4||0===e?_i(t):_i.ceil(t)}function qa(t,e){return t=ja(t),Wi(_i.count(Li(t),t)+(4===Li(t).getUTCDay()),e,2)}function Ga(t){return t.getUTCDay()}function Qa(t,e){return Wi(Ci.count(Li(t)-1,t),e,2)}function Za(t,e){return Wi(t.getUTCFullYear()%100,e,2)}function Ka(t,e){return Wi((t=ja(t)).getUTCFullYear()%100,e,2)}function Ja(t,e){return Wi(t.getUTCFullYear()%1e4,e,4)}function tl(t,e){var n=t.getUTCDay();return Wi((t=n>=4||0===n?_i(t):_i.ceil(t)).getUTCFullYear()%1e4,e,4)}function el(){return"+0000"}function nl(){return"%"}function rl(t){return+t}function il(t){return Math.floor(+t/1e3)}function al(t){return new Date(t)}function ll(t){return t instanceof Date?+t:+new Date(+t)}function ol(t,e,n,r,i,a,l,o,s,u){var f=Wr(),c=f.invert,h=f.domain,d=u(".%L"),g=u(":%S"),p=u("%I:%M"),y=u("%I %p"),m=u("%a %d"),v=u("%b %d"),x=u("%B"),A=u("%Y");function w(t){return(s(t)<t?d:o(t)<t?g:l(t)<t?p:a(t)<t?y:r(t)<t?i(t)<t?m:v:n(t)<t?x:A)(t)}return f.invert=function(t){return new Date(c(t))},f.domain=function(t){return arguments.length?h(Array.from(t,ll)):h().map(al)},f.ticks=function(e){var n=h();return t(n[0],n[n.length-1],null==e?10:e)},f.tickFormat=function(t,e){return null==e?w:u(e)},f.nice=function(t){var n=h();return t&&"function"==typeof t.range||(t=e(n[0],n[n.length-1],null==t?10:t)),t?h(function(t,e){var n,r=0,i=(t=t.slice()).length-1,a=t[r],l=t[i];return l<a&&(n=r,r=i,i=n,n=a,a=l,l=n),t[r]=e.floor(a),t[i]=e.ceil(l),t}(n,t)):f},f.copy=function(){return Hr(f,ol(t,e,n,r,i,a,l,o,s,u))},f}function sl(){return Ir.apply(ol(Ii,Oi,Ni,Si,yi,hi,fi,si,oi,Ri).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}!function(t){Yi=function(t){var e=t.dateTime,n=t.date,r=t.time,i=t.periods,a=t.days,l=t.shortDays,o=t.months,s=t.shortMonths,u=qi(i),f=Gi(i),c=qi(a),h=Gi(a),d=qi(l),g=Gi(l),p=qi(o),y=Gi(o),m=qi(s),v=Gi(s),x={a:function(t){return l[t.getDay()]},A:function(t){return a[t.getDay()]},b:function(t){return s[t.getMonth()]},B:function(t){return o[t.getMonth()]},c:null,d:ya,e:ya,f:wa,g:Ba,G:La,H:ma,I:va,j:xa,L:Aa,m:ba,M:Da,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rl,s:il,S:ka,u:Ca,U:Ma,V:_a,w:Ea,W:Fa,x:null,X:null,y:Sa,Y:Na,Z:Ia,"%":nl},A={a:function(t){return l[t.getUTCDay()]},A:function(t){return a[t.getUTCDay()]},b:function(t){return s[t.getUTCMonth()]},B:function(t){return o[t.getUTCMonth()]},c:null,d:Oa,e:Oa,f:Ra,g:Ka,G:tl,H:Va,I:Pa,j:$a,L:Ya,m:za,M:Ua,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rl,s:il,S:Ha,u:Xa,U:Wa,V:qa,w:Ga,W:Qa,x:null,X:null,y:Za,Y:Ja,Z:el,"%":nl},w={a:function(t,e,n){var r=d.exec(e.slice(n));return r?(t.w=g.get(r[0].toLowerCase()),n+r[0].length):-1},A:function(t,e,n){var r=c.exec(e.slice(n));return r?(t.w=h.get(r[0].toLowerCase()),n+r[0].length):-1},b:function(t,e,n){var r=m.exec(e.slice(n));return r?(t.m=v.get(r[0].toLowerCase()),n+r[0].length):-1},B:function(t,e,n){var r=p.exec(e.slice(n));return r?(t.m=y.get(r[0].toLowerCase()),n+r[0].length):-1},c:function(t,n,r){return k(t,e,n,r)},d:la,e:la,f:ha,g:na,G:ea,H:sa,I:sa,j:oa,L:ca,m:aa,M:ua,p:function(t,e,n){var r=u.exec(e.slice(n));return r?(t.p=f.get(r[0].toLowerCase()),n+r[0].length):-1},q:ia,Q:ga,s:pa,S:fa,u:Zi,U:Ki,V:Ji,w:Qi,W:ta,x:function(t,e,r){return k(t,n,e,r)},X:function(t,e,n){return k(t,r,e,n)},y:na,Y:ea,Z:ra,"%":da};function b(t,e){return function(n){var r,i,a,l=[],o=-1,s=0,u=t.length;for(n instanceof Date||(n=new Date(+n));++o<u;)37===t.charCodeAt(o)&&(l.push(t.slice(s,o)),null!=(i=zi[r=t.charAt(++o)])?r=t.charAt(++o):i="e"===r?" ":"0",(a=e[r])&&(r=a(n,i)),l.push(r),s=o+1);return l.push(t.slice(s,o)),l.join("")}}function D(t,e){return function(n){var r,i,a=$i(1900,void 0,1);if(k(a,t,n+="",0)!=n.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(e&&!("Z"in a)&&(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(i=(r=Pi($i(a.y,0,1))).getUTCDay(),r=i>4||0===i?Ci.ceil(r):Ci(r),r=di.offset(r,7*(a.V-1)),a.y=r.getUTCFullYear(),a.m=r.getUTCMonth(),a.d=r.getUTCDate()+(a.w+6)%7):(i=(r=Vi($i(a.y,0,1))).getDay(),r=i>4||0===i?mi.ceil(r):mi(r),r=hi.offset(r,7*(a.V-1)),a.y=r.getFullYear(),a.m=r.getMonth(),a.d=r.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:"W"in a?1:0),i="Z"in a?Pi($i(a.y,0,1)).getUTCDay():Vi($i(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,Pi(a)):Vi(a)}}function k(t,e,n,r){for(var i,a,l=0,o=e.length,s=n.length;l<o;){if(r>=s)return-1;if(37===(i=e.charCodeAt(l++))){if(i=e.charAt(l++),!(a=w[i in zi?e.charAt(l++):i])||(r=a(t,n,r))<0)return-1}else if(i!=n.charCodeAt(r++))return-1}return r}return x.x=b(n,x),x.X=b(r,x),x.c=b(e,x),A.x=b(n,A),A.X=b(r,A),A.c=b(e,A),{format:function(t){var e=b(t+="",x);return e.toString=function(){return t},e},parse:function(t){var e=D(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=b(t+="",A);return e.toString=function(){return t},e},utcParse:function(t){var e=D(t+="",!0);return e.toString=function(){return t},e}}}(t),Ri=Yi.format,Yi.parse,Yi.utcFormat,Yi.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var ul=new Array(3).concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map((function(t){for(var e=t.length/6|0,n=new Array(e),r=0;r<e;)n[r]="#"+t.slice(6*r,6*++r);return n}));function fl(t){return function(){return t}}(t=>{tn(t[t.length-1])})(ul);const cl=Math.abs,hl=Math.atan2,dl=Math.cos,gl=Math.max,pl=Math.min,yl=Math.sin,ml=Math.sqrt,vl=1e-12,xl=Math.PI,Al=xl/2,wl=2*xl;function bl(t){return t>=1?Al:t<=-1?-Al:Math.asin(t)}function Dl(t){let e=3;return t.digits=function(n){if(!arguments.length)return e;if(null==n)e=null;else{const t=Math.floor(n);if(!(t>=0))throw new RangeError(`invalid digits: ${n}`);e=t}return t},()=>new xr(e)}function kl(t){return t.innerRadius}function Cl(t){return t.outerRadius}function Ml(t){return t.startAngle}function Tl(t){return t.endAngle}function _l(t){return t&&t.padAngle}function El(t,e,n,r,i,a,l){var o=t-n,s=e-r,u=(l?a:-a)/ml(o*o+s*s),f=u*s,c=-u*o,h=t+f,d=e+c,g=n+f,p=r+c,y=(h+g)/2,m=(d+p)/2,v=g-h,x=p-d,A=v*v+x*x,w=i-a,b=h*p-g*d,D=(x<0?-1:1)*ml(gl(0,w*w*A-b*b)),k=(b*x-v*D)/A,C=(-b*v-x*D)/A,M=(b*x+v*D)/A,T=(-b*v+x*D)/A,_=k-y,E=C-m,F=M-y,S=T-m;return _*_+E*E>F*F+S*S&&(k=M,C=T),{cx:k,cy:C,x01:-f,y01:-c,x11:k*(i/w-1),y11:C*(i/w-1)}}function Fl(){var t=kl,e=Cl,n=fl(0),r=null,i=Ml,a=Tl,l=_l,o=null,s=Dl(u);function u(){var u,f,c=+t.apply(this,arguments),h=+e.apply(this,arguments),d=i.apply(this,arguments)-Al,g=a.apply(this,arguments)-Al,p=cl(g-d),y=g>d;if(o||(o=u=s()),h<c&&(f=h,h=c,c=f),h>vl)if(p>wl-vl)o.moveTo(h*dl(d),h*yl(d)),o.arc(0,0,h,d,g,!y),c>vl&&(o.moveTo(c*dl(g),c*yl(g)),o.arc(0,0,c,g,d,y));else{var m,v,x=d,A=g,w=d,b=g,D=p,k=p,C=l.apply(this,arguments)/2,M=C>vl&&(r?+r.apply(this,arguments):ml(c*c+h*h)),T=pl(cl(h-c)/2,+n.apply(this,arguments)),_=T,E=T;if(M>vl){var F=bl(M/c*yl(C)),S=bl(M/h*yl(C));(D-=2*F)>vl?(w+=F*=y?1:-1,b-=F):(D=0,w=b=(d+g)/2),(k-=2*S)>vl?(x+=S*=y?1:-1,A-=S):(k=0,x=A=(d+g)/2)}var B=h*dl(x),N=h*yl(x),L=c*dl(b),I=c*yl(b);if(T>vl){var O,V=h*dl(A),P=h*yl(A),$=c*dl(w),Y=c*yl(w);if(p<xl)if(O=function(t,e,n,r,i,a,l,o){var s=n-t,u=r-e,f=l-i,c=o-a,h=c*s-f*u;if(!(h*h<vl))return[t+(h=(f*(e-a)-c*(t-i))/h)*s,e+h*u]}(B,N,$,Y,V,P,L,I)){var R=B-O[0],z=N-O[1],U=V-O[0],H=P-O[1],X=1/yl(function(t){return t>1?0:t<-1?xl:Math.acos(t)}((R*U+z*H)/(ml(R*R+z*z)*ml(U*U+H*H)))/2),W=ml(O[0]*O[0]+O[1]*O[1]);_=pl(T,(c-W)/(X-1)),E=pl(T,(h-W)/(X+1))}else _=E=0}k>vl?E>vl?(m=El($,Y,B,N,h,E,y),v=El(V,P,L,I,h,E,y),o.moveTo(m.cx+m.x01,m.cy+m.y01),E<T?o.arc(m.cx,m.cy,E,hl(m.y01,m.x01),hl(v.y01,v.x01),!y):(o.arc(m.cx,m.cy,E,hl(m.y01,m.x01),hl(m.y11,m.x11),!y),o.arc(0,0,h,hl(m.cy+m.y11,m.cx+m.x11),hl(v.cy+v.y11,v.cx+v.x11),!y),o.arc(v.cx,v.cy,E,hl(v.y11,v.x11),hl(v.y01,v.x01),!y))):(o.moveTo(B,N),o.arc(0,0,h,x,A,!y)):o.moveTo(B,N),c>vl&&D>vl?_>vl?(m=El(L,I,V,P,c,-_,y),v=El(B,N,$,Y,c,-_,y),o.lineTo(m.cx+m.x01,m.cy+m.y01),_<T?o.arc(m.cx,m.cy,_,hl(m.y01,m.x01),hl(v.y01,v.x01),!y):(o.arc(m.cx,m.cy,_,hl(m.y01,m.x01),hl(m.y11,m.x11),!y),o.arc(0,0,c,hl(m.cy+m.y11,m.cx+m.x11),hl(v.cy+v.y11,v.cx+v.x11),y),o.arc(v.cx,v.cy,_,hl(v.y11,v.x11),hl(v.y01,v.x01),!y))):o.arc(0,0,c,b,w,y):o.lineTo(L,I)}else o.moveTo(0,0);if(o.closePath(),u)return o=null,u+""||null}return u.centroid=function(){var n=(+t.apply(this,arguments)+ +e.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +a.apply(this,arguments))/2-xl/2;return[dl(r)*n,yl(r)*n]},u.innerRadius=function(e){return arguments.length?(t="function"==typeof e?e:fl(+e),u):t},u.outerRadius=function(t){return arguments.length?(e="function"==typeof t?t:fl(+t),u):e},u.cornerRadius=function(t){return arguments.length?(n="function"==typeof t?t:fl(+t),u):n},u.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:fl(+t),u):r},u.startAngle=function(t){return arguments.length?(i="function"==typeof t?t:fl(+t),u):i},u.endAngle=function(t){return arguments.length?(a="function"==typeof t?t:fl(+t),u):a},u.padAngle=function(t){return arguments.length?(l="function"==typeof t?t:fl(+t),u):l},u.context=function(t){return arguments.length?(o=null==t?null:t,u):o},u}function Sl(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Bl(t){this._context=t}function Nl(t){return new Bl(t)}function Ll(t){return t[0]}function Il(t){return t[1]}function Ol(t,e){return e<t?-1:e>t?1:e>=t?0:NaN}function Vl(t){return t}function Pl(t,e,n){this.k=t,this.x=e,this.y=n}var $l,Yl,Rl;Bl.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},Pl.prototype={constructor:Pl,scale:function(t){return 1===t?this:new Pl(this.k*t,this.x,this.y)},translate:function(t,e){return 0===t&0===e?this:new Pl(this.k,this.x+this.k*t,this.y+this.k*e)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}},Pl.prototype,function(t){t[t.Tag=0]="Tag",t[t.Frontmatter=1]="Frontmatter",t[t.Wiki=2]="Wiki",t[t.WikiLink=3]="WikiLink",t[t.WikiDisplay=4]="WikiDisplay",t[t.Text=5]="Text",t[t.dvField=6]="dvField",t[t.Table=7]="Table",t[t.FileMeta=8]="FileMeta",t[t.Task=9]="Task",t[t.TaskDone=10]="TaskDone",t[t.TaskNotDone=11]="TaskNotDone"}($l||($l={})),function(t){t[t.Line=0]="Line",t[t.Bar=1]="Bar",t[t.Pie=2]="Pie",t[t.Radar=3]="Radar",t[t.Summary=4]="Summary",t[t.Table=5]="Table",t[t.Month=6]="Month",t[t.Heatmap=7]="Heatmap",t[t.Bullet=8]="Bullet",t[t.Unknown=9]="Unknown"}(Yl||(Yl={})),function(t){t[t.Number=0]="Number",t[t.Int=1]="Int",t[t.Date=2]="Date",t[t.Time=3]="Time",t[t.DateTime=4]="DateTime",t[t.String=5]="String"}(Rl||(Rl={}));class zl{constructor(t,e){this.date=t,this.value=e}}class Ul{constructor(t,e,n){if(this.type=e,this.target=n,this.separator="",this.id=t,this.accessor=-1,this.accessor1=-1,this.accessor2=-1,this.valueType=Rl.Number,this.usedAsXDataset=!1,this.numTargets=0,e===$l.Table){let t,e=new RegExp("\\[(?<accessor>[0-9]+)\\]\\[(?<accessor1>[0-9]+)\\](\\[(?<accessor2>[0-9]+)\\])?","gm");for(;t=e.exec(n);)if(void 0!==t.groups.accessor){let r=parseFloat(t.groups.accessor);if(Number.isNumber(r)&&void 0!==t.groups.accessor1){let i=parseFloat(t.groups.accessor1);if(Number.isNumber(i)){let a;void 0!==t.groups.accessor2&&(a=parseFloat(t.groups.accessor2)),this.accessor=r,this.accessor1=i,Number.isNumber(a)&&(this.accessor2=a),this.parentTarget=n.replace(e,"")}break}}}else{let t,e=new RegExp("\\[(?<accessor>[0-9]+)\\]","gm");for(;t=e.exec(n);)if(void 0!==t.groups.accessor){let r=parseFloat(t.groups.accessor);Number.isNumber(r)&&(this.accessor=r,this.parentTarget=n.replace(e,""));break}}}equalTo(t){return this.type===t.type&&this.target===t.target}getType(){return this.type}getTarget(){return this.target}getParentTarget(){return this.parentTarget}getId(){return this.id}getAccessor(t=0){switch(t){case 0:return this.accessor;case 1:return this.accessor1;case 2:return this.accessor2}return null}setSeparator(t){this.separator=t}getSeparator(t=!1){return""===this.separator?t?",":"/":this.separator}addNumTargets(t=1){this.numTargets=this.numTargets+t}getNumTargets(){return this.numTargets}}class Hl{constructor(t,e){this.currentIndex=0,this.name="untitled",this.query=e,this.values=[],this.parent=t,this.id=-1,this.yMin=null,this.yMax=null,this.startDate=null,this.endDate=null,this.numTargets=0,this.lineInfo=null,this.barInfo=null,this.isTmpDataset=!1,this.valueType=null==e?void 0:e.valueType;for(let e=0;e<t.getDates().length;e++)this.values.push(null)}cloneToTmpDataset(){if(!this.isTmpDataset){let t=new Hl(this.parent,null);return t.name="tmp",t.values=[...this.values],t.yMin=this.yMin,t.yMax=this.yMax,t.startDate=this.startDate.clone(),t.endDate=this.endDate.clone(),t.numTargets=this.numTargets,t.isTmpDataset=!0,t.valueType=this.valueType,t}return this}getName(){return this.name}setName(t){this.name=t}getId(){return this.id}setId(t){this.id=t}addNumTargets(t){this.numTargets=this.numTargets+t}getNumTargets(){return this.numTargets}getValue(t,e=0){let n=this.parent.getIndexOfDate(t)+Math.floor(e);return n>=0&&n<this.values.length?this.values[n]:null}setValue(t,e){let n=this.parent.getIndexOfDate(t);n>=0&&n<this.values.length&&(this.values[n]=e,(null===this.yMin||e<this.yMin)&&(this.yMin=e),(null===this.yMax||e>this.yMax)&&(this.yMax=e),(null===this.startDate||t<this.startDate)&&(this.startDate=t.clone()),(null===this.endDate||t>this.endDate)&&(this.endDate=t.clone()))}recalculateMinMax(){this.yMin=Math.min(...this.values),this.yMax=Math.max(...this.values)}getYMin(){return this.yMin}getYMax(){return this.yMax}getStartDate(){return this.startDate}getEndDate(){return this.endDate}shift(t,e){let n=!1;for(let r=0;r<this.values.length;r++)null!==this.values[r]&&(null===e||this.values[r]>=e)&&(this.values[r]=this.values[r]+t,n=!0);n&&(this.yMin=this.yMin+t,this.yMax=this.yMax+t)}setPenalty(t){for(let e=0;e<this.values.length;e++)null===this.values[e]&&(this.values[e]=t,t<this.yMin&&(this.yMin=t),t>this.yMax&&(this.yMax=t))}getQuery(){return this.query}accumulateValues(){let t=0;for(let e=0;e<this.values.length;e++)null!==this.values[e]&&(t+=this.values[e]),this.values[e]=t,t<this.yMin&&(this.yMin=t),t>this.yMax&&(this.yMax=t)}shiftByDataset(t){for(let e=0;e<this.values.length;e++){let n=this.values[e];null!==t.values[e]&&null!==n?n+=t.values[e]:null!==t.values[e]&&(n=t.values[e]),this.values[e]=n,n<this.yMin&&(this.yMin=n),n>this.yMax&&(this.yMax=n)}}getValues(){return this.values}getLength(){return this.values.length}getLengthNotNull(){let t=0;for(let e=0;e<this.values.length;e++)null!==this.values[e]&&t++;return t}next(){if(this.currentIndex<this.values.length){let t=this.currentIndex++;return{done:!1,value:new zl(this.parent.getDates()[t],this.values[t])}}return this.currentIndex=0,{done:!0,value:null}}[Symbol.iterator](){return this}}class Xl{constructor(t,e){this.currentIndex=0,this.dates=[],this.datasets=[];const n=t.creationData().format.toString();for(let r=t.clone();r<=e;r.add(1,"days")){let t=window.moment(r.format(n),n,!0);this.dates.push(t)}}createDataset(t,e){let n=new Hl(this,t);return n.setId(t.getId()),e&&n.setName(e.datasetName[t.getId()]),this.datasets.push(n),n}getIndexOfDate(t){const e=t.creationData().format.toString();for(let n=0;n<this.dates.length;n++)if(this.dates[n].format(e)===t.format(e))return n;return-1}getDatasetByQuery(t){for(let e of this.datasets)if(e.getQuery().equalTo(t))return e;return null}getDatasetById(t){for(let e of this.datasets)if(e.getId()===t)return e;return null}getXDatasetIds(){let t=[];for(let e of this.datasets)if(e.getQuery().usedAsXDataset){let n=e.getQuery().getId();t.includes(n)||-1===n||t.push(n)}return t}getDates(){return this.dates}getNames(){let t=[];for(let e of this.datasets)t.push(e.getName());return t}next(){return this.currentIndex<this.datasets.length?{done:!1,value:this.datasets[this.currentIndex++]}:(this.currentIndex=0,{done:!0,value:null})}[Symbol.iterator](){return this}}class Wl{constructor(t){this.queries=t,this.xDataset=[],this.folder="/",this.file=[],this.specifiedFilesOnly=!1,this.fileContainsLinkedFiles=[],this.fileMultiplierAfterLink="",this.dateFormat="YYYY-MM-DD",this.dateFormatPrefix="",this.dateFormatSuffix="",this.startDate=null,this.endDate=null,this.datasetName=[],this.constValue=[1],this.ignoreAttachedValue=[],this.ignoreZeroValue=[],this.accum=[],this.stack=!1,this.penalty=[],this.valueShift=[],this.shiftOnlyValueLargerThan=[],this.valueType=[],this.textValueMap={},this.dataAreaSize=new no(300,300),this.aspectRatio=new ro(1,1),this.margin=new io(10,10,10,10),this.fixedScale=1,this.fitPanelWidth=!1,this.output=[],this.line=[],this.bar=[],this.pie=[],this.summary=[],this.month=[],this.heatmap=[],this.bullet=[],this.customDataset=[],this.datasets=null}getQueryById(t){for(let e of this.queries)if(e.getId()===t)return e}}class jl{constructor(){this.id=-1,this.name="",this.xData=[],this.yData=[]}}class ql{constructor(){this.title="",this.xAxisLabel="Date",this.xAxisColor="",this.xAxisLabelColor="",this.yAxisLabel=[],this.yAxisColor=[],this.yAxisLabelColor=[],this.yAxisUnit=[],this.xAxisTickInterval=null,this.yAxisTickInterval=[],this.xAxisTickLabelFormat=null,this.yAxisTickLabelFormat=[],this.yMin=[],this.yMax=[],this.reverseYAxis=[],this.allowInspectData=!0,this.showLegend=!1,this.legendPosition="",this.legendOrientation="",this.legendBgColor="",this.legendBorderColor=""}GetGraphType(){return Yl.Unknown}}class Gl extends ql{constructor(){super(),this.lineColor=[],this.lineWidth=[],this.showLine=[],this.showPoint=[],this.pointColor=[],this.pointBorderColor=[],this.pointBorderWidth=[],this.pointSize=[],this.fillGap=[],this.yAxisLocation=[]}GetGraphType(){return Yl.Line}}class Ql extends ql{constructor(){super(),this.barColor=[],this.yAxisLocation=[]}GetGraphType(){return Yl.Bar}}class Zl{constructor(){this.title="",this.data=[],this.dataColor=[],this.dataName=[],this.label=[],this.hideLabelLessThan=.03,this.extLabel=[],this.showExtLabelOnlyIfNoLabel=!1,this.ratioInnerRadius=0,this.showLegend=!1,this.legendPosition="",this.legendOrientation="",this.legendBgColor="",this.legendBorderColor=""}GetGraphType(){return Yl.Pie}}class Kl{constructor(){this.template="",this.style=""}GetGraphType(){return Yl.Summary}}class Jl{constructor(){this.mode="circle",this.dataset=[],this.startWeekOn="Sun",this.threshold=[],this.yMin=[],this.yMax=[],this.color=null,this.dimNotInMonth=!0,this.initMonth="",this.showSelectedValue=!0,this.headerYearColor=null,this.headerMonthColor=null,this.dividingLineColor=null,this.showCircle=!0,this.showStreak=!0,this.showTodayRing=!0,this.showSelectedRing=!0,this.circleColor=null,this.circleColorByValue=!1,this.todayRingColor="",this.selectedRingColor="firebrick",this.showAnnotation=!0,this.annotation=[],this.showAnnotationOfAllTargets=!0,this.selectedDate="",this.selectedDataset=null}GetGraphType(){return Yl.Month}}class to{constructor(){this.dataset="0",this.startWeekOn="Sun",this.orientation="vertical",this.yMin=null,this.yMax=null,this.color=null}GetGraphType(){return Yl.Heatmap}}class eo{constructor(){this.title="",this.dataset="0",this.orientation="horizontal",this.value="",this.valueUnit="",this.valueColor="#69b3a2",this.range=[],this.rangeColor=[],this.showMarker=!1,this.markerValue=0,this.markerColor=""}GetGraphType(){return Yl.Bullet}}class no{constructor(t,e){this.width=t,this.height=e}}class ro{constructor(t,e){this.x=t,this.y=e}recalculateSize(t){let e=this.x/this.y,n=parseFloat((t.width*e).toFixed(2));return new no(n,t.height)}}class io{constructor(t,e,n,r){this.top=t,this.right=e,this.bottom=n,this.left=r}}class ao{constructor(t){if(this.translateX=0,this.translateY=0,"string"==typeof t){let e=t.match(/translate\(\s*(?<x>[\d\.\/-]+)\s*,\s*(?<y>[\d\.\/-]+)\s*\)/).groups;e&&(this.translateX=parseFloat(e.x),this.translateY=parseFloat(e.y))}}}class lo{constructor(t,e){this.filePath=t,this.tableIndex=e,this.xDataset=null,this.yDatasets=[]}}class oo{constructor(){this.fileTotal=0,this.fileAvailable=0,this.fileOutOfDateRange=0,this.fileNotInFormat=0,this.errorMessage="",this.minDate=window.moment(""),this.maxDate=window.moment(""),this.gotAnyValidXValue=!1,this.gotAnyValidYValue=!1}}const so=function(){const t=["HH","H","hh","h"],e=["mm","m"],n=["ss","s",""];let r=[];for(let i of t)for(let t of e)for(let e of n){let n=`${i}:${t}`;""!==e&&(n+=`:${e}`),i.contains("h")&&(n+=" a"),r.push(n)}return r}();function uo(t,e,n){if(!e&&!n)return t;let r=t;if(r.startsWith("^")&&(r=r.slice(1)),e){let t=new RegExp("^("+e+")","gm");t.test(r)&&(r=r.replace(t,""))}if(n){let t=new RegExp("("+n+")$","gm");t.test(r)&&(r=r.replace(t,""))}return r}function fo(t,e){let n=e;t.length>4&&t.startsWith("[[")&&t.endsWith("]]")&&(t=t.substring(2,t.length-2)),"iso-8601"===e.toLowerCase()&&(n=window.moment.ISO_8601);let r=window.moment(t,n,!0);return r=r.startOf("day"),r}function co(t,e,n=!0){if(!t||!e||0===e.length)return[null,t];let r=null;const i="^(?<value>[0-9]+)("+e.join("|")+")$",a=new RegExp(i,"gm");let l=a.exec(t);return l&&void 0!==l.groups&&void 0!==l.groups.value&&(r=parseFloat(l.groups.value),Number.isNumber(r)&&!Number.isNaN(r))?(n&&(t=t.replace(a,"")),[r,t]):[null,t]}function ho(t){if(!t)return null;let e=window.moment.duration(0),n=!1,r=!1;t.startsWith("+")&&(r=!1,t=t.substring(1)),t.startsWith("-")&&(r=!0,t=t.substring(1));let i=null;[i,t]=co(t,["year","years","Y","y"]),null!==i&&(r&&(i*=-1),e.add(i,"years"),n=!0);let a=null;[a,t]=co(t,["month","months","M"]),null!==a&&(r&&(a*=-1),e.add(a,"months"),n=!0);let l=null;[l,t]=co(t,["week","weeks","W","w"]),null!==l&&(r&&(l*=-1),e.add(l,"weeks"),n=!0);let o=null;[o,t]=co(t,["day","days","D","d"]),null!==o&&(r&&(o*=-1),e.add(o,"days"),n=!0);let s=null;[s,t]=co(t,["hour","hours","H","h"]),null!==s&&(r&&(s*=-1),e.add(s,"hours"),n=!0);let u=null;[u,t]=co(t,["minute","minutes","m"]),null!==u&&(r&&(u*=-1),e.add(u,"minutes"),n=!0);let f=null;return[f,t]=co(t,["second","seconds","S","s"]),null!==f&&(r&&(f*=-1),e.add(f,"seconds"),n=!0),n?e:null}function go(t,e){let n=null,r=ho(t);return r&&window.moment.isDuration(r)&&(n=mo(e),n=n.add(r),n&&n.isValid()),n}function po(t,e){return null==t?null:"iso-8601"===e.toLowerCase()?t.format():t.format(e)}function yo(t,e){return fo(po(window.moment(t),e),e)}function mo(t){return fo(po(window.moment(),t),t)}function vo(t,e){for(var n=(e=e.replace(/^\./,"")).split("."),r=0,i=n.length;r<i;++r){var a=n[r];if(!(a in t))return null;t=t[a]}return"string"==typeof t||Array.isArray(t)?t:"number"==typeof t||"boolean"==typeof t?t.toString():null}function xo(t,e){const n=Array.from(t),r=n.findIndex((t=>t!==e)),i=n.reverse().findIndex((t=>t!==e));return-1===r&&-1===i?t:t.substring(r,t.length-i)}function Ao(t){if(null===t)return null;let e=new RegExp('<img[^>]*?alt\\s*=\\s*[""\']?(?<emoji>[^\'"" >]+?)[ \'""][^>]*?>',"g");return t.replace(e,((...t)=>{let e=t[t.length-1];return e&&e.emoji?e.emoji.trim():""}))}function wo(t,e=null){let n=null,r=Rl.Number;if("string"==typeof t)if(t.includes(":")){let e=!1;t.startsWith("-")&&(e=!0,t=t.substring(1));let i=window.moment(t,so,!0);i.isValid()&&(n=i.diff(window.moment("00:00","HH:mm",!0),"seconds"),e&&(n*=-1),r=Rl.Time)}else if(e){const r=Object.keys(e);for(let n of r)if("string"==typeof n){let r=new RegExp(n,"gm");if(r.test(t)&&Number.isNumber(e[n])){let i=e[n].toString();t=t.replace(r,i);break}}n=parseFloat(t),Number.isNaN(n)&&(n=null)}else n=parseFloat(t),Number.isNaN(n)&&(n=null);else"number"==typeof t&&(n=t);return{type:r,value:n}}function bo(t,e="",n=""){var r=Qt("body").append("svg");let i=r.append("text").text(t).attr("x",-99999).attr("y",-99999);e&&i.attr("class",e),n&&i.attr("transform","rotate("+n+")");var a=r.node().getBBox();return r.remove(),{width:a.width,height:a.height}}function Do(t,e,n){let r=(0|parseFloat(t.attr("width")))+e,i=(0|parseFloat(t.attr("height")))+n;t.attr("width",r),t.attr("height",i)}function ko(t,e,n){let r=new ao(t.attr("transform"));t.attr("transform","translate("+(r.translateX+e)+","+(r.translateY+n)+")")}function Co(t){return t=(t=(t=t.replace(/(^\\s\*)|(\\s\*$)/gi,"")).replace(/\[ \]{2,}/gi," ")).replace(/\\n /,"\\n")}class Mo{static get version(){return"1.4.0"}static toString(){return"JavaScript Expression Parser (JSEP) v"+Mo.version}static addUnaryOp(t){return Mo.max_unop_len=Math.max(t.length,Mo.max_unop_len),Mo.unary_ops[t]=1,Mo}static addBinaryOp(t,e,n){return Mo.max_binop_len=Math.max(t.length,Mo.max_binop_len),Mo.binary_ops[t]=e,n?Mo.right_associative.add(t):Mo.right_associative.delete(t),Mo}static addIdentifierChar(t){return Mo.additional_identifier_chars.add(t),Mo}static addLiteral(t,e){return Mo.literals[t]=e,Mo}static removeUnaryOp(t){return delete Mo.unary_ops[t],t.length===Mo.max_unop_len&&(Mo.max_unop_len=Mo.getMaxKeyLen(Mo.unary_ops)),Mo}static removeAllUnaryOps(){return Mo.unary_ops={},Mo.max_unop_len=0,Mo}static removeIdentifierChar(t){return Mo.additional_identifier_chars.delete(t),Mo}static removeBinaryOp(t){return delete Mo.binary_ops[t],t.length===Mo.max_binop_len&&(Mo.max_binop_len=Mo.getMaxKeyLen(Mo.binary_ops)),Mo.right_associative.delete(t),Mo}static removeAllBinaryOps(){return Mo.binary_ops={},Mo.max_binop_len=0,Mo}static removeLiteral(t){return delete Mo.literals[t],Mo}static removeAllLiterals(){return Mo.literals={},Mo}get char(){return this.expr.charAt(this.index)}get code(){return this.expr.charCodeAt(this.index)}constructor(t){this.expr=t,this.index=0}static parse(t){return new Mo(t).parse()}static getMaxKeyLen(t){return Math.max(0,...Object.keys(t).map((t=>t.length)))}static isDecimalDigit(t){return t>=48&&t<=57}static binaryPrecedence(t){return Mo.binary_ops[t]||0}static isIdentifierStart(t){return t>=65&&t<=90||t>=97&&t<=122||t>=128&&!Mo.binary_ops[String.fromCharCode(t)]||Mo.additional_identifier_chars.has(String.fromCharCode(t))}static isIdentifierPart(t){return Mo.isIdentifierStart(t)||Mo.isDecimalDigit(t)}throwError(t){const e=new Error(t+" at character "+this.index);throw e.index=this.index,e.description=t,e}runHook(t,e){if(Mo.hooks[t]){const n={context:this,node:e};return Mo.hooks.run(t,n),n.node}return e}searchHook(t){if(Mo.hooks[t]){const e={context:this};return Mo.hooks[t].find((function(t){return t.call(e.context,e),e.node})),e.node}}gobbleSpaces(){let t=this.code;for(;t===Mo.SPACE_CODE||t===Mo.TAB_CODE||t===Mo.LF_CODE||t===Mo.CR_CODE;)t=this.expr.charCodeAt(++this.index);this.runHook("gobble-spaces")}parse(){this.runHook("before-all");const t=this.gobbleExpressions(),e=1===t.length?t[0]:{type:Mo.COMPOUND,body:t};return this.runHook("after-all",e)}gobbleExpressions(t){let e,n,r=[];for(;this.index<this.expr.length;)if(e=this.code,e===Mo.SEMCOL_CODE||e===Mo.COMMA_CODE)this.index++;else if(n=this.gobbleExpression())r.push(n);else if(this.index<this.expr.length){if(e===t)break;this.throwError('Unexpected "'+this.char+'"')}return r}gobbleExpression(){const t=this.searchHook("gobble-expression")||this.gobbleBinaryExpression();return this.gobbleSpaces(),this.runHook("after-expression",t)}gobbleBinaryOp(){this.gobbleSpaces();let t=this.expr.substr(this.index,Mo.max_binop_len),e=t.length;for(;e>0;){if(Mo.binary_ops.hasOwnProperty(t)&&(!Mo.isIdentifierStart(this.code)||this.index+t.length<this.expr.length&&!Mo.isIdentifierPart(this.expr.charCodeAt(this.index+t.length))))return this.index+=e,t;t=t.substr(0,--e)}return!1}gobbleBinaryExpression(){let t,e,n,r,i,a,l,o,s;if(a=this.gobbleToken(),!a)return a;if(e=this.gobbleBinaryOp(),!e)return a;for(i={value:e,prec:Mo.binaryPrecedence(e),right_a:Mo.right_associative.has(e)},l=this.gobbleToken(),l||this.throwError("Expected expression after "+e),r=[a,i,l];e=this.gobbleBinaryOp();){if(n=Mo.binaryPrecedence(e),0===n){this.index-=e.length;break}i={value:e,prec:n,right_a:Mo.right_associative.has(e)},s=e;const o=t=>i.right_a&&t.right_a?n>t.prec:n<=t.prec;for(;r.length>2&&o(r[r.length-2]);)l=r.pop(),e=r.pop().value,a=r.pop(),t={type:Mo.BINARY_EXP,operator:e,left:a,right:l},r.push(t);t=this.gobbleToken(),t||this.throwError("Expected expression after "+s),r.push(i,t)}for(o=r.length-1,t=r[o];o>1;)t={type:Mo.BINARY_EXP,operator:r[o-1].value,left:r[o-2],right:t},o-=2;return t}gobbleToken(){let t,e,n,r;if(this.gobbleSpaces(),r=this.searchHook("gobble-token"),r)return this.runHook("after-token",r);if(t=this.code,Mo.isDecimalDigit(t)||t===Mo.PERIOD_CODE)return this.gobbleNumericLiteral();if(t===Mo.SQUOTE_CODE||t===Mo.DQUOTE_CODE)r=this.gobbleStringLiteral();else if(t===Mo.OBRACK_CODE)r=this.gobbleArray();else{for(e=this.expr.substr(this.index,Mo.max_unop_len),n=e.length;n>0;){if(Mo.unary_ops.hasOwnProperty(e)&&(!Mo.isIdentifierStart(this.code)||this.index+e.length<this.expr.length&&!Mo.isIdentifierPart(this.expr.charCodeAt(this.index+e.length)))){this.index+=n;const t=this.gobbleToken();return t||this.throwError("missing unaryOp argument"),this.runHook("after-token",{type:Mo.UNARY_EXP,operator:e,argument:t,prefix:!0})}e=e.substr(0,--n)}Mo.isIdentifierStart(t)?(r=this.gobbleIdentifier(),Mo.literals.hasOwnProperty(r.name)?r={type:Mo.LITERAL,value:Mo.literals[r.name],raw:r.name}:r.name===Mo.this_str&&(r={type:Mo.THIS_EXP})):t===Mo.OPAREN_CODE&&(r=this.gobbleGroup())}return r?(r=this.gobbleTokenProperty(r),this.runHook("after-token",r)):this.runHook("after-token",!1)}gobbleTokenProperty(t){this.gobbleSpaces();let e=this.code;for(;e===Mo.PERIOD_CODE||e===Mo.OBRACK_CODE||e===Mo.OPAREN_CODE||e===Mo.QUMARK_CODE;){let n;if(e===Mo.QUMARK_CODE){if(this.expr.charCodeAt(this.index+1)!==Mo.PERIOD_CODE)break;n=!0,this.index+=2,this.gobbleSpaces(),e=this.code}this.index++,e===Mo.OBRACK_CODE?((t={type:Mo.MEMBER_EXP,computed:!0,object:t,property:this.gobbleExpression()}).property||this.throwError('Unexpected "'+this.char+'"'),this.gobbleSpaces(),e=this.code,e!==Mo.CBRACK_CODE&&this.throwError("Unclosed ["),this.index++):e===Mo.OPAREN_CODE?t={type:Mo.CALL_EXP,arguments:this.gobbleArguments(Mo.CPAREN_CODE),callee:t}:(e===Mo.PERIOD_CODE||n)&&(n&&this.index--,this.gobbleSpaces(),t={type:Mo.MEMBER_EXP,computed:!1,object:t,property:this.gobbleIdentifier()}),n&&(t.optional=!0),this.gobbleSpaces(),e=this.code}return t}gobbleNumericLiteral(){let t,e,n="";for(;Mo.isDecimalDigit(this.code);)n+=this.expr.charAt(this.index++);if(this.code===Mo.PERIOD_CODE)for(n+=this.expr.charAt(this.index++);Mo.isDecimalDigit(this.code);)n+=this.expr.charAt(this.index++);if(t=this.char,"e"===t||"E"===t){for(n+=this.expr.charAt(this.index++),t=this.char,"+"!==t&&"-"!==t||(n+=this.expr.charAt(this.index++));Mo.isDecimalDigit(this.code);)n+=this.expr.charAt(this.index++);Mo.isDecimalDigit(this.expr.charCodeAt(this.index-1))||this.throwError("Expected exponent ("+n+this.char+")")}return e=this.code,Mo.isIdentifierStart(e)?this.throwError("Variable names cannot start with a number ("+n+this.char+")"):(e===Mo.PERIOD_CODE||1===n.length&&n.charCodeAt(0)===Mo.PERIOD_CODE)&&this.throwError("Unexpected period"),{type:Mo.LITERAL,value:parseFloat(n),raw:n}}gobbleStringLiteral(){let t="";const e=this.index,n=this.expr.charAt(this.index++);let r=!1;for(;this.index<this.expr.length;){let e=this.expr.charAt(this.index++);if(e===n){r=!0;break}if("\\"===e)switch(e=this.expr.charAt(this.index++),e){case"n":t+="\n";break;case"r":t+="\r";break;case"t":t+="\t";break;case"b":t+="\b";break;case"f":t+="\f";break;case"v":t+="\v";break;default:t+=e}else t+=e}return r||this.throwError('Unclosed quote after "'+t+'"'),{type:Mo.LITERAL,value:t,raw:this.expr.substring(e,this.index)}}gobbleIdentifier(){let t=this.code,e=this.index;for(Mo.isIdentifierStart(t)?this.index++:this.throwError("Unexpected "+this.char);this.index<this.expr.length&&(t=this.code,Mo.isIdentifierPart(t));)this.index++;return{type:Mo.IDENTIFIER,name:this.expr.slice(e,this.index)}}gobbleArguments(t){const e=[];let n=!1,r=0;for(;this.index<this.expr.length;){this.gobbleSpaces();let i=this.code;if(i===t){n=!0,this.index++,t===Mo.CPAREN_CODE&&r&&r>=e.length&&this.throwError("Unexpected token "+String.fromCharCode(t));break}if(i===Mo.COMMA_CODE){if(this.index++,r++,r!==e.length)if(t===Mo.CPAREN_CODE)this.throwError("Unexpected token ,");else if(t===Mo.CBRACK_CODE)for(let t=e.length;t<r;t++)e.push(null)}else if(e.length!==r&&0!==r)this.throwError("Expected comma");else{const t=this.gobbleExpression();t&&t.type!==Mo.COMPOUND||this.throwError("Expected comma"),e.push(t)}}return n||this.throwError("Expected "+String.fromCharCode(t)),e}gobbleGroup(){this.index++;let t=this.gobbleExpressions(Mo.CPAREN_CODE);if(this.code===Mo.CPAREN_CODE)return this.index++,1===t.length?t[0]:!!t.length&&{type:Mo.SEQUENCE_EXP,expressions:t};this.throwError("Unclosed (")}gobbleArray(){return this.index++,{type:Mo.ARRAY_EXP,elements:this.gobbleArguments(Mo.CBRACK_CODE)}}}const To=new class{add(t,e,n){if("string"!=typeof arguments[0])for(let t in arguments[0])this.add(t,arguments[0][t],arguments[1]);else(Array.isArray(t)?t:[t]).forEach((function(t){this[t]=this[t]||[],e&&this[t][n?"unshift":"push"](e)}),this)}run(t,e){this[t]=this[t]||[],this[t].forEach((function(t){t.call(e&&e.context?e.context:e,e)}))}};Object.assign(Mo,{hooks:To,plugins:new class{constructor(t){this.jsep=t,this.registered={}}register(...t){t.forEach((t=>{if("object"!=typeof t||!t.name||!t.init)throw new Error("Invalid JSEP plugin format");this.registered[t.name]||(t.init(this.jsep),this.registered[t.name]=t)}))}}(Mo),COMPOUND:"Compound",SEQUENCE_EXP:"SequenceExpression",IDENTIFIER:"Identifier",MEMBER_EXP:"MemberExpression",LITERAL:"Literal",THIS_EXP:"ThisExpression",CALL_EXP:"CallExpression",UNARY_EXP:"UnaryExpression",BINARY_EXP:"BinaryExpression",ARRAY_EXP:"ArrayExpression",TAB_CODE:9,LF_CODE:10,CR_CODE:13,SPACE_CODE:32,PERIOD_CODE:46,COMMA_CODE:44,SQUOTE_CODE:39,DQUOTE_CODE:34,OPAREN_CODE:40,CPAREN_CODE:41,OBRACK_CODE:91,CBRACK_CODE:93,QUMARK_CODE:63,SEMCOL_CODE:59,COLON_CODE:58,unary_ops:{"-":1,"!":1,"~":1,"+":1},binary_ops:{"||":1,"??":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"===":6,"!==":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":10,"/":10,"%":10,"**":11},right_associative:new Set(["**"]),additional_identifier_chars:new Set(["$","_"]),literals:{true:!0,false:!1,null:null},this_str:"this"}),Mo.max_unop_len=Mo.getMaxKeyLen(Mo.unary_ops),Mo.max_binop_len=Mo.getMaxKeyLen(Mo.binary_ops);const _o=t=>new Mo(t).parse(),Eo=Object.getOwnPropertyNames(class{});Object.getOwnPropertyNames(Mo).filter((t=>!Eo.includes(t)&&void 0===_o[t])).forEach((t=>{_o[t]=Mo[t]})),_o.Jsep=Mo;var Fo={name:"ternary",init(t){t.hooks.add("after-expression",(function(e){if(e.node&&this.code===t.QUMARK_CODE){this.index++;const n=e.node,r=this.gobbleExpression();if(r||this.throwError("Expected expression"),this.gobbleSpaces(),this.code===t.COLON_CODE){this.index++;const i=this.gobbleExpression();if(i||this.throwError("Expected expression"),e.node={type:"ConditionalExpression",test:n,consequent:r,alternate:i},n.operator&&t.binary_ops[n.operator]<=.9){let r=n;for(;r.right.operator&&t.binary_ops[r.right.operator]<=.9;)r=r.right;e.node.test=r.right,r.right=e.node,e.node=n}}else this.throwError("Expected :")}}))}};_o.plugins.register(Fo);var So,Bo={};function No(t){if("number"==typeof t){if(0===t)return!1}else if(t instanceof Hl&&t.getValues().some((function(t){return 0===t})))return!1;return!0}So=Bo,function(){var t={not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function e(n){return function(n,r){var i,a,l,o,s,u,f,c,h,d=1,g=n.length,p="";for(a=0;a<g;a++)if("string"==typeof n[a])p+=n[a];else if("object"==typeof n[a]){if((o=n[a]).keys)for(i=r[d],l=0;l<o.keys.length;l++){if(null==i)throw new Error(e('[sprintf] Cannot access property "%s" of undefined value "%s"',o.keys[l],o.keys[l-1]));i=i[o.keys[l]]}else i=o.param_no?r[o.param_no]:r[d++];if(t.not_type.test(o.type)&&t.not_primitive.test(o.type)&&i instanceof Function&&(i=i()),t.numeric_arg.test(o.type)&&"number"!=typeof i&&isNaN(i))throw new TypeError(e("[sprintf] expecting number but found %T",i));switch(t.number.test(o.type)&&(c=i>=0),o.type){case"b":i=parseInt(i,10).toString(2);break;case"c":i=String.fromCharCode(parseInt(i,10));break;case"d":case"i":i=parseInt(i,10);break;case"j":i=JSON.stringify(i,null,o.width?parseInt(o.width):0);break;case"e":i=o.precision?parseFloat(i).toExponential(o.precision):parseFloat(i).toExponential();break;case"f":i=o.precision?parseFloat(i).toFixed(o.precision):parseFloat(i);break;case"g":i=o.precision?String(Number(i.toPrecision(o.precision))):parseFloat(i);break;case"o":i=(parseInt(i,10)>>>0).toString(8);break;case"s":i=String(i),i=o.precision?i.substring(0,o.precision):i;break;case"t":i=String(!!i),i=o.precision?i.substring(0,o.precision):i;break;case"T":i=Object.prototype.toString.call(i).slice(8,-1).toLowerCase(),i=o.precision?i.substring(0,o.precision):i;break;case"u":i=parseInt(i,10)>>>0;break;case"v":i=i.valueOf(),i=o.precision?i.substring(0,o.precision):i;break;case"x":i=(parseInt(i,10)>>>0).toString(16);break;case"X":i=(parseInt(i,10)>>>0).toString(16).toUpperCase()}t.json.test(o.type)?p+=i:(!t.number.test(o.type)||c&&!o.sign?h="":(h=c?"+":"-",i=i.toString().replace(t.sign,"")),u=o.pad_char?"0"===o.pad_char?"0":o.pad_char.charAt(1):" ",f=o.width-(h+i).length,s=o.width&&f>0?u.repeat(f):"",p+=o.align?h+i+s:"0"===u?h+s+i:s+h+i)}return p}(function(e){if(r[e])return r[e];for(var n,i=e,a=[],l=0;i;){if(null!==(n=t.text.exec(i)))a.push(n[0]);else if(null!==(n=t.modulo.exec(i)))a.push("%");else{if(null===(n=t.placeholder.exec(i)))throw new SyntaxError("[sprintf] unexpected placeholder");if(n[2]){l|=1;var o=[],s=n[2],u=[];if(null===(u=t.key.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(o.push(u[1]);""!==(s=s.substring(u[0].length));)if(null!==(u=t.key_access.exec(s)))o.push(u[1]);else{if(null===(u=t.index_access.exec(s)))throw new SyntaxError("[sprintf] failed to parse named argument key");o.push(u[1])}n[2]=o}else l|=2;if(3===l)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");a.push({placeholder:n[0],param_no:n[1],keys:n[2],sign:n[3],pad_char:n[4],align:n[5],width:n[6],precision:n[7],type:n[8]})}i=i.substring(n[0].length)}return r[e]=a}(n),arguments)}function n(t,n){return e.apply(null,[t].concat(n||[]))}var r=Object.create(null);So.sprintf=e,So.vsprintf=n,"undefined"!=typeof window&&(window.sprintf=e,window.vsprintf=n)}();const Lo={first:function(t,e){return t.getValue(this.startDate(...arguments))},last:function(t,e){return t.getValue(this.endDate(...arguments))},min:function(t,e){return m(t.getValues())},minDate:function(t,e){let n=m(t.getValues());if(Number.isNumber(n)){let e=Array.from(t);for(let t of e.reverse())if(null!==t.value&&t.value===n)return t.date}return"Error: min not found"},max:function(t,e){return y(t.getValues())},maxDate:function(t,e){let n=y(t.getValues());if(Number.isNumber(n)){let e=Array.from(t);for(let t of e.reverse())if(null!==t.value&&t.value===n)return t.date}return"Error: max not found"},startDate:function(t,e){if(t){let e=t.getStartDate();if(e&&e.isValid())return e}return e.startDate},endDate:function(t,e){if(t){let e=t.getEndDate();if(e&&e.isValid())return e}return e.endDate},sum:function(t,e){return b(t.getValues())},count:function(t,e){return"Error: deprecated function 'count'"},numTargets:function(t,e){return t.getNumTargets()},days:function(t,e){return"Error: deprecated function 'days'"},numDays:function(t,e){return t.getLength()},numDaysHavingData:function(t,e){return t.getLengthNotNull()},maxStreak:function(t,e){let n=0,r=0;for(let e of t)e.value?n++:n=0,n>=r&&(r=n);return r},maxStreakStart:function(t,e){let n=0,r=0,i=null,a=null;if(t)for(let e of t)e.value?(0===n&&(i=e.date),n++):n=0,n>=r&&(r=n,a=i);return a},maxStreakEnd:function(t,e){let n=0,r=0,i=null,a=null;if(t){let e=Array.from(t);for(let t=0;t<e.length;t++){let l=e[t],o=null;t<e.length-1&&(o=e[t+1]),l.value?(n++,(null==o?void 0:o.value)||(i=l.date)):n=0,n>=r&&(r=n,a=i)}}return a},maxBreaks:function(t,e){let n=0,r=0;for(let e of t)e.value?n=0:n++,n>r&&(r=n);return r},maxBreaksStart:function(t,e){let n=0,r=0,i=null,a=null;if(t)for(let e of t)e.value?n=0:(0===n&&(i=e.date),n++),n>=r&&(r=n,a=i);return a},maxBreaksEnd:function(t,e){let n=0,r=0,i=null,a=null;if(t){let e=Array.from(t);for(let t=0;t<e.length;t++){let l=e[t],o=null;t<e.length-1&&(o=e[t+1]),l.value?n=0:(n++,(null==o?void 0:o.value)&&(i=l.date)),n>=r&&(r=n,a=i)}}return a},lastStreak:function(t,e){return"Error: deprecated function 'lastStreak'"},currentStreak:function(t,e){let n=0;if(t){let e=Array.from(t);for(let t=e.length-1;t>=0;t--){if(!e[t].value)break;n++}}return n},currentStreakStart:function(t,e){let n=null;if(t){let e=Array.from(t);for(let t=e.length-1;t>=0;t--){let r=e[t];if(t<e.length-1&&(n=e[t+1].date),!r.value)break}}return null===n?"Error: absense":n},currentStreakEnd:function(t,e){let n=0,r=null;if(t){let e=Array.from(t);for(let t=e.length-1;t>=0;t--){let i=e[t];if(!i.value)break;0===n&&(r=i.date),n++}}return null===r?"Error: absense":r},currentBreaks:function(t,e){let n=0;if(t){let e=Array.from(t);for(let t=e.length-1;t>=0;t--){if(e[t].value)break;n++}}return n},currentBreaksStart:function(t,e){let n=null;if(t){let e=Array.from(t);for(let t=e.length-1;t>=0;t--){let r=e[t];if(t<e.length-1&&(n=e[t+1].date),r.value)break}}return null===n?"Error: absense":n},currentBreaksEnd:function(t,e){let n=0,r=null;if(t){let e=Array.from(t);for(let t=e.length-1;t>=0;t--){let i=e[t];if(i.value)break;0===n&&(r=i.date),n++}}return null===r?"Error: absense":r},average:function(t,e){let n=t.getLengthNotNull();return No(n)?b(t.getValues())/n:"Error: divide by zero in expression"},median:function(t,e){return A(t.getValues())},variance:function(t,e){return function(t){let e,n=0,r=0,i=0;for(let a of t)null!=a&&(a=+a)>=a&&(e=a-r,r+=e/++n,i+=e*(a-r));if(n>1)return i/(n-1)}(t.getValues())}},Io={"-":function(t){if("number"==typeof t)return-1*t;if(t instanceof Hl){let e=t.cloneToTmpDataset();return e.getValues().forEach((function(t,e,n){null!==n[e]&&(n[e]=-1*t)})),e.recalculateMinMax(),e}return"Error: unknown operation for '-'"},"+":function(t){if("number"==typeof t)return t;if(t instanceof Hl){return t.cloneToTmpDataset()}return"Error: unknown operation for '+'"}},Oo={"+":function(t,e){if("number"==typeof t&&"number"==typeof e)return t+e;if("number"==typeof t&&e instanceof Hl){let n=e.cloneToTmpDataset();return n.getValues().forEach((function(e,n,r){null!==r[n]?r[n]=t+e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&"number"==typeof e){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t+e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&e instanceof Hl){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t+e.getValues()[n]:r[n]=null})),n.recalculateMinMax(),n}return"Error: unknown operation for '+'"},"-":function(t,e){if("number"==typeof t&&"number"==typeof e)return t-e;if("number"==typeof t&&e instanceof Hl){let n=e.cloneToTmpDataset();return n.getValues().forEach((function(e,n,r){null!==r[n]?r[n]=t-e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&"number"==typeof e){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t-e:r[n]=null})),n}if(t instanceof Hl&&e instanceof Hl){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t-e.getValues()[n]:r[n]=null})),n.recalculateMinMax(),n}return"Error: unknown operation for '-'"},"*":function(t,e){if("number"==typeof t&&"number"==typeof e)return t*e;if("number"==typeof t&&e instanceof Hl){let n=e.cloneToTmpDataset();return n.getValues().forEach((function(e,n,r){null!==r[n]?r[n]=t*e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&"number"==typeof e){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t*e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&e instanceof Hl){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t*e.getValues()[n]:r[n]=null})),n.recalculateMinMax(),n}return"Error: unknown operation for '*'"},"/":function(t,e){if(!No(e))return"Error: divide by zero in expression";if("number"==typeof t&&"number"==typeof e)return t/e;if("number"==typeof t&&e instanceof Hl){let n=e.cloneToTmpDataset();return n.getValues().forEach((function(e,n,r){null!==r[n]?r[n]=t/e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&"number"==typeof e){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t/e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&e instanceof Hl){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t/e.getValues()[n]:r[n]=null})),n.recalculateMinMax(),n}return"Error: unknown operation for '/'"},"%":function(t,e){if(!No(e))return"Error: divide by zero in expression";if("number"==typeof t&&"number"==typeof e)return t%e;if("number"==typeof t&&e instanceof Hl){let n=e.cloneToTmpDataset();return n.getValues().forEach((function(e,n,r){null!==r[n]?r[n]=t%e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&"number"==typeof e){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t%e:r[n]=null})),n.recalculateMinMax(),n}if(t instanceof Hl&&e instanceof Hl){let n=t.cloneToTmpDataset();return n.getValues().forEach((function(t,n,r){null!==r[n]?r[n]=t%e.getValues()[n]:r[n]=null})),n.recalculateMinMax(),n}return"Error: unknown operation for '%'"}},Vo={normalize:function(t,e,n){let r=t.getYMin(),i=t.getYMax();if(null!==r&&null!==i&&i>r){let e=t.cloneToTmpDataset();return e.getValues().forEach((function(t,e,n){n[e]=(t-r)/(i-r)})),e.recalculateMinMax(),e}return"Error: invalid data range for function 'normalize'"},setMissingValues:function(t,e,n){if(e&&e.length>0){let n=e[0],r=t.cloneToTmpDataset();return Number.isNumber(n)&&!Number.isNaN(n)?(r.getValues().forEach((function(t,e,r){null===t&&(r[e]=n)})),r.recalculateMinMax(),r):"Error: invalid arguments for function 'setMissingValues'"}return"Error: invalid arguments for function 'setMissingValues"}};function Po(t,e){switch(t.type){case"Literal":return t.value;case"Identifier":let n=t.name;return n in Lo||n in Vo?`Error: deprecated template variable '${n}', use '${n}()' instead`:`Error: unknown function name '${n}'`;case"UnaryExpression":let r=t,i=Po(r.argument,e);return"string"==typeof i?i:Io[r.operator](i);case"BinaryExpression":let a=t,l=Po(a.left,e),o=Po(a.right,e),s=function(t,e){return"string"==typeof t?t:"string"==typeof e?e:"number"==typeof t||window.moment.isMoment(t)||t instanceof Hl?"number"==typeof e||window.moment.isMoment(e)||e instanceof Hl?"":"Error: invalide operant type":"Error: invalid operant type"}(l,o);return"string"==typeof s&&s.startsWith("Error:")?s:Oo[a.operator](l,o);case"CallExpression":let u=t,f=u.callee.name,c=function(t,e){return t.map((function(t){return Po(t,e)}))}(u.arguments,e);if("string"==typeof c)return c;if("dataset"===f){if(1===c.length){let t=c[0];if("string"==typeof t)return t;if("number"!=typeof t)return"Error: function 'dataset' only accepts id in number";let n=function(t,e){return e.datasets.getDatasetById(t)}(t,e);return n||`Error: no dataset found for id '${t}'`}}else{if(f in Lo){if(0===c.length){let t=null;for(let n of e.datasets)t||n.getQuery().usedAsXDataset||(t=n);return t?Lo[f](t,e):`No available dataset found for function ${f}`}if(1===c.length){let t=c[0];return"string"==typeof t?t:t instanceof Hl?Lo[f](t,e):`Error: function '${f}' only accepts Dataset`}return`Error: Too many arguments for function ${f}`}if(f in Vo){if(1===c.length){if("string"==typeof c[0])return c[0];if(c[0]instanceof Hl){let t=c[0];return Vo[f](t,null,e)}return`Error: function ${f} only accept Dataset`}if(c.length>1){if("string"==typeof c[0])return c[0];if(c[0]instanceof Hl){let t=c[0];return Vo[f](t,c.filter((function(t,e,n){return e>0})),e)}return`Error: function ${f} only accept Dataset`}return`Error: Too many arguments for function ${f}`}}return`Error: unknown function name '${f}'`}return"Error: unknown expression"}function $o(t,e){let n,r=[],i=new RegExp("{{(?<expr>[\\w+\\-*\\/0-9\\s()\\[\\]%.,]+)(::(?<format>[\\w+\\-*\\/0-9\\s()\\[\\]%.:]+))?}}","gm");for(;n=i.exec(t);){let t=n[0];if(!r.some((e=>e.source===t))&&(void 0!==n.groups&&void 0!==n.groups.expr)){let i=n.groups.expr,a=null;try{a=_o(i)}catch(t){return"Error:"+t.message}if(!a)return"Error: failed to parse expression";const l=Po(a,e);if("string"==typeof l)return l;if("number"==typeof l||window.moment.isMoment(l)){let e=null;void 0!==n.groups.format&&(e=n.groups.format),r.push({source:t,value:l,format:e})}}}return r}function Yo(t,e){let n=$o(t,e);if("string"==typeof n)return n;let r=n;for(let n of r){let r=n.source,i=n.value,a=n.format,l="";"number"==typeof i?l=a?Bo.sprintf("%"+a,i):i.toFixed(1):window.moment.isMoment(i)&&(l=po(i,a||e.dateFormat)),l&&(t=t.split(r).join(l))}return t}function Ro(t,e){if(t=t.trim(),/^([\-]?[0-9]+[\.][0-9]+|[\-]?[0-9]+)$/.test(t))return parseFloat(t);let n=$o(t,e);if("string"==typeof n)return n;let r=n;return r.length>0?r[0].value:"Error: failed to resolve values"}function zo(t,e,n,r){let i="",a=.5*n.dataAreaSize.width,l=.7*a,o=l*r.ratioInnerRadius,s=[];for(let t of r.data){let e=Ro(t,n);if("string"==typeof e){i=e;break}"number"==typeof e&&s.push(e)}if(""!==i)return i;let u=[];for(let t of r.label){let e=Yo(t,n);if(e.startsWith("Error")){i=e;break}u.push(e)}if(""!==i)return i;let f=r.hideLabelLessThan,c=u.map((function(t){return bo(t,"tracker-tick-label")})),h=[];for(let t of r.extLabel){let e=Yo(t,n);if(e.startsWith("Error")){i=e;break}h.push(e)}if(""!==i)return i;let d=h.map((function(t){return bo(t,"tracker-pie-label")})),g=r.showExtLabelOnlyIfNoLabel,p=Vr().range(r.dataColor),y=e.dataArea.append("g");y.attr("transform",(function(){return"translate("+.5*n.dataAreaSize.width+","+.5*n.dataAreaSize.height+")"}));let m=function(){var t=Vl,e=Ol,n=null,r=fl(0),i=fl(wl),a=fl(0);function l(l){var o,s,u,f,c,h=(l=Sl(l)).length,d=0,g=new Array(h),p=new Array(h),y=+r.apply(this,arguments),m=Math.min(wl,Math.max(-wl,i.apply(this,arguments)-y)),v=Math.min(Math.abs(m)/h,a.apply(this,arguments)),x=v*(m<0?-1:1);for(o=0;o<h;++o)(c=p[g[o]=o]=+t(l[o],o,l))>0&&(d+=c);for(null!=e?g.sort((function(t,n){return e(p[t],p[n])})):null!=n&&g.sort((function(t,e){return n(l[t],l[e])})),o=0,u=d?(m-h*x)/d:0;o<h;++o,y=f)s=g[o],f=y+((c=p[s])>0?c*u:0)+x,p[s]={data:l[s],index:o,value:c,startAngle:y,endAngle:f,padAngle:v};return p}return l.value=function(e){return arguments.length?(t="function"==typeof e?e:fl(+e),l):t},l.sortValues=function(t){return arguments.length?(e=t,n=null,l):e},l.sort=function(t){return arguments.length?(n=t,e=null,l):n},l.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:fl(+t),l):r},l.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:fl(+t),l):i},l.padAngle=function(t){return arguments.length?(a="function"==typeof t?t:fl(+t),l):a},l}(),v=m(s);v.forEach((function(t,e){t.input_index=e}));let x=y.selectAll("sector").data(v).enter().append("g").attr("class","sector"),A=Fl().innerRadius(o).outerRadius(l);var w=Fl().innerRadius(.9*a).outerRadius(.9*a);function b(t){return(t.endAngle-t.startAngle)/(2*Math.PI)<f}function D(t){return t.startAngle+(t.endAngle-t.startAngle)/2}function k(t,e){return g?""===u[e]||b(t)?h[e]:"":h[e]}x.append("path").attr("fill",(function(t,e){return p(e.toString())})).attr("d",A),y.selectAll("label").data(m(s)).enter().append("text").text((function(t,e){return b(t)?"":u[e]})).attr("transform",(function(t){return"translate("+A.centroid(t)[0]+","+A.centroid(t)[1]+")"})).style("text-anchor","middle").attr("class","tracker-pie-label");let C=null,M={};function T(t,e){let n=c[e].width;d[e].width;let r=b(t),i=D(t),a=A.centroid(t),l=w.centroid(t),o=M[e]||w.centroid(t);l[1]=o[1];let s=Math.sqrt((l[0]-a[0])**2+(l[1]-a[1])**2);return""===u[e]||r||(a[0]=a[0]+(l[0]-a[0])*n/s,a[1]=a[1]+(l[1]-a[1])*n/s,o[0]=o[0]+-3*(i<Math.PI?1:-1)),s=Math.sqrt((l[0]-a[0])**2+(l[1]-a[1])**2),s>Math.sqrt((o[0]-a[0])**2+(o[1]-a[1])**2)?[a,o]:[a,l,o]}y.selectAll("extLabel").data(v).enter().append("text").sort((function(t,e){return Math.cos(D(e))-Math.cos(D(t))})).text((function(t,e){return k(t,t.input_index)})).attr("transform",(function(t,e){if(0==k(t,e=t.input_index).length)return;let n=w.centroid(t),r=D(t);n[0]=(.99*a-d[e].width)*(r<Math.PI?1:-1);var i=0;let l=new DOMRect(n[0],n[1],d[e].width,d[e].height);return null!==C&&(l.right<C.left||C.right<l.left||C.bottom<l.top||(i=C.bottom-l.top)),0!=i&&(l=new DOMRect(n[0],n[1]+i,d[e].width,d[e].height)),C=l,M[e]=[n[0],n[1]+i],"translate("+n[0]+","+(n[1]+i)+")"})).style("text-anchor",(function(t){return D(t)<Math.PI?"start":"end"})).attr("class","tracker-pie-label"),y.selectAll("line").data(v).enter().append("polyline").attr("stroke","black").style("fill","none").attr("stroke-width",1).attr("points",(function(t,e){if(g){if((""===u[e]||b(t))&&""!==h[e])return T(t,e)}else if(""!==h[e])return T(t,e)})).attr("class","tracker-axis")}function Uo(t,e,n){if(!e||!n)return;let r={};r=function(t,e,n){Qt(e).select("#svg").remove();for(var r=Object.getOwnPropertyNames(t),i=0;i<r.length;i++)delete t[r[i]];let a=Qt(e).append("svg").attr("id","svg").attr("width",n.dataAreaSize.width+n.margin.left+n.margin.right).attr("height",n.dataAreaSize.height+n.margin.top+n.margin.bottom);t.svg=a;let l=a.append("g").attr("id","graphArea").attr("transform","translate("+n.margin.left+","+n.margin.top+")").attr("width",n.dataAreaSize.width+n.margin.right).attr("height",n.dataAreaSize.height+n.margin.bottom);t.graphArea=l;let o=l.append("g").attr("id","dataArea").attr("width",n.dataAreaSize.width).attr("height",n.dataAreaSize.height);return t.dataArea=o,t}(r,t,e);let i=ul[n.dataColor.length];for(let t=0;t<n.dataColor.length;t++)null===n.dataColor[t]&&(n.dataColor[t]=i[t]);!function(t,e,n,r){if(!n||!r)return;if(!r.title)return;let i=bo(r.title,"tracker-title"),a=e.graphArea.append("text").text(r.title).attr("id","title").attr("transform","translate("+n.dataAreaSize.width/2+","+i.height/2+")").attr("height",i.height).attr("class","tracker-title");e.title=a,Do(e.svg,0,i.height),Do(e.graphArea,0,i.height),ko(e.dataArea,0,i.height)}(0,r,e,n),zo(0,r,e,n),n.showLegend&&function(t,e,n,r){let i=e.svg;e.graphArea;let a=e.dataArea,l=e.title,o=0;l&&(o=parseFloat(l.attr("height")));let s=r.dataName,u=s.map((function(t){return bo(t,"tracker-legend-label")})),f=0,c=0;for(let t=0;t<s.length;t++)u[t].width>c&&(c=u[t].width,f=t);let h=c/s[f].length,d=u[f].height,g=s.length,p=2*h,y=d,m=2*h,v=0,x=0;"vertical"===r.legendOrientation?(v=3*p+m+c,x=(g+1)*y):"horizontal"===r.legendOrientation&&(v=(2*p+m)*g+p+b(u,(function(t,e){return t.width})),x=y+d);let A=0,w=0;if("top"===r.legendPosition)A=n.dataAreaSize.width/2-v/2,w=o,Do(i,0,x+y),ko(a,0,x+y);else if("bottom"===r.legendPosition)A=n.dataAreaSize.width/2-v/2,w=o+n.dataAreaSize.height+y,Do(i,0,x+y);else if("left"===r.legendPosition)A=0,w=o+n.dataAreaSize.height/2-x/2,Do(i,v+p,0),ko(a,v+p,0);else{if("right"!==r.legendPosition)return;A=n.dataAreaSize.width+p,w=o+n.dataAreaSize.height/2-x/2,Do(i,v+p,0)}let D=e.graphArea.append("g").attr("id","legend").attr("transform","translate("+A+","+w+")"),k=D.append("rect").attr("class","tracker-legend").attr("width",v).attr("height",x);r.legendBgColor&&k.style("fill",r.legendBgColor),r.legendBorderColor&&k.style("stroke",r.legendBorderColor);let C=p,M=d,T=C+p+m,_=M;if("vertical"===r.legendOrientation)D.selectAll("markers").data(s).enter().append("circle").attr("cx",C+m/2).attr("cy",(function(t,e){return M+e*y})).attr("r",(function(t,e){return 5})).style("fill",(function(t,e){return r.dataColor[e]})),D.selectAll("labels").data(s).enter().append("text").attr("x",T).attr("y",(function(t,e){return _+e*y})).text((function(t,e){return t})).style("alignment-baseline","middle").attr("class","tracker-legend-label").style("fill",(function(t,e){return r.dataColor[e]}));else if("horizontal"===r.legendOrientation){let t=0;t=0,D.selectAll("markers").data(s).enter().append("circle").attr("cx",(function(e,n){return 0===n?t=C+m/2:t+=u[n].width+p+m+p,t})).attr("cy",M).attr("r",(function(t,e){return 5})).style("fill",(function(t,e){return r.dataColor[e]})),t=0,D.selectAll("labels").data(s).enter().append("text").attr("x",(function(e,n){return 0===n?t=T:t+=u[n].width+p+m+p,t})).attr("y",_).text((function(t,e){return t})).style("alignment-baseline","middle").attr("class","tracker-legend-label").style("fill",(function(t,e){return r.dataColor[e]}))}}(0,r,e,n),function(t,e,n){let r=Qt(t),i=e.svg,a=parseFloat(i.attr("width")),l=parseFloat(i.attr("height"));i.attr("width",null).attr("height",null).attr("viewBox",`0 0 ${a} ${l}`).attr("preserveAspectRatio","xMidYMid meet"),n.fitPanelWidth?r.style("width","100%"):(r.style("width",(a*n.fixedScale).toString()+"px"),r.style("height",(l*n.fixedScale).toString()+"px"))}(t,r,e)}function Ho(t,e,n){if(!e||!n)return;let r="";n.template,r=n.template;let i=Yo(r,e);if(i.startsWith("Error:"))return i;if(r=i,""!==r){let e=Qt(t).append("div");if(r.includes("\n")||r.includes("\\n")){let t=r.split(/(\n|\\n)/);for(let n of t)"\n"!==n&&"\\n"!==n&&e.append("div").text(n)}else e.text(r);""!==n.style&&e.attr("style",n.style)}}let Xo=!1;function Wo(t,e,n){let r=Qt(t),i=e.svg,a=parseFloat(i.attr("width")),l=parseFloat(i.attr("height"));i.attr("width",null).attr("height",null).attr("viewBox",`0 0 ${a} ${l}`).attr("preserveAspectRatio","xMidYMid meet"),n.fitPanelWidth?r.style("width","100%"):(r.style("width",(a*n.fixedScale).toString()+"px"),r.style("height",(l*n.fixedScale).toString()+"px"))}function jo(t,e){let n=e.dataset;if(0===n.length)return!1;let r=null;if(null===e.selectedDataset){for(let e of n)if(r=t.datasets.getDatasetById(e),r&&!r.getQuery().usedAsXDataset)break;if(r)return e.selectedDataset=r.getId(),!0}else{let i=e.selectedDataset,a=n.findIndex((t=>t===i));if(a>=0){if(a===e.dataset.length-1){for(let e of n)if(r=t.datasets.getDatasetById(e),r&&!r.getQuery().usedAsXDataset)break;return!!r&&(e.selectedDataset=r.getId(),!0)}{a++;let i=n[a];if(r=t.datasets.getDatasetById(i),e.selectedDataset=i,r&&!r.getQuery().usedAsXDataset)return!0;jo(t,e)}}}return!1}function qo(t,e,n,r){Qt(e).select("#svg").remove();for(var i=Object.getOwnPropertyNames(t),a=0;a<i.length;a++)delete t[i[a]];let l=Qt(e).append("svg").attr("id","svg").attr("width",n.dataAreaSize.width+n.margin.left+n.margin.right).attr("height",n.dataAreaSize.height+n.margin.top+n.margin.bottom);t.svg=l;let o=l.append("g").attr("id","graphArea").attr("transform","translate("+n.margin.left+","+n.margin.top+")").attr("width",n.dataAreaSize.width+n.margin.right).attr("height",n.dataAreaSize.height+n.margin.bottom);t.graphArea=o;let s=o.append("g").attr("id","dataArea").attr("width",n.dataAreaSize.width).attr("height",n.dataAreaSize.height);return t.dataArea=s,t}function Go(t,e){let n=t.svg.selectAll("circle");for(let t of n){let e=Qt(t).attr("id");e&&e.startsWith("tracker-selected-circle-")&&Qt(t).style("stroke","none")}e.selectedDate="",t.monitor.text("")}function Qo(t,e,n,r,i){if(!n||!r)return;let a=r.selectedDataset;if(null===a)return;let l=n.datasets.getDatasetById(a);if(!l)return;let o=l.getName();i.month(),i.daysInMonth(),i.year();let s=bo("30","tracker-month-label"),u=2.8*Math.max(s.width,s.height),f=i.format("YYYY"),c=i.format("MMM"),h=bo(f,"tracker-month-header-year"),d=bo(c,"tracker-month-header-month"),g=0,p=e.graphArea.append("g"),y=null;r.headerMonthColor?y=r.headerMonthColor:r.color&&(y=r.color);let m=p.append("text").text(c).attr("id","titleMonth").attr("transform","translate("+u/4+","+d.height+")").attr("class","tracker-month-header-month").style("cursor","default").on("click",(function(t){Go(e,r)}));y&&m.style("fill",y),g+=d.height;let v=null;r.headerYearColor?v=r.headerYearColor:r.color&&(v=r.color);let x=p.append("text").text(f).attr("id","titleYear").attr("transform","translate("+u/4+","+(g+h.height)+")").attr("class","tracker-month-header-year").style("cursor","default").attr("font-weight","bold").on("click",(function(t){Go(e,r)}));v&&x.style("fill",v),g+=h.height,"annotation"===r.mode&&r.showAnnotationOfAllTargets&&r.dataset.length>1&&(o="All Targets");let A=bo(o,"tracker-month-title-rotator"),w=p.append("text").text(o).attr("transform","translate("+3.5*u+","+A.height+")").attr("class","tracker-month-title-rotator").style("cursor","pointer");r.showAnnotationOfAllTargets&&"annotation"===r.mode||w.on("click",(function(a){jo(n,r)&&(Go(e,r),Ko(t,e,n,r,i))})),e.rotator=w;let b=bo("0.0000","tracker-month-title-monitor"),D=p.append("text").text("").attr("id","monitor").attr("class","tracker-month-title-monitor").attr("transform","translate("+3.5*u+","+(A.height+b.height)+")").style("cursor","pointer").style("fill",r.selectedRingColor);e.monitor=D;let k=bo("<","tracker-month-title-arrow");p.append("text").text("<").attr("id","arrowLeft").attr("transform","translate("+5.5*u+","+(g/2+k.height/2)+")").attr("class","tracker-month-title-arrow").on("click",(function(a){Go(e,r),r.selectedDate="";let l=i.clone().add(-1,"month");Ko(t,e,n,r,l)})).style("cursor","pointer"),p.append("text").text(">").attr("id","arrowLeft").attr("transform","translate("+6.5*u+","+(g/2+k.height/2)+")").attr("class","tracker-month-title-arrow").on("click",(function(a){Go(e,r);let l=i.clone().add(1,"month");Ko(t,e,n,r,l)})).style("cursor","pointer"),p.append("text").text("◦").attr("id","arrowToday").attr("transform","translate("+6*u+","+(g/2+k.height/2)+")").attr("class","tracker-month-title-arrow").on("click",(function(i){Go(e,r);let a=mo(n.dateFormat);Ko(t,e,n,r,a)})).style("cursor","pointer"),g+=8;let C=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];"mon"===r.startWeekOn.toLowerCase()&&C.push(C.shift());let M=bo(C[0],"tracker-month-weekday");e.graphArea.selectAll("weekDays").data(C).enter().append("text").text((function(t){return t})).attr("transform",(function(t,e){return"translate("+(e+.5)*u+","+(g+M.height)+")"})).attr("class","tracker-month-weekday").attr("text-anchor","middle").style("cursor","default").on("click",(function(t){Go(e,r)})),g+=M.height+8;let T=null;r.dividingLineColor?T=r.dividingLineColor:r.color&&(T=r.color);let _=e.graphArea.append("rect").attr("x",0).attr("y",g).attr("width",6.5*u+M.width).attr("height",1).attr("class","tracker-month-dividing-line");T&&_.style("fill",T),g+=1,p.attr("height",g),e.header=p,ko(e.dataArea,0,g)}function Zo(t,e,n,r,i){if(!n||!r)return;let a=r.mode;if("circle"!==a&&"annotation"!==a)return"Unknown month view mode";let l=r.selectedDataset;if(null===l)return;let o=n.datasets.getDatasetById(l);if(!o)return;let s=r.dataset.findIndex((t=>t===l));l<0&&(s=0);let u=r.threshold[s];i.month(),i.daysInMonth();let f=bo("30","tracker-month-label"),c=2.8*Math.max(f.width,f.height),h=c/2.8*1.8/2,d=(c-2*h)/2,g=m(o.getValues());null!==r.yMin[s]&&(g=r.yMin[s]);let p=y(o.getValues());null!==r.yMax[s]&&(p=r.yMax[s]);let v=!0;(null===p||null===g||p<=g)&&(v=!1);const x=i.clone().startOf("month");let A=x.clone().subtract(x.day(),"days");"mon"===r.startWeekOn.toLowerCase()&&(A=A.add(1,"days"));const w=i.clone().endOf("month");let b=w.clone().add(7-w.day()-1,"days");"mon"===r.startWeekOn.toLowerCase()&&(b=b.add(1,"days"));const D=o.getStartDate(),k=o.getEndDate();let C=r.showAnnotation,M=r.annotation,T=M[s],_=r.showAnnotationOfAllTargets,E=[],F=0,S=0,B=0;for(let t=A.clone();t<=b;t.add(1,"days")){t=fo(po(t,n.dateFormat),n.dateFormat),"2021-09-13"===t.format("YYYY-MM-DD")&&(Xo=!1),"mon"===r.startWeekOn.toLowerCase()?(F=t.day()-1,F<0&&(F=6),S=Math.floor(B/7)):(F=t.day(),S=Math.floor(B/7));let e=!0;(t.diff(x)<0||t.diff(w)>0)&&(e=!1);let i=!0;D&&k&&t.diff(D)>=0&&t.diff(k)<=0&&(i=!1);const a=o.getValue(t);Xo&&(console.log(o),console.log(po(t,n.dateFormat)),console.log(a));const l=null!=a&&a>u;let s=null;r.circleColorByValue&&v&&null!==a&&(s=(a-g)/(p-g)),Xo&&(console.log(g),console.log(p),console.log(s));let f=o.getValue(t,1),c=o.getValue(t,-1),h=!1;null!==a&&a>u&&null!==c&&c>u&&(h=!0);let d=!1;null!==a&&a>u&&null!==f&&f>u&&(d=!0),Xo&&(console.log(`preValue: ${c}, curValue: ${a}, nextValue: ${f}`),console.log(r.threshold),console.log(`streakIn: ${h}, streakOut: ${d}`));let y="";if(C)if(_)for(let e of r.dataset){let i=r.dataset.findIndex((t=>t===e));if(i>=0){let a=n.datasets.getDatasetById(e).getValue(t),l=r.threshold[i];null!==a&&a>l&&(y+=M[i])}}else a>u&&(y=T);E.push({date:po(t,n.dateFormat),value:a,scaledValue:s,dayInMonth:t.date(),isInThisMonth:e,isOutOfDataRange:i,row:S,col:F,showCircle:l,streakIn:h,streakOut:d,annotation:y}),B++,Xo&&(Xo=!1)}let N=(F+1)*c,L=Gr().domain([-.5,6.5]).range([0,N]);if("circle"===a&&r.showCircle&&r.showStreak){let t="#69b3a2";r.circleColor?t=r.circleColor:r.color&&(t=r.color),e.dataArea.selectAll("streakIn").data(E.filter((function(t){return t.streakIn}))).enter().append("rect").attr("x",(function(t){return L(t.col)-h-d})).attr("y",(function(t){return L(t.row)-1.5})).attr("width",d).attr("height",3).style("fill",(function(e){return e.showCircle?r.circleColorByValue?null!==e.scaledValue?xn("white",t)(.8*e.scaledValue+.2):"none":t:"none"})).style("opacity",(function(t){return t.isOutOfDataRange||r.dimNotInMonth&&!t.isInThisMonth?.2:1})),e.dataArea.selectAll("streakOut").data(E.filter((function(t){return t.streakOut}))).enter().append("rect").attr("x",(function(t){return L(t.col)+h})).attr("y",(function(t){return L(t.row)-1.5})).attr("width",d).attr("height",3).style("fill",(function(e){return e.showCircle?r.circleColorByValue?null!==e.scaledValue?xn("white",t)(.8*e.scaledValue+.2):"none":t:"none"})).style("opacity",(function(t){return t.isOutOfDataRange||r.dimNotInMonth&&!t.isInThisMonth?.2:1}))}let I="#69b3a2";r.circleColor?I=r.circleColor:r.color&&(I=r.color),"circle"===a&&r.showCircle&&e.dataArea.selectAll("dot").data(E).enter().append("circle").attr("r",h).attr("cx",(function(t){return L(t.col)})).attr("cy",(function(t){return L(t.row)})).style("fill",(function(t){if(t.showCircle){if(!r.circleColorByValue)return I;if(null!==t.scaledValue){return xn("white",I)(.8*t.scaledValue+.2)}return"none"}return"none"})).style("opacity",(function(t){return t.isOutOfDataRange||r.dimNotInMonth&&!t.isInThisMonth?.2:1})).style("cursor","default");let O=po(window.moment(),n.dateFormat);if("circle"===a&&r.showTodayRing){let t=e.dataArea.selectAll("todayRing").data(E.filter((function(t){return t.date===O}))).enter().append("circle").attr("r",.9*h).attr("cx",(function(t){return L(t.col)})).attr("cy",(function(t){return L(t.row)})).attr("class","tracker-month-today-circle").style("cursor","default");""!==r.todayRingColor?t.style("stroke",r.todayRingColor):t.style("stroke","white")}"circle"===a&&r.showSelectedRing&&e.dataArea.selectAll("selectedRing").data(E).enter().append("circle").attr("r",h).attr("cx",(function(t){return L(t.col)})).attr("cy",(function(t){return L(t.row)})).attr("id",(function(t){return"tracker-selected-circle-"+t.date})).attr("class","tracker-month-selected-circle").style("cursor","default").style("stroke","none"),e.dataArea.selectAll("dayLabel").data(E).enter().append("text").text((function(t){return t.dayInMonth.toString()})).attr("transform",(function(t){return"translate("+L(t.col)+","+(L(t.row)+f.height/4)+")"})).style("fill-opacity",(function(t){return t.isOutOfDataRange||r.dimNotInMonth&&!t.isInThisMonth?.2:1})).attr("date",(function(t){return t.date})).attr("value",(function(t){return t.value})).attr("valueType",(function(t){return Rl[o.valueType]})).attr("class","tracker-month-label").on("click",(function(t){Go(e,r);let n=Qt(this).attr("date");if(r.selectedDate=n,r.showSelectedRing&&e.dataArea.select("#tracker-selected-circle-"+n).style("stroke",r.selectedRingColor),r.showSelectedValue){let t=Qt(this).attr("value"),n="";if("Time"===Qt(this).attr("valueType")){n=window.moment("00:00","HH:mm",!0).add(parseFloat(t),"seconds").format("HH:mm")}else n=t;e.monitor.text(n)}})).style("cursor","pointer"),"annotation"===a&&C&&e.dataArea.selectAll("dayAnnotation").data(E).enter().append("text").text((function(t){return t.annotation})).attr("transform",(function(t){let e=L(t.col),n=L(t.row)+f.height/4;return t.annotation&&(n+=h),"translate("+e+","+n+")"})).attr("class","tracker-month-annotation");let V=parseFloat(e.svg.attr("width")),P=parseFloat(e.svg.attr("height")),$=parseFloat(e.graphArea.attr("width")),Y=parseFloat(e.graphArea.attr("height")),R=7*c+parseFloat(e.header.attr("height")),z=7*c;R>P&&Do(e.svg,0,R-P),z>V&&Do(e.svg,z-V,0),R>Y&&Do(e.graphArea,0,R-Y),z>$&&Do(e.svg,z-$,0)}function Ko(t,e,n,r,i){n&&Jo&&(Qo(t,e=qo(e,t,n),n,r,i),Zo(0,e,n,r,i),Wo(t,e,n))}function Jo(t,e,n){if(!e||!Jo)return;n.dataset;let r=0;for(let t of e.datasets)t.getQuery().usedAsXDataset||r++;if(0===r)return"No available dataset found";if(jo(e,n),null===n.selectedDataset)return"No available dataset found";let i={};i=qo(i,t,e);let a=null;if(n.initMonth){if(a=go(n.initMonth,e.dateFormat),!a){let t=window.moment(n.initMonth,"YYYY-MM",!0);if(!t.isValid())return"Invalid initMonth";a=t}}else a=e.datasets.getDates().last();a&&(Qo(t,i,e,n,a),Zo(0,i,e,n,a),Wo(t,i,e))}function ts(t,e,n){if(e&&ts)return"Under construction"}function es(t,e,n){if(!e||!n)return;let r=parseFloat(n.dataset);e.datasets.getDatasetById(r),"horizontal"===n.orientation?e.dataAreaSize={width:250,height:24}:"vertical"===n.orientation&&(e.dataAreaSize={width:24,height:250});let i=function(t,e,n){let r={};if(!e||!n)return;let i=Qt(t).append("svg").attr("id","svg").attr("width",e.dataAreaSize.width+e.margin.left+e.margin.right).attr("height",e.dataAreaSize.height+e.margin.top+e.margin.bottom);r.svg=i;let a=i.append("g").attr("id","graphArea").attr("transform","translate("+e.margin.left+","+e.margin.top+")").attr("width",e.dataAreaSize.width+e.margin.right).attr("height",e.dataAreaSize.height+e.margin.bottom);r.graphArea=a;let l=a.append("g").attr("id","dataArea").attr("width",e.dataAreaSize.width).attr("height",e.dataAreaSize.height);return r.dataArea=l,r}(t,e,n),a=function(t,e,n){if(!e||!n)return;let r=n.range,i=r[r.length-1],a=[0,i],l=n.valueUnit,o=function(t){return l&&l.endsWith("%")?jr(0,i,7)(t)+" %":jr(0,i,7)(t)},s=bo(o(i),"tracker-tick-label");if("horizontal"===n.orientation){let n=Gr();n.domain(a).range([0,e.dataAreaSize.width]),t.scale=n;let r=S(n);r.tickFormat(o);let i=t.dataArea.append("g").attr("id","axis").attr("transform","translate(0,"+e.dataAreaSize.height+")").call(r).attr("class","tracker-axis");t.axis=i,i.selectAll("path").style("stroke","none"),i.selectAll("line"),i.selectAll("text").attr("class","tracker-tick-label"),i.attr("width",e.dataAreaSize.width+s.width),i.attr("height",6+s.height),Do(t.svg,+s.width,6+s.height),Do(t.graphArea,+s.width,6+s.height)}else if("vertical"===n.orientation){let n=Gr();n.domain(a).range([e.dataAreaSize.height,0]),t.scale=n;let r=B(n);r.tickFormat(o);let i=t.dataArea.append("g").attr("id","axis").attr("x",0).attr("y",0).call(r).attr("class","tracker-axis");t.axis=i,i.selectAll("path").style("stroke","none"),i.selectAll("line"),i.selectAll("text").attr("class","tracker-tick-label"),i.attr("width",6+s.width),i.attr("height",e.dataAreaSize.width),Do(t.svg,6+s.width,0),Do(t.graphArea,6+s.width,0),ko(t.dataArea,6+s.width,0)}}(i,e,n);if("string"==typeof a)return a;!function(t,e,n){if(!e||!n)return;if(n.title){let r=bo(n.title,"tracker-title-small");if("horizontal"===n.orientation){let i=t.graphArea.append("text").text(n.title).attr("id","title").attr("x",r.width/2).attr("y",e.dataAreaSize.height/2).attr("height",r.height).attr("class","tracker-title-small");t.title=i,Do(t.svg,r.width+6,0),Do(t.graphArea,r.width+6,0),ko(t.dataArea,r.width+6,0)}else if("vertical"===n.orientation){let i=e.dataAreaSize.width/2;r.width>e.dataAreaSize.width&&(Do(t.svg,r.width-e.dataAreaSize.width,0),Do(t.graphArea,r.width-e.dataAreaSize.width,0),ko(t.dataArea,r.width/2-e.dataAreaSize.width/2,0),i=r.width/2);let a=parseFloat(t.axis.attr("width")),l=t.graphArea.append("text").text(n.title).attr("id","title").attr("x",i+a).attr("y",r.height/2).attr("height",r.height).attr("class","tracker-title-small");t.title=l,Do(t.svg,0,r.height+6),Do(t.graphArea,0,r.height+6),ko(t.dataArea,0,r.height+6)}}if(n.valueUnit){let r=bo(n.valueUnit,"tracker-tick-label");if("horizontal"===n.orientation){let i=t.dataArea.append("text").text(n.valueUnit).attr("id","unit").attr("x",-1*(r.width+6)).attr("y",e.dataAreaSize.height+6).attr("height",r.height).attr("class","tracker-tick-label");t.unit=i}else if("vertical"===n.orientation){let i=t.dataArea.append("text").text(n.valueUnit).attr("id","unit").attr("x",e.dataAreaSize.width/2-r.width/2).attr("y",-(r.height/2+6)).attr("height",r.height).attr("class","tracker-tick-label");t.unit=i,Do(t.svg,0,r.height+6),Do(t.graphArea,0,r.height+6),ko(t.dataArea,0,r.height+6)}}}(i,e,n),function(t,e,n){if(!e||!n)return;let r=t.scale,i=n.range,a=n.rangeColor,l=[],o=0;for(let t=0;t<i.length;t++)l.push({start:o,end:i[t],color:a[t]}),o=i[t];"horizontal"===n.orientation?t.dataArea.selectAll("backPanel").data(l).enter().append("rect").attr("x",(function(t,e){return Math.floor(r(t.start))})).attr("y",(function(t){return 0})).attr("width",(function(t,e){return Math.ceil(r(t.end-t.start))})).attr("height",e.dataAreaSize.height).style("fill",(function(t){return t.color})):"vertical"===n.orientation&&t.dataArea.selectAll("backPanel").data(l).enter().append("rect").attr("x",(function(t,e){return 0})).attr("y",(function(t){return Math.floor(r(t.end))})).attr("width",e.dataAreaSize.width).attr("height",(function(t){return e.dataAreaSize.height-Math.floor(r(t.end-t.start))})).style("fill",(function(t){return t.color}))}(i,e,n);let l=function(t,e,n){let r="";if(!e||!n)return;let i=Ro(n.value,e);if("string"==typeof i)return i;let a=i;if(Number.isNaN(a))return r="Invalid input value: "+i,r;let l=n.valueColor,o=t.scale;if("horizontal"===n.orientation){let n=e.dataAreaSize.height/3;t.dataArea.append("rect").attr("x",o(0)).attr("y",n).attr("width",Math.floor(o(a))).attr("height",n).style("fill",l)}else if("vertical"===n.orientation){let n=e.dataAreaSize.width/3;t.dataArea.append("rect").attr("x",n).attr("y",Math.floor(o(a))).attr("width",n).attr("height",e.dataAreaSize.height-Math.floor(o(a))).style("fill",l)}}(i,e,n);if("string"==typeof l)return l;!function(t,e,n){if(!e||!n)return;if(!n.showMarker)return;let r=n.markerValue,i=n.markerColor,a=t.scale;if("horizontal"===n.orientation){let n=2*e.dataAreaSize.height/3;t.dataArea.append("rect").attr("x",a(r)-1.5).attr("y",n/4).attr("width",3).attr("height",n).style("fill",i)}else if("vertical"===n.orientation){let n=2*e.dataAreaSize.width/3;t.dataArea.append("rect").attr("x",n/4).attr("y",a(r)-1.5).attr("width",n).attr("height",3).style("fill",i)}}(i,e,n),function(t,e,n){let r=Qt(t),i=e.svg,a=parseFloat(i.attr("width")),l=parseFloat(i.attr("height"));i.attr("width",null).attr("height",null).attr("viewBox",`0 0 ${a} ${l}`).attr("preserveAspectRatio","xMidYMid meet"),n.fitPanelWidth?r.style("width","100%"):(r.style("width",(a*n.fixedScale).toString()+"px"),r.style("height",(l*n.fixedScale).toString()+"px"))}(t,i,e)}function ns(t,e,n){if(!e||!n)return;let r=e.datasets,i=function(t){let e,n;for(const r of t)null!=r&&(void 0===e?r>=r&&(e=n=r):(e>r&&(e=r),n<r&&(n=r)));return[e,n]}(r.getDates()),a=sl().domain(i).range([0,e.dataAreaSize.width]);t.xScale=a;let l=ho(n.xAxisTickInterval),[o,s]=function(t,e){let n=[],r=null;if(e){let r=t[0],i=t[t.length-1];n=hi.range(r.toDate(),i.toDate(),e.asDays())}else{let e=t.length;r=e<=15?hi:e<=60?hi.every(4):e<=105?yi:e<=450?Si:e<=900?Si.every(2):Ni}return[n,r]}(r.getDates(),l),u=function(t,e){if(e){function n(t){return po(window.moment(t),e)}return n}{let r=null,i=t.length;return r=Ri(i<=15||i<=60||i<=105?"%y-%m-%d":i<=450||i<=900?"%y %b":"%Y"),r}}(r.getDates(),n.xAxisTickLabelFormat),f=S(a);o&&0!==o.length?f.tickValues(o):s&&f.ticks(s),u&&f.tickFormat(u);let c=t.dataArea.append("g").attr("id","xAxis").attr("transform","translate(0,"+e.dataAreaSize.height+")").call(f).attr("class","tracker-axis");n.xAxisColor&&c.style("stroke",n.xAxisColor),t.xAxis=c;let h=bo("99-99-99"),d=c.selectAll("text").attr("x",-1*h.height*Math.cos(65/180*Math.PI)).attr("y",0).attr("transform","rotate(-65)").style("text-anchor","end").attr("class","tracker-tick-label");n.xAxisColor&&d.style("fill",n.xAxisColor);let g=h.width*Math.sin(65/180*Math.PI),p=c.append("text").text(n.xAxisLabel).attr("transform","translate("+e.dataAreaSize.width/2+","+(6+g)+")").attr("class","tracker-axis-label");n.xAxisLabelColor&&p.style("fill",n.xAxisLabelColor),c.attr("height",6+g),Do(t.svg,0,6+g),Do(t.graphArea,0,6+g)}function rs(t,e,n,r,i){if(!e||!n)return;let a=e.datasets;if(0===i.length)return;if("left"!==r&&"right"!==r)return;let l=null,o=null,s=null,u=!1;for(let t of i){let e=a.getDatasetById(t);if(!e.getQuery().usedAsXDataset)if((null===l||e.getYMin()<l)&&(l=e.getYMin()),(null===o||e.getYMax()>o)&&(o=e.getYMax()),u=e.valueType===Rl.Time,null===s)s=u;else if(u!==s)return"Not all values in time format"}let f=null;"left"===r?f=n.yMin[0]:"right"===r&&(f=n.yMin[1]);let c=!1;"number"!=typeof f?f=l:c=!0;let h=null;"left"===r?h=n.yMax[0]:"right"===r&&(h=n.yMax[1]);let d=!1;if("number"!=typeof h?h=o:d=!0,h<f){let t=f;f=h,h=t;let e=c;c=d,d=e}let g,p,y=h-f,m=Gr();g=c?f:f-.2*y,p=d?h:h+.2*y,n.GetGraphType()===Yl.Bar&&(p<0&&(p=0),g>0&&(g=0));let v=[g,p];("left"===r&&n.reverseYAxis[0]||"right"===r&&n.reverseYAxis[1])&&(v=[p,g]),m.domain(v).range([e.dataAreaSize.height,0]),"left"===r?t.leftYScale=m:"right"===r&&(t.rightYScale=m);let x="";"left"===r?x=n.yAxisColor[0]:"right"===r&&(x=n.yAxisColor[1]);let A="";"left"===r?A=n.yAxisLabelColor[0]:"right"===r&&(A=n.yAxisLabelColor[1]);let b="";"left"===r?b=n.yAxisLabel[0]:"right"===r&&(b=n.yAxisLabel[1]);let D="",k=null,C=null;"left"===r?(D=n.yAxisUnit[0],k=n.yAxisTickInterval[0],C=n.yAxisTickLabelFormat[0]):"right"===r&&(D=n.yAxisUnit[1],k=n.yAxisTickInterval[1],C=n.yAxisTickLabelFormat[1]);let M,T=null;if(u?T=ho(k):(T=parseFloat(k),Number.isNumber(T)&&!Number.isNaN(T)||(T=null)),"left"===r?M=B(m):"right"===r&&(M=F(2,m)),M){let t=function(t,e,n,r=!1){if(r){if(n){function i(t){return window.moment("00:00","HH:mm",!0).add(t,"seconds").format(n)}return i}{function a(n){const r=Math.abs(e-t);let i=window.moment("00:00","HH:mm",!0).add(n,"seconds").format("HH:mm");return r>43200&&(n<t||n>e||(n-t)/3600%2<1)&&(i=""),i}return a}}if(n){function l(t){return Bo.sprintf("%"+n,t)}return l}return jr(t,e,10)}(g,p,C,u);t&&M.tickFormat(t);let e=function(t,e,n,r=!1){const i=Math.abs(e-t);let a=[];r?a=n&&window.moment.isDuration(n)?w(t,e,Math.abs(n.asSeconds())):i>18e3?w(t=3600*Math.floor(t/3600),e=3600*Math.ceil(e/3600),3600):w(t=1800*Math.floor(t/1800),e=1800*Math.ceil(e/1800),1800):n&&"number"==typeof n&&(a=w(t,e,n));return 0===a.length?null:a}(g,p,T,u);e&&M.tickValues(e)}let _=t.dataArea.append("g").attr("id","yAxis").call(M).attr("class","tracker-axis");"right"==r&&_.attr("transform","translate("+e.dataAreaSize.width+" ,0)"),"left"===r?t.leftYAxis=_:"right"===r&&(t.rightYAxis=_);let E=_.selectAll("path");x&&E.style("stroke",x);let S=_.selectAll("line");x&&S.style("stroke",x);let N=_.selectAll("text").attr("class","tracker-tick-label");x&&N.style("fill",x);let L=0;for(let t of N)if(t.textContent){let e=bo(t.textContent,"tracker-axis-label");e.width>L&&(L=e.width)}""!==D&&(b+=" ("+D+")");let I=bo(b),O=_.append("text").text(b).attr("transform","rotate(-90)").attr("x",-1*e.dataAreaSize.height/2).attr("class","tracker-axis-label");"left"===r?O.attr("y",-6-L-I.height/2):O.attr("y",6+L+I.height),A&&O.style("fill",A);let V=I.height+L+6;_.attr("width",V),Do(t.svg,V,0),Do(t.graphArea,V,0),"left"===r&&(ko(t.dataArea,V,0),t.title&&ko(t.title,V,0))}function is(t,e,n,r,i){if(!e||!n)return;let a=null;if("left"===i?a=t.leftYScale:"right"===i&&(a=t.rightYScale),n.showLine[r.getId()]){let e=function(t,e){var n=fl(!0),r=null,i=Nl,a=null,l=Dl(o);function o(o){var s,u,f,c=(o=Sl(o)).length,h=!1;for(null==r&&(a=i(f=l())),s=0;s<=c;++s)!(s<c&&n(u=o[s],s,o))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+t(u,s,o),+e(u,s,o));if(f)return a=null,f+""||null}return t="function"==typeof t?t:void 0===t?Ll:fl(t),e="function"==typeof e?e:void 0===e?Il:fl(e),o.x=function(e){return arguments.length?(t="function"==typeof e?e:fl(+e),o):t},o.y=function(t){return arguments.length?(e="function"==typeof t?t:fl(+t),o):e},o.defined=function(t){return arguments.length?(n="function"==typeof t?t:fl(!!t),o):n},o.curve=function(t){return arguments.length?(i=t,null!=r&&(a=i(r)),o):i},o.context=function(t){return arguments.length?(null==t?r=a=null:a=i(r=t),o):r},o}().defined((function(t){return null!==t.value})).x((function(e){return t.xScale(e.date)})).y((function(t){return a(t.value)})),i=t.dataArea.append("path").attr("class","tracker-line").style("stroke-width",n.lineWidth[r.getId()]);n.fillGap[r.getId()]?i.datum(Array.from(r).filter((function(t){return null!==t.value}))).attr("d",e):i.datum(r).attr("d",e),n.lineColor[r.getId()]&&i.style("stroke",n.lineColor[r.getId()])}}function as(t,e,n,r,i){if(!e||!n)return;let a=null;if("left"===i?a=t.leftYScale:"right"===i&&(a=t.rightYScale),n.showPoint[r.getId()]){let i=t.dataArea.selectAll("dot").data(Array.from(r).filter((function(t){return null!==t.value}))).enter().append("circle").attr("r",n.pointSize[r.getId()]).attr("cx",(function(e){return t.xScale(e.date)})).attr("cy",(function(t){return a(t.value)})).attr("date",(function(t){return Ri("%y-%m-%d")(t.date)})).attr("value",(function(t){if(null!==t.value)return Number.isInteger(t.value)?t.value.toFixed(0):t.value.toFixed(2)})).attr("valueType",Rl[r.valueType]).attr("class","tracker-dot");n.pointColor[r.getId()]&&(i.style("fill",n.pointColor[r.getId()]),n.pointBorderColor[r.getId()]&&n.pointBorderWidth[r.getId()]>0&&(i.style("stroke",n.pointBorderColor[r.getId()]),i.style("stroke-width",n.pointBorderWidth[r.getId()]))),n.allowInspectData&&function(t,e,n){let r=e.dataArea.append("svg").style("opacity",0),i=r.append("rect").attr("x",0).attr("y",0),a=r.append("text"),l=a.append("tspan").attr("class","tracker-tooltip-label"),o=a.append("tspan").attr("class","tracker-tooltip-label"),s=3,u=3;t.on("mouseenter",(function(t){const[e,f]=function(t,e){if(t=function(t){let e;for(;e=t.sourceEvent;)t=e;return t}(t),void 0===e&&(e=t.currentTarget),e){var n=e.ownerSVGElement||e;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(e.getScreenCTM().inverse())).x,r.y]}if(e.getBoundingClientRect){var i=e.getBoundingClientRect();return[t.clientX-i.left-e.clientLeft,t.clientY-i.top-e.clientTop]}}return[t.pageX,t.pageY]}(t);let c=0,h=0,d="date: "+Qt(this).attr("date"),g=bo(d,"tracker-tooltip-label");l.text(d),g.width>c&&(c=g.width),h+=g.height,l.attr("x",s).attr("y",h);let p="value: ",y=Qt(this).attr("valueType"),m=Qt(this).attr("value");if("Time"===y){p+=window.moment("00:00","HH:mm",!0).add(parseFloat(m),"seconds").format("HH:mm"),o.text(p)}else p+=m,o.text(p);let v=bo(p,"tracker-tooltip-label");v.width>c&&(c=v.width),h+=u+v.height,o.attr("x",s).attr("y",h),c+=2*s,h+=2*u,a.attr("width",c).attr("height",h),i.attr("width",c).attr("height",h).attr("class","tracker-tooltip");let x=e,A=f,w=12,b=12;x=e+w+c>n.dataAreaSize.width?e-c-w:e+w,A=f-b-h<0?f+b:f-b-h,r.attr("x",x).attr("y",A),r.transition().duration(200).style("opacity",1)})).on("mouseleave",(function(){r.transition().duration(500).style("opacity",0)}))}(i,t,e)}}function ls(t,e,n,r,i,a,l){if(!e||!n)return;let o=e.dataAreaSize.width/r.getLength(),s=o,u=a,f=l;o-1>0&&(s=o-1),e.stack?(u=0,f=1):s/=l;let c=null;"left"===i?c=t.leftYScale:"right"===i&&(c=t.rightYScale);let h=t.dataArea.selectAll("bar").data(Array.from(r).filter((function(t){return null!==t.value}))).enter().append("rect").attr("x",(function(e,n){if(0===n){let n=u+1-f/2;if(n<1)return t.xScale(e.date)-o/2+u*s+n*s}return t.xScale(e.date)-o/2+u*s})).attr("y",(function(t){return c(Math.max(t.value,0))})).attr("width",(function(t,e){if(0===e){let t=u+1-f/2;return t<0?0:t<1?s*t:s}if(e===r.getLength()-1){let t=1-(u+1-f/2);return t<0?0:t<1?s*t:s}return s})).attr("height",(function(t){if(null!==t.value)return Math.abs(c(t.value)-c(0))})).attr("class","tracker-bar");n.barColor[r.getId()]&&h.style("fill",n.barColor[r.getId()])}function os(t,e,n){let r=t.svg;t.graphArea;let i=t.dataArea,a=t.title,l=t.xAxis,o=t.leftYAxis,s=t.rightYAxis,u=0;a&&(u=parseFloat(a.attr("height")));let f=parseFloat(l.attr("height")),c=0;o&&(c=parseFloat(o.attr("width")));let h=0;s&&(h=parseFloat(s.attr("width")));let d=e.datasets,g=d.getXDatasetIds(),p=d.getNames(),y=p.map((function(t){return bo(t,"tracker-legend-label")})),m=0,v=0;for(let t=0;t<p.length;t++)g.includes(t)||y[t].width>v&&(v=y[t].width,m=t);let x=v/p[m].length,A=y[m].height,w=p.length-g.length,D=2*x,k=A,C=2*x,M=0,T=0;"vertical"===n.legendOrientation?(M=3*D+C+v,T=(w+1)*k):"horizontal"===n.legendOrientation&&(M=(2*D+C)*w+D+b(y,(function(t,e){return g.includes(e)?0:t.width})),T=k+A);let _=0,E=0;if("top"===n.legendPosition)_=c+e.dataAreaSize.width/2-M/2,E=u,Do(r,0,T+k),ko(i,0,T+k);else if("bottom"===n.legendPosition)_=c+e.dataAreaSize.width/2-M/2,E=u+e.dataAreaSize.height+f+k,Do(r,0,T+k);else if("left"===n.legendPosition)_=0,E=u+e.dataAreaSize.height/2-T/2,Do(r,M+D,0),ko(i,M+D,0);else{if("right"!==n.legendPosition)return;_=e.dataAreaSize.width+c+h+D,E=u+e.dataAreaSize.height/2-T/2,Do(r,M+D,0)}let F=t.graphArea.append("g").attr("id","legend").attr("transform","translate("+_+","+E+")"),S=F.append("rect").attr("class","tracker-legend").attr("width",M).attr("height",T);n.legendBgColor&&S.style("fill",n.legendBgColor),n.legendBorderColor&&S.style("stroke",n.legendBorderColor);let B=D,N=A,L=B+D+C,I=N;if("vertical"===n.legendOrientation){n.GetGraphType()===Yl.Line?(F.selectAll("markers").data(p).enter().append("line").attr("x1",B).attr("x2",B+C).attr("y1",(function(t,e){let n=g.filter((t=>t<e)).length;return N+(e-=n)*k})).attr("y2",(function(t,e){let n=g.filter((t=>t<e)).length;return N+(e-=n)*k})).style("stroke",(function(t,e){if(!g.includes(e))return n.lineColor[e]})),F.selectAll("markers").data(p).enter().append("circle").attr("cx",B+C/2).attr("cy",(function(t,e){let n=g.filter((t=>t<e)).length;return N+(e-=n)*k})).attr("r",(function(t,e){if(!g.includes(e))return n.showPoint[e]?n.pointSize[e]:0})).style("fill",(function(t,e){if(!g.includes(e))return n.pointColor[e]}))):n.GetGraphType()===Yl.Bar&&F.selectAll("markers").data(p).enter().append("rect").attr("x",B).attr("y",(function(t,e){let n=g.filter((t=>t<e)).length;return N+(e-=n)*k-A/2})).attr("width",C).attr("height",A).style("fill",(function(t,e){if(!g.includes(e))return n.barColor[e]}));let t=F.selectAll("labels").data(p).enter().append("text").attr("x",L).attr("y",(function(t,e){let n=g.filter((t=>t<e)).length;return I+(e-=n)*k})).text((function(t,e){return g.includes(e)?"":t})).style("alignment-baseline","middle").attr("class","tracker-legend-label");n.GetGraphType()===Yl.Line?t.style("fill",(function(t,e){if(!g.includes(e))return n.lineColor[e]})):n.GetGraphType()===Yl.Bar&&t.style("fill",(function(t,e){if(!g.includes(e))return n.barColor[e]}))}else if("horizontal"===n.legendOrientation){n.GetGraphType()===Yl.Line?(F.selectAll("markers").data(p).enter().append("line").attr("x1",(function(t,e){let n=D;for(let[t,r]of y.entries())if(!g.includes(t)){if(!(t<e))break;n+=C+D+r.width+D}return n})).attr("x2",(function(t,e){let n=D+C;for(let[t,r]of y.entries())if(!g.includes(t)){if(!(t<e))break;n+=D+r.width+D+C}return n})).attr("y1",N).attr("y2",N).style("stroke",(function(t,e){if(!g.includes(e))return n.lineColor[e]})),F.selectAll("markers").data(p).enter().append("circle").attr("cx",(function(t,e){let n=D+C/2;for(let[t,r]of y.entries())if(!g.includes(t)){if(!(t<e))break;n+=C/2+D+r.width+D+C/2}return n})).attr("cy",N).attr("r",(function(t,e){if(!g.includes(e))return n.showPoint[e]?n.pointSize[e]:0})).style("fill",(function(t,e){if(!g.includes(e))return n.pointColor[e]}))):n.GetGraphType()===Yl.Bar&&F.selectAll("markers").data(p.filter(((t,e)=>!g.includes(e)))).enter().append("rect").attr("x",(function(t,e){let n=D;for(let[t,r]of y.entries())if(!g.includes(t)){if(!(t<e))break;n+=C+D+r.width+D}return n})).attr("y",N-A/2).attr("width",C).attr("height",A).style("fill",(function(t,e){if(!g.includes(e))return n.barColor[e]}));let t=F.selectAll("labels").data(p).enter().append("text").attr("x",(function(t,e){let n=D+C+D;for(let[t,r]of y.entries())if(!g.includes(t)){if(!(t<e))break;n+=r.width+D+C+D}return n})).attr("y",I).text((function(t,e){return g.includes(e)?"":t})).style("alignment-baseline","middle").attr("class","tracker-legend-label");n.GetGraphType()===Yl.Line?t.style("fill",(function(t,e){if(!g.includes(e))return n.lineColor[e]})):n.GetGraphType()===Yl.Bar&&t.style("fill",(function(t,e){if(!g.includes(e))return n.barColor[e]}))}}function ss(t,e,n){if(!e||!n)return;if(!n.title)return;let r=bo(n.title,"tracker-title"),i=t.graphArea.append("text").text(n.title).attr("id","title").attr("transform","translate("+e.dataAreaSize.width/2+","+r.height/2+")").attr("height",r.height).attr("class","tracker-title");t.title=i,Do(t.svg,0,r.height),Do(t.graphArea,0,r.height),ko(t.dataArea,0,r.height)}function us(t,e,n){let r=Qt(t),i=e.svg,a=parseFloat(i.attr("width")),l=parseFloat(i.attr("height"));i.attr("width",null).attr("height",null).attr("viewBox",`0 0 ${a} ${l}`).attr("preserveAspectRatio","xMidYMid meet"),n.fitPanelWidth?r.style("width","100%"):(r.style("width",(a*n.fixedScale).toString()+"px"),r.style("height",(l*n.fixedScale).toString()+"px"))}function fs(t,e){let n={},r=Qt(t).append("svg").attr("id","svg").attr("width",e.dataAreaSize.width+e.margin.left+e.margin.right).attr("height",e.dataAreaSize.height+e.margin.top+e.margin.bottom);n.svg=r;let i=r.append("g").attr("id","graphArea").attr("transform","translate("+e.margin.left+","+e.margin.top+")").attr("width",e.dataAreaSize.width+e.margin.right).attr("height",e.dataAreaSize.height+e.margin.bottom);n.graphArea=i;let a=i.append("g").attr("id","dataArea").attr("width",e.dataAreaSize.width).attr("height",e.dataAreaSize.height);return n.dataArea=a,n}function cs(t,e,n){if(!e||!n)return;let r=fs(t,e);ss(r,e,n),ns(r,e,n);let i=[],a=[],l=e.datasets.getXDatasetIds();for(let t=0;t<n.yAxisLocation.length;t++){if(l.includes(t))continue;let e=n.yAxisLocation[t];"left"===e.toLowerCase()?i.push(t):"right"===e.toLocaleLowerCase()&&a.push(t)}let o=rs(r,e,n,"left",i);if("string"==typeof o)return o;if(r.leftYAxis&&r.leftYScale)for(let t of i){let i=e.datasets.getDatasetById(t);i.getQuery().usedAsXDataset||(is(r,e,n,i,"left"),as(r,e,n,i,"left"))}let s=rs(r,e,n,"right",a);if("string"==typeof s)return s;if(r.rightYAxis&&r.rightYScale)for(let t of a){let i=e.datasets.getDatasetById(t);i.getQuery().usedAsXDataset||(is(r,e,n,i,"right"),as(r,e,n,i,"right"))}n.showLegend&&os(r,e,n),us(t,r,e)}function hs(t,e,n){if(!e||!n)return;let r=fs(t,e);ss(r,e,n),ns(r,e,n);let i=[],a=[],l=e.datasets.getXDatasetIds();if(e.stack)for(let t=n.yAxisLocation.length-1;t>=0;t--){if(l.includes(t))continue;let e=n.yAxisLocation[t];"left"===e.toLowerCase()?i.push(t):"right"===e.toLocaleLowerCase()&&a.push(t)}else for(let t=0;t<n.yAxisLocation.length;t++){if(l.includes(t))continue;let e=n.yAxisLocation[t];"left"===e.toLowerCase()?i.push(t):"right"===e.toLocaleLowerCase()&&a.push(t)}let o=rs(r,e,n,"left",i);if("string"==typeof o)return o;let s=i.length+a.length,u=0;if(r.leftYAxis&&r.leftYScale)for(let t of i){let i=e.datasets.getDatasetById(t);i.getQuery().usedAsXDataset||(ls(r,e,n,i,"left",u,s),u++)}let f=rs(r,e,n,"right",a);if("string"==typeof f)return f;if(r.rightYAxis&&r.rightYScale)for(let t of a){let i=e.datasets.getDatasetById(t);i.getQuery().usedAsXDataset||(ls(r,e,n,i,"right",u,s),u++)}n.showLegend&&os(r,e,n),us(t,r,e)}function ds(t){switch(t=t.trim().toLowerCase()){case"true":case"1":case"on":case"yes":return!0;case"false":case"0":case"off":case"no":return!1}return null}function gs(t){return"tag"===t.toLowerCase()||"text"===t.toLowerCase()||"frontmatter"===t.toLowerCase()||"wiki"===t.toLowerCase()||"wiki.link"===t.toLowerCase()||"wiki.display"===t.toLowerCase()||"dvfield"===t.toLowerCase()||"table"===t.toLowerCase()||"filemeta"===t.toLowerCase()||"task"===t.toLowerCase()||"task.all"===t.toLowerCase()||"task.done"===t.toLowerCase()||"task.notdone"===t.toLowerCase()}function ps(t){return"left"===t||"right"===t||"none"===t}function ys(t){return!0}function ms(t){const e="::::::tracker::::::";let n=t.split("\\,").join(e).split(",");for(let t=0;t<n.length;t++)n[t]=n[t].split(e).join(",");return n}function vs(t,e,n,r,i){let a=[],l="",o=0;for(;n>a.length;)a.push(r);if(null==e);else if("object"==typeof e&&null!==e){if(Array.isArray(e)){if(e.length>n)return l="Too many inputs for parameter '"+t+"'",l;if(0===e.length)return l="Empty array not allowd for "+t,l;for(let n=0;n<a.length;n++)if(n<e.length){let i=e[n],s=null;if(n>0&&(s=e[n-1].trim()),"string"==typeof i){if(i=i.trim(),""!==i){l="Invalid inputs for "+t;break}a[n]=null!==s?s:r}else{if("boolean"!=typeof i){l="Invalid inputs for "+t;break}a[n]=i,o++}}else{let t=e[e.length-1];a[n]=o>0?t:r}}}else if("string"==typeof e){let i=ms(e);if(i.length>1){if(i.length>n)return l="Too many inputs for parameter '"+t+"'",l;for(let e=0;e<a.length;e++)if(e<i.length){let n=i[e].trim(),s=null;if(e>0&&(s=ds(i[e-1].trim())),""===n)a[e]=null!==s?s:r;else{let r=ds(n);if(null===r){l="Invalid inputs for "+t;break}a[e]=r,o++}}else{let t=ds(i[i.length-1].trim());a[e]=o>0&&null!==t?t:r}}else if(""===e);else{let n=ds(e);if(null!==n){a[0]=n,o++;for(let t=1;t<a.length;t++)a[t]=n}else l="Invalid inputs for "+t}}else if("boolean"==typeof e){a[0]=e,o++;for(let t=1;t<a.length;t++)a[t]=e}else l="Invalid inputs for "+t;return""!==l?l:a}function xs(t,e,n,r,i){let a=[],l="",o=0;for(;n>a.length;)a.push(r);if(null==e);else if("object"==typeof e&&null!==e){if(Array.isArray(e)){if(e.length>n)return l="Too many inputs for parameter '"+t+"'",l;if(0===e.length)return l="Empty array not allowd for "+t,l;for(let n=0;n<a.length;n++)if(n<e.length){let i=e[n],s=null;if(n>0&&(s=e[n-1].trim()),"string"==typeof i){if(i=i.trim(),""!==i){l="Invalid inputs for "+t;break}a[n]=null!==s?s:r}else{if("number"!=typeof i){l="Invalid inputs for "+t;break}a[n]=i,o++}}else{let t=e[e.length-1];a[n]=o>0?t:r}}}else if("string"==typeof e){let i=ms(e);if(i.length>1){if(i.length>n)return l="Too many inputs for parameter '"+t+"'",l;for(let n=0;n<a.length;n++)if(n<i.length){let e=i[n].trim(),s=null;if(n>0&&(s=wo(i[n-1].trim()).value),""===e)null!==s&&Number.isNumber(s)?a[n]=s:a[n]=r;else{let r=wo(e).value;if(null===r){l="Invalid inputs for "+t;break}a[n]=r,o++}}else{let t=wo(i[e.length-1].trim()).value;a[n]=o>0&&null!==t?t:r}}else if(""===e);else{let n=wo(e).value;if(null!==n){a[0]=n,o++;for(let t=1;t<a.length;t++)a[t]=n}else l="Invalid inputs for "+t}}else if("number"==typeof e)if(Number.isNumber(e)){a[0]=e,o++;for(let t=1;t<a.length;t++)a[t]=e}else l="Invalid inputs for "+t;else l="Invalid inputs for "+t;return""!==l?l:a}function As(t,e){return"string"==typeof t?Ao(t):"number"==typeof t?t.toString():e}function ws(t,e,n,r,i,a){let l=[],o="",s=0;for(;n>l.length;)l.push(r);if(null==e);else if("object"==typeof e&&null!==e){if(Array.isArray(e)){if(e.length>n)return o="Too many inputs for parameter '"+t+"'",o;if(0===e.length)return o="Empty array not allowd for "+t,o;for(let n=0;n<l.length;n++)if(n<e.length){let a=e[n],u=null;if(n>0&&(u=e[n-1].trim()),"string"!=typeof a){o="Invalid inputs for "+t;break}if(a=a.trim(),""===a)l[n]=null!==u?u:r;else if(i){if(!i(a)){o="Invalid inputs for "+t;break}l[n]=a,s++}else l[n]=a,s++}else{let t=e[e.length-1].trim();l[n]=s>0?t:r}}}else if("string"==typeof e){let a=ms(e);if(a.length>1){if(a.length>n)return o="Too many inputs for parameter '"+t+"'",o;for(let e=0;e<l.length;e++)if(e<a.length){let n=a[e].trim(),u=null;if(e>0&&(u=a[e-1].trim()),""===n)l[e]=null!==u?u:r;else if(i){if(!i(n)){o="Invalid inputs for "+t;break}l[e]=n,s++}else l[e]=n,s++}else{let t=a[a.length-1].trim();l[e]=s>0?t:r}}else if(""===e);else if(i)if(i(e)){l[0]=e,s++;for(let t=1;t<l.length;t++)l[t]=e}else o="Invalid inputs for "+t;else{l[0]=e,s++;for(let t=1;t<l.length;t++)l[t]=e}}else if("number"==typeof e){let n=e.toString();if(i)if(i(n)){l[0]=n,s++;for(let t=1;t<l.length;t++)l[t]=n}else o="Invalid inputs for "+t;else{l[0]=n,s++;for(let t=1;t<l.length;t++)l[t]=n}}else o="Invalid inputs for "+t;if(a||0!==s||(o="No valid input for "+t),""!==o)return o;for(let t=0;t<l.length;t++)l[t]=Ao(l[t]);return l}function bs(t,e){let n=[];if(null==e)return n;if("object"==typeof e){if(Array.isArray(e))for(let r of e)if("string"==typeof r){let e=parseFloat(r);if(!Number.isNumber(e)){return`Parameter '${t}' accepts only numbers`}n.push(e)}}else if("string"==typeof e){let r=ms(e);if(r.length>1)for(let e of r){let r=parseFloat(e.trim());if(Number.isNaN(r)){return`Parameter '${t}' accepts only numbers`}n.push(r)}else{if(""===e){return`Empty ${t} is not allowed.`}{let r=parseFloat(e);if(!Number.isNumber(r)){return`Parameter '${t}' accepts only numbers`}n.push(r)}}}else{if("number"!=typeof e){return`Invalid ${t}`}n.push(e)}return n}function Ds(t,e){let n=[];if(null==e)return n;if("object"==typeof e){if(Array.isArray(e))for(let t of e)"string"==typeof t&&n.push(t.trim())}else{if("string"!=typeof e){return`Invalid ${t}`}{let r=ms(e);if(r.length>1)for(let t of r)n.push(t.trim());else{if(""===e){return`Empty ${t} is not allowed.`}n.push(e)}}}for(let t=0;t<n.length;t++)n[t]=Ao(n[t]);return n}function ks(t,e){t&&(e.title=As(null==t?void 0:t.title,e.title),e.xAxisLabel=As(null==t?void 0:t.xAxisLabel,e.xAxisLabel),e.xAxisColor=As(null==t?void 0:t.xAxisColor,e.xAxisColor),e.xAxisLabelColor=As(null==t?void 0:t.xAxisLabelColor,e.xAxisLabelColor),"boolean"==typeof t.allowInspectData&&(e.allowInspectData=t.allowInspectData),"boolean"==typeof t.showLegend&&(e.showLegend=t.showLegend),"string"==typeof t.legendPosition?e.legendPosition=t.legendPosition:e.legendPosition="bottom","string"==typeof t.legendOrientation?e.legendOrientation=t.legendOrientation:"top"===e.legendPosition||"bottom"===e.legendPosition?e.legendOrientation="horizontal":"left"===e.legendPosition||"right"===e.legendPosition?e.legendOrientation="vertical":e.legendOrientation="horizontal",e.legendBgColor=As(null==t?void 0:t.legendBgColor,e.legendBgColor),e.legendBorderColor=As(null==t?void 0:t.legendBorderColor,e.legendBorderColor));let n=ws("yAxisLabel",null==t?void 0:t.yAxisLabel,2,"Value",null,!0);if("string"==typeof n)return n;if(n.length>2)return"yAxisLabel accepts not more than two values for left and right y-axes";e.yAxisLabel=n;let r=ws("yAxisColor",null==t?void 0:t.yAxisColor,2,"",ys,!0);if("string"==typeof r)return r;if(r.length>2)return"yAxisColor accepts not more than two values for left and right y-axes";e.yAxisColor=r;let i=ws("yAxisLabelColor",null==t?void 0:t.yAxisLabelColor,2,"",ys,!0);if("string"==typeof i)return i;if(i.length>2)return"yAxisLabelColor accepts not more than two values for left and right y-axes";e.yAxisLabelColor=i;let a=ws("yAxisUnit",null==t?void 0:t.yAxisUnit,2,"",null,!0);if("string"==typeof a)return a;if(a.length>2)return"yAxisUnit accepts not more than two values for left and right y-axes";e.yAxisUnit=a,e.xAxisTickInterval=As(null==t?void 0:t.xAxisTickInterval,e.xAxisTickInterval);let l=ws("yAxisTickInterval",null==t?void 0:t.yAxisTickInterval,2,null,null,!0);if("string"==typeof l)return l;if(l.length>2)return"yAxisTickInterval accepts not more than two values for left and right y-axes";e.yAxisTickInterval=l,e.xAxisTickLabelFormat=As(null==t?void 0:t.xAxisTickLabelFormat,e.xAxisTickLabelFormat);let o=ws("yAxisTickLabelFormat",null==t?void 0:t.yAxisTickLabelFormat,2,null,null,!0);if("string"==typeof o)return o;if(o.length>2)return"yAxisTickLabelFormat accepts not more than two values for left and right y-axes";e.yAxisTickLabelFormat=o;let s=xs("yMin",null==t?void 0:t.yMin,2,null);if("string"==typeof s)return s;if(s.length>2)return"yMin accepts not more than two values for left and right y-axes";e.yMin=s;let u=xs("yMax",null==t?void 0:t.yMax,2,null);if("string"==typeof u)return u;if(u.length>2)return"yMax accepts not more than two values for left and right y-axes";e.yMax=u;let f=vs("reverseYAxis",null==t?void 0:t.reverseYAxis,2,!1);return"string"==typeof f?f:f.length>2?"reverseYAxis accepts not more than two values for left and right y-axes":void(e.reverseYAxis=f)}function Cs(t){let e=[];if(null!==t){const n=Object.keys(t);for(let t of n)e.push(t.toString())}return e}function Ms(t,e){return fo(uo(t.basename,e.dateFormatPrefix,e.dateFormatSuffix),e.dateFormat)}function Ts(t,e,n){let r=window.moment(""),i=t.frontmatter;if(i&&vo(i,e.getTarget())){let t=vo(i,e.getTarget());"string"==typeof t&&(t=uo(t,n.dateFormatPrefix,n.dateFormatSuffix),r=fo(t,n.dateFormat))}return r}function _s(t,e,n){let r,i=window.moment(""),a=new RegExp(e,"gm");for(;r=a.exec(t);)if(void 0!==r.groups&&void 0!==r.groups.value){let t=r.groups.value.trim();if(t=uo(t,n.dateFormatPrefix,n.dateFormatSuffix),i=fo(t,n.dateFormat),i.isValid())return i}return i}function Es(t,e,n){window.moment("");let r=e.getTarget();return e.getParentTarget()&&(r=e.getParentTarget()),_s(t,"(^|\\s)#"+r+"(\\/[\\w-]+)*(:(?<value>[\\d\\.\\/-]*)[a-zA-Z]*)?([\\.!,\\?;~-]*)?(\\s|$)",n)}function Fs(t,e,n){return window.moment(""),_s(t,e.getTarget(),n)}function Ss(t,e,n){window.moment("");let r=e.getTarget();return e.getParentTarget()&&(r=e.getParentTarget()),r=r.replace("-","[\\s\\-]"),_s(t,"(^| |\\t)\\*{0,2}"+r+"\\*{0,2}(::[ |\\t]*(?<value>[\\d\\.\\/\\-\\w,@; \\t:]*))(\\r\\?\\n|\\r|$)",n)}function Bs(e,n,r){let i=window.moment("");if(e&&e instanceof t.TFile){let t=n.getTarget();if("cDate"===t){i=yo(e.stat.ctime,r.dateFormat)}else if("mDate"===t){i=yo(e.stat.mtime,r.dateFormat)}else"name"===t&&(i=Ms(e,r))}return i}function Ns(t,e,n){window.moment("");let r=e.getType(),i=e.getTarget();return i=r===$l.Task?"\\[[\\sx]\\]\\s"+i:r===$l.TaskDone?"\\[x\\]\\s"+i:r===$l.TaskNotDone?"\\[\\s\\]\\s"+i:"\\[[\\sx]\\]\\s"+i,_s(t,i,n)}function Ls(t,e,n,r){if(t.has(e)){t.get(e).push({query:n,value:r})}else{let i=new Array;i.push({query:n,value:r}),t.set(e,i)}}function Is(t,e,n,r,i,a){let l,o=new RegExp(e,"gmu"),s=0,u=!1;for(;l=o.exec(t);)if(a.ignoreAttachedValue[n.getId()])s+=a.constValue[n.getId()],u=!0,n.addNumTargets();else if(void 0!==l.groups&&void 0!==l.groups.value){let t=l.groups.value.trim().split(n.getSeparator());if(!t)continue;if(1===t.length){let e=wo(t[0].trim(),a.textValueMap);null!==e.value&&(e.type===Rl.Time?(s=e.value,u=!0,n.valueType=Rl.Time,n.addNumTargets()):a.ignoreZeroValue[n.getId()]&&0===e.value||(s+=e.value,u=!0,n.addNumTargets()))}else if(t.length>n.getAccessor()&&n.getAccessor()>=0){let e=wo(t[n.getAccessor()].trim(),a.textValueMap);null!==e.value&&(e.type===Rl.Time?(s=e.value,u=!0,n.valueType=Rl.Time,n.addNumTargets()):(s+=e.value,u=!0,n.addNumTargets()))}}else s+=a.constValue[n.getId()],u=!0,n.addNumTargets();if(u){return Ls(r,i.get(a.xDataset[n.getId()]),n,s),!0}return!1}function Os(e,n,r,i,a,l){if(e&&e instanceof t.TFile){let t=r.getTarget(),o=l.get(i.xDataset[r.getId()]);if("cDate"===t){let t=e.stat.ctime;return r.valueType=Rl.Date,r.addNumTargets(),Ls(a,o,r,t),!0}if("mDate"===t){let t=e.stat.mtime;return r.valueType=Rl.Date,r.addNumTargets(),Ls(a,o,r,t),!0}if("size"===t){let t=e.stat.size;return r.addNumTargets(),Ls(a,o,r,t),!0}if("numWords"===t){return Ls(a,o,r,function(t){t=Co(t);var e=new RegExp(["(?:[0-9]+(?:(?:,|\\.)[0-9]+)*|[\\-"+/A-Za-z\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC/.source+"])+",/[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u4E00-\u9FD5]{1}/.source].join("|"),"g");return(t.match(e)||[]).length}(n)),!0}if("numChars"===t){let t=n.length;return r.addNumTargets(),Ls(a,o,r,t),!0}if("numSentences"===t){let t=function(t){return(((t=Co(t))||"").match(/[^.。!！?？\s][^.。!！?？]*(?:[.!?](?!['‘’"“”「」『』]?\s|$)[^.。!！?？]*)*[.。!！?？]?['’"”」』]?(?=\s||$)/gm)||[]).length}(n);return r.addNumTargets(),Ls(a,o,r,t),!0}if("name"===t){let t=0,n=!1,l=wo(e.basename,i.textValueMap);null!==l.value&&(l.type===Rl.Time?(t=l.value,n=!0,r.valueType=Rl.Time,r.addNumTargets()):i.ignoreZeroValue[r.getId()]&&0===l.value||(t+=l.value,n=!0,r.addNumTargets()));let s=null;if(n&&(s=t),null!==s)return Ls(a,o,r,s),!0}}return!1}const Vs={folder:"/",dateFormat:"YYYY-MM-DD"};class Ps extends t.PluginSettingTab{constructor(t,e){super(t,e),this.plugin=e}display(){let{containerEl:e}=this;e.empty(),new t.Setting(e).setName("Default folder location").setDesc("Files in this folder will be parsed and used as input data of the tracker plugin.\nYou can also override it using 'folder' argument in the tracker codeblock.").addText((t=>t.setPlaceholder("Folder Path").setValue(this.plugin.settings.folder).onChange((async t=>{this.plugin.settings.folder=t,await this.plugin.saveSettings()})))),new t.Setting(e).setName("Default date format").setDesc("This format is used to parse the date in your diary title.\nYou can also override it using 'dateFormat' argument in the tracker codeblock.").addText((t=>t.setPlaceholder("YYYY-MM-DD").setValue(this.plugin.settings.dateFormat).onChange((async t=>{this.plugin.settings.dateFormat=t,await this.plugin.saveSettings()}))))}}class $s extends t.Plugin{async onload(){console.log("loading obsidian-tracker plugin"),await this.loadSettings(),this.addSettingTab(new Ps(this.app,this)),this.registerMarkdownCodeBlockProcessor("tracker",this.postprocessor.bind(this)),this.addCommand({id:"add-line-chart-tracker",name:"Add Line Chart Tracker",callback:()=>this.addCodeBlock(Yl.Line)}),this.addCommand({id:"add-bar-chart-tracker",name:"Add Bar Chart Tracker",callback:()=>this.addCodeBlock(Yl.Bar)}),this.addCommand({id:"add-summary-tracker",name:"Add Summary Tracker",callback:()=>this.addCodeBlock(Yl.Summary)})}async loadSettings(){this.settings=Object.assign({},Vs,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}renderErrorMessage(t,e,n){!function(t,e){Qt(t).select("#svg").remove(),Qt(t).append("div").text(e).style("background-color","white").style("margin-bottom","20px").style("padding","10px").style("color","red")}(e,t),n.appendChild(e)}onunload(){console.log("unloading obsidian-tracker plugin")}getFilesInFolder(e,n=!0){let r=[];for(let i of e.children)i instanceof t.TFile?"md"===i.extension&&r.push(i):i instanceof t.TFolder&&n&&(r=r.concat(this.getFilesInFolder(i)));return r}async getFiles(e,n,r=!0){if(!e)return;let i=n.folder,a=n.specifiedFilesOnly,l=n.file,o=n.fileContainsLinkedFiles,s=n.fileMultiplierAfterLink;if(!a){let n=this.app.vault.getAbstractFileByPath(t.normalizePath(i));if(n&&n instanceof t.TFolder){let t=this.getFilesInFolder(n);for(let n of t)e.push(n)}}for(let n of l){let r=n;r.endsWith(".md")||(r+=".md"),r=t.normalizePath(r);let i=this.app.vault.getAbstractFileByPath(r);i&&i instanceof t.TFile&&e.push(i)}let u=1,f=!0;""===s?f=!1:/^[0-9]+$/.test(s)?(u=parseFloat(s),f=!1):/\?<value>/.test(s)||(f=!1);for(let r of o){r.endsWith(".md")||(r+=".md");let i=this.app.vault.getAbstractFileByPath(t.normalizePath(r));if(i&&i instanceof t.TFile){let a=this.app.metadataCache.getFileCache(i),l=(await this.app.vault.adapter.read(i.path)).split(/\r\n|[\n\v\f\r\x85\u2028\u2029]/);if(!(null==a?void 0:a.links))continue;for(let i of a.links){if(!i)continue;let a=this.app.metadataCache.getFirstLinkpathDest(i.link,r);if(a&&a instanceof t.TFile){if(f){let t=i.position.end.line;if(t>=0&&t<l.length){let e=l[t].split(i.original);if(2===e.length){let t,r=e[1].trim(),i=new RegExp(s,"gm");for(;t=i.exec(r);)if(void 0!==t.groups&&void 0!==t.groups.value){let e=wo(t.groups.value.trim(),n.textValueMap);if(null!==e.value){u=e.value;break}}}}}for(let t=0;t<u;t++)e.push(a)}}}}}async postprocessor(e,n,r){const i=document.createElement("div");let a=e.trim(),l=this.app.vault.getConfig("tabSize"),o=Array(l).fill(" ").join("");a=a.replace(/\t/gm,o);let s=function(e,n){let r;try{r=t.parseYaml(e)}catch(t){let e="Error parsing YAML";return console.log(t),e}if(!r)return"Error parsing YAML";let i=Cs(r),a="";if(!i.includes("searchTarget"))return"Parameter 'searchTarget' not found in YAML";let l=[];if("object"==typeof r.searchTarget&&null!==r.searchTarget){if(Array.isArray(r.searchTarget))for(let t of r.searchTarget)if("string"==typeof t){if(""===t){a="Empty search target is not allowed.";break}l.push(t)}}else if("string"==typeof r.searchTarget){let t=ms(r.searchTarget);if(t.length>1)for(let e of t){if(e=e.trim(),""===e){a="Empty search target is not allowed.";break}l.push(e)}else""===r.searchTarget?a="Empty search target is not allowed.":l.push(r.searchTarget)}else a="Invalid search target (searchTarget)";for(let t=0;t<l.length;t++)l[t]=Ao(l[t]);if(""!==a)return a;let o=l.length;if(!i.includes("searchType"))return"Parameter 'searchType' not found in YAML";let s=[],u=ws("searchType",r.searchType,o,"",gs,!1);if("string"==typeof u)return u;for(let t of u)switch(t.toLowerCase()){case"tag":s.push($l.Tag);break;case"frontmatter":s.push($l.Frontmatter);break;case"wiki":s.push($l.Wiki);break;case"wiki.link":s.push($l.WikiLink);break;case"wiki.display":s.push($l.WikiDisplay);break;case"text":s.push($l.Text);break;case"dvfield":s.push($l.dvField);break;case"table":s.push($l.Table);break;case"filemeta":s.push($l.FileMeta);break;case"task":case"task.all":s.push($l.Task);break;case"task.done":s.push($l.TaskDone);break;case"task.notdone":s.push($l.TaskNotDone)}if(s.includes($l.Table)&&s.filter((t=>t!==$l.Table)).length>0)return"searchType 'table' doestn't work with other types for now";let f=[],c=ws("separator",r.separator,o,"",null,!0);if("string"==typeof c)return c;f=c.map((t=>"comma"===t||"\\,"===t?",":t));let h=xs("xDataset",r.xDataset,o,-1);if("string"==typeof h)return h;let d=h.map((t=>t<0||t>=o?-1:t)),g=[];for(let t=0;t<l.length;t++){let e=new Ul(g.length,s[t],l[t]);e.setSeparator(f[t]),d.includes(t)&&(e.usedAsXDataset=!0),g.push(e)}let p=new Wl(g),y=Cs(p),m=["searchType","searchTarget","separator"],v=[],x=[],A=[],w=[],b=[],D=[],k=[];for(let t of i)/^line[0-9]*$/.test(t)&&(v.push(t),m.push(t)),/^bar[0-9]*$/.test(t)&&(x.push(t),m.push(t)),/^pie[0-9]*$/.test(t)&&(A.push(t),m.push(t)),/^summary[0-9]*$/.test(t)&&(w.push(t),m.push(t)),/^bullet[0-9]*$/.test(t)&&(k.push(t),m.push(t)),/^month[0-9]*$/.test(t)&&(b.push(t),m.push(t)),/^heatmap[0-9]*$/.test(t)&&(D.push(t),m.push(t));let C=[];for(let t of i)if(/^dataset[0-9]*$/.test(t)){let e=-1,n=t.replace("dataset","");if(e=""===n?0:parseFloat(n),g.some((t=>t.getId()===e)))return a="Duplicated dataset id for key '"+t+"'",a;C.push(t),m.push(t)}for(let t of i)if(!y.includes(t)&&!m.includes(t))return a="'"+t+"' is not an available key",a;if(0===v.length+x.length+A.length+w.length+k.length+b.length+D.length)return"No output parameter provided, please place line, bar, pie, month, bullet, or summary.";p.folder=As(null==r?void 0:r.folder,n.settings.folder),""===p.folder.trim()&&(p.folder=n.settings.folder);let M=n.app.vault.getAbstractFileByPath(t.normalizePath(p.folder));if(!(M&&M instanceof t.TFolder))return"Folder '"+p.folder+"' doesn't exist";if("string"==typeof r.file){let t=Ds("file",r.file);if("string"==typeof t)return t;p.file=t}if("boolean"==typeof r.specifiedFilesOnly&&(p.specifiedFilesOnly=r.specifiedFilesOnly),"string"==typeof r.fileContainsLinkedFiles){let t=Ds("fileContainsLinkedFiles",r.fileContainsLinkedFiles);if("string"==typeof t)return t;p.fileContainsLinkedFiles=t}p.fileMultiplierAfterLink=As(null==r?void 0:r.fileMultiplierAfterLink,p.fileMultiplierAfterLink);const T=r.dateFormat;if("string"==typeof r.dateFormat?""===r.dateFormat?p.dateFormat=n.settings.dateFormat:p.dateFormat=T:p.dateFormat=n.settings.dateFormat,p.dateFormatPrefix=As(null==r?void 0:r.dateFormatPrefix,p.dateFormatPrefix),p.dateFormatSuffix=As(null==r?void 0:r.dateFormatSuffix,p.dateFormatSuffix),"string"==typeof r.startDate){if(/^([\-]?[0-9]+[\.][0-9]+|[\-]?[0-9]+)m$/.test(r.startDate))return"'m' for 'minute' is too small for parameter startDate, please use 'd' for 'day' or 'M' for month";let t=uo(r.startDate,p.dateFormatPrefix,p.dateFormatSuffix),e=null,n=!1;if(e=go(t,p.dateFormat),e?n=!0:(e=fo(t,p.dateFormat),e.isValid()&&(n=!0)),!n||null===e)return"Invalid startDate, the format of startDate may not match your dateFormat "+p.dateFormat;p.startDate=e}if("string"==typeof r.endDate){if(/^([\-]?[0-9]+[\.][0-9]+|[\-]?[0-9]+)m$/.test(r.endDate))return"'m' for 'minute' is too small for parameter endDate, please use 'd' for 'day' or 'M' for month";let t=uo(r.endDate,p.dateFormatPrefix,p.dateFormatSuffix),e=null,n=!1;if(e=go(t,p.dateFormat),e?n=!0:(e=fo(t,p.dateFormat),e.isValid()&&(n=!0)),!n||null===e)return"Invalid endDate, the format of endDate may not match your dateFormat "+p.dateFormat;p.endDate=e}if(null!==p.startDate&&p.startDate.isValid()&&null!==p.endDate&&p.endDate.isValid()&&p.endDate<p.startDate)return"Invalid date range (startDate larger than endDate)";p.xDataset=d;let _=ws("datasetName",r.datasetName,o,"untitled",null,!0);if("string"==typeof _)return _;let E=0;for(let t=0;t<_.length;t++)p.xDataset.includes(t)||"untitled"===_[t]&&(_[t]="untitled"+E.toString(),E++);if(new Set(_).size!==_.length)return"Not enough dataset names or duplicated names";p.datasetName=_;let F=xs("constValue",r.constValue,o,1);if("string"==typeof F)return F;p.constValue=F;let S=vs("ignoreAttachedValue",r.ignoreAttachedValue,o,!1);if("string"==typeof S)return S;p.ignoreAttachedValue=S;let B=vs("ignoreZeroValue",r.ignoreZeroValue,o,!1);if("string"==typeof B)return B;p.ignoreZeroValue=B;let N=vs("accum",r.accum,o,!1);if("string"==typeof N)return N;p.accum=N,"boolean"==typeof r.stack&&(p.stack=r.stack);let L=xs("penalty",r.penalty,o,null);if("string"==typeof L)return L;p.penalty=L;let I=xs("valueShift",r.valueShift,o,0);if("string"==typeof I)return I;p.valueShift=I;let O=xs("shiftOnlyValueLargerThan",r.shiftOnlyValueLargerThan,o,null);if("string"==typeof O)return O;if(p.shiftOnlyValueLargerThan=O,void 0!==r.textValueMap){let t=Cs(r.textValueMap);for(let e of t){let t=e.trim();p.textValueMap[t]=r.textValueMap[t]}}if("number"==typeof r.fixedScale&&(p.fixedScale=r.fixedScale),"boolean"==typeof r.fitPanelWidth&&(p.fitPanelWidth=r.fitPanelWidth),"string"==typeof r.aspectRatio){let t=/([0-9]*):([0-9]*)/,e=r.aspectRatio.match(t);e.shift(),e=e.map((t=>parseInt(t,10))),2==e.length&&(p.aspectRatio=new ro(e[0],e[1]),p.dataAreaSize=p.aspectRatio.recalculateSize(p.dataAreaSize))}let V=xs("margin",r.margin,4,10);if("string"==typeof V)return V;if(V.length>4)return"margin accepts not more than four values for top, right, bottom, and left margins.";p.margin=new io(V[0],V[1],V[2],V[3]);for(let t of C){let e=new jl,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;let o=-1,s=t.replace("dataset","");o=""===s?0:parseFloat(s),e.id=o,e.name=As(null==n?void 0:n.name,e.name);let u=Ds("xData",null==n?void 0:n.xData);if("string"==typeof u)return u;e.xData=u;let f=e.xData.length,c=Ds("yData",null==n?void 0:n.yData);if("string"==typeof c)return c;if(e.yData=c,e.yData.length!==f)return"Number of elements in xData and yData not matched";p.customDataset.push(e)}for(let t of v){let e=new Gl,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;let s=ks(n,e);if("string"==typeof s)return s;let u=ws("lineColor",null==n?void 0:n.lineColor,o,"",ys,!0);if("string"==typeof u)return u;e.lineColor=u;let f=xs("lineWidth",null==n?void 0:n.lineWidth,o,1.5);if("string"==typeof f)return f;e.lineWidth=f;let c=vs("showLine",null==n?void 0:n.showLine,o,!0);if("string"==typeof c)return c;e.showLine=c;let h=vs("showPoint",null==n?void 0:n.showPoint,o,!0);if("string"==typeof h)return h;e.showPoint=h;let d=ws("pointColor",null==n?void 0:n.pointColor,o,"#69b3a2",ys,!0);if("string"==typeof d)return d;e.pointColor=d;let g=ws("pointBorderColor",null==n?void 0:n.pointBorderColor,o,"#69b3a2",ys,!0);if("string"==typeof g)return g;e.pointBorderColor=g;let y=xs("pointBorderWidth",null==n?void 0:n.pointBorderWidth,o,0);if("string"==typeof y)return y;e.pointBorderWidth=y;let m=xs("pointSize",null==n?void 0:n.pointSize,o,3);if("string"==typeof m)return m;e.pointSize=m;let v=vs("fillGap",null==n?void 0:n.fillGap,o,!1);if("string"==typeof v)return v;e.fillGap=v;let x=ws("yAxisLocation",null==n?void 0:n.yAxisLocation,o,"left",ps,!0);if("string"==typeof x)return x;e.yAxisLocation=x,p.line.push(e)}for(let t of x){let e=new Ql,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;let s=ks(n,e);if("string"==typeof s)return s;let u=ws("barColor",null==n?void 0:n.barColor,o,"",ys,!0);if("string"==typeof u)return u;e.barColor=u;let f=ws("yAxisLocation",null==n?void 0:n.yAxisLocation,o,"left",ps,!0);if("string"==typeof f)return f;e.yAxisLocation=f,p.bar.push(e)}for(let t of A){let e=new Zl,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;e.title=As(null==n?void 0:n.title,e.title);let o=Ds("data",null==n?void 0:n.data);if("string"==typeof o)return o;e.data=o;let s=e.data.length,u=ws("dataColor",null==n?void 0:n.dataColor,s,null,ys,!0);if("string"==typeof u)return u;e.dataColor=u;let f=ws("dataName",null==n?void 0:n.dataName,s,"",null,!0);if("string"==typeof f)return f;e.dataName=f;let c=ws("label",null==n?void 0:n.label,s,"",null,!0);if("string"==typeof c)return c;e.label=c,"number"==typeof(null==n?void 0:n.hideLabelLessThan)&&(e.hideLabelLessThan=n.hideLabelLessThan);let h=ws("extLabel",null==n?void 0:n.extLabel,s,"",null,!0);if("string"==typeof h)return h;e.extLabel=h,"boolean"==typeof(null==n?void 0:n.showExtLabelOnlyIfNoLabel)&&(e.showExtLabelOnlyIfNoLabel=n.showExtLabelOnlyIfNoLabel),"number"==typeof(null==n?void 0:n.ratioInnerRadius)&&(e.ratioInnerRadius=n.ratioInnerRadius),"boolean"==typeof(null==n?void 0:n.showLegend)&&(e.showLegend=n.showLegend),e.legendPosition=As(null==n?void 0:n.legendPosition,"right");let d="horizontal";d="top"===e.legendPosition||"bottom"===e.legendPosition?"horizontal":"left"===e.legendPosition||"right"===e.legendPosition?"vertical":"horizontal",e.legendOrientation=As(null==n?void 0:n.legendOrientation,d),e.legendBgColor=As(null==n?void 0:n.legendBgColor,e.legendBgColor),e.legendBorderColor=As(null==n?void 0:n.legendBorderColor,e.legendBorderColor),p.pie.push(e)}for(let t of w){let e=new Kl,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;e.template=As(null==n?void 0:n.template,e.template),e.style=As(null==n?void 0:n.style,e.style),p.summary.push(e)}for(let t of b){let e=new Jl,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;e.mode=As(null==n?void 0:n.mode,e.mode);let o=bs("dataset",null==n?void 0:n.dataset);if("string"==typeof o)return o;if(0===o.length)for(let t of g)o.push(t.getId());e.dataset=o;let s=e.dataset.length;e.startWeekOn=As(null==n?void 0:n.startWeekOn,e.startWeekOn),"boolean"==typeof(null==n?void 0:n.showCircle)&&(e.showCircle=n.showCircle);let u=bs("threshold",null==n?void 0:n.threshold);if("string"==typeof u)return u;if(e.threshold=u,0===e.threshold.length)for(let t=0;t<s;t++)e.threshold.push(0);if(e.threshold.length!==e.dataset.length)return"The number of inputs of threshold and dataset not matched";let f=bs("yMin",null==n?void 0:n.yMin);if("string"==typeof f)return f;if(e.yMin=f,0===e.yMin.length)for(let t=0;t<s;t++)e.yMin.push(null);if(e.yMin.length!==e.dataset.length)return"The number of inputs of yMin and dataset not matched";let c=bs("yMax",null==n?void 0:n.yMax);if("string"==typeof c)return c;if(e.yMax=c,0===e.yMax.length)for(let t=0;t<s;t++)e.yMax.push(null);if(e.yMax.length!==e.dataset.length)return"The number of inputs of yMin and dataset not matched";e.color=As(null==n?void 0:n.color,e.color),"boolean"==typeof(null==n?void 0:n.dimNotInMonth)&&(e.dimNotInMonth=n.dimNotInMonth),"boolean"==typeof(null==n?void 0:n.showStreak)&&(e.showStreak=n.showStreak),"boolean"==typeof(null==n?void 0:n.showTodayRing)&&(e.showTodayRing=n.showTodayRing),"boolean"==typeof(null==n?void 0:n.showSelectedValue)&&(e.showSelectedValue=n.showSelectedValue),"boolean"==typeof(null==n?void 0:n.showSelectedRing)&&(e.showSelectedRing=n.showSelectedRing),e.circleColor=As(null==n?void 0:n.circleColor,e.circleColor),"boolean"==typeof(null==n?void 0:n.circleColorByValue)&&(e.circleColorByValue=n.circleColorByValue),e.headerYearColor=As(null==n?void 0:n.headerYearColor,e.headerYearColor),e.headerMonthColor=As(null==n?void 0:n.headerMonthColor,e.headerMonthColor),e.dividingLineColor=As(null==n?void 0:n.dividingLineColor,e.dividingLineColor),e.todayRingColor=As(null==n?void 0:n.todayRingColor,e.todayRingColor),e.selectedRingColor=As(null==n?void 0:n.selectedRingColor,e.selectedRingColor),e.initMonth=As(null==n?void 0:n.initMonth,e.initMonth),"boolean"==typeof(null==n?void 0:n.showAnnotation)&&(e.showAnnotation=n.showAnnotation);let h=Ds("annotation",null==n?void 0:n.annotation);if("string"==typeof h)return h;if(e.annotation=h,0===e.annotation.length)for(let t=0;t<s;t++)e.annotation.push(null);if(e.annotation.length!==e.dataset.length)return"The number of inputs of annotation and dataset not matched";"boolean"==typeof(null==n?void 0:n.showAnnotationOfAllTargets)&&(e.showAnnotationOfAllTargets=n.showAnnotationOfAllTargets),p.month.push(e)}for(let t of D){let e=new to,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;p.heatmap.push(e)}for(let t of k){let e=new eo,n=r[t],i=Cs(e),l=Cs(n);for(let t of l)if(!i.includes(t))return a="'"+t+"' is not an available key",a;e.title=As(null==n?void 0:n.title,e.title),e.dataset=As(null==n?void 0:n.dataset,e.dataset),e.orientation=As(null==n?void 0:n.orientation,e.orientation);let o=bs("range",null==n?void 0:n.range);if("string"==typeof o)return o;let s=o;if(1===s.length){if(s[0]<0)return a="Negative range value is not allowed",a}else{if(!(s.length>1))return a="Empty range is not allowed",a;{let t=s[0];if(t<0)return a="Negative range value is not allowed",a;for(let e=1;e<s.length;e++)if(s[e]<=t)return a="Values in parameter 'range' should be monotonically increasing",a}}e.range=s;let u=s.length,f=ws("rangeColor",null==n?void 0:n.rangeColor,u,"",ys,!0);if("string"==typeof f)return f;e.rangeColor=f,e.value=As(null==n?void 0:n.value,e.value),e.valueUnit=As(null==n?void 0:n.valueUnit,e.valueUnit),e.valueColor=As(null==n?void 0:n.valueColor,e.valueColor),"boolean"==typeof(null==n?void 0:n.showMarker)&&(e.showMarker=n.showMarker),"number"==typeof(null==n?void 0:n.markerValue)&&(e.markerValue=n.markerValue),e.markerColor=As(null==n?void 0:n.markerColor,e.markerColor),p.bullet.push(e)}return p}(a,this);if("string"==typeof s)return this.renderErrorMessage(s,i,n);let u=s,f=[];try{await this.getFiles(f,u)}catch(t){return this.renderErrorMessage(t.message,i,n)}if(0===f.length)return this.renderErrorMessage("No markdown files found in folder",i,n);let c=new Map,h=new oo;h.fileTotal=f.length;const d=f.map((async t=>{let e=null;u.queries.some((t=>{let e=t.getType();return t.getTarget(),e===$l.Frontmatter||e===$l.Tag||e===$l.Wiki||e===$l.WikiLink||e===$l.WikiDisplay}))&&(e=this.app.metadataCache.getFileCache(t));let n=null;u.queries.some((t=>{let e=t.getType(),n=t.getTarget();return e===$l.Tag||e===$l.Text||e===$l.dvField||e===$l.Task||e===$l.TaskDone||e===$l.TaskNotDone||e===$l.FileMeta&&("numWords"===n||"numChars"===n||"numSentences"===n)}))&&(n=await this.app.vault.adapter.read(t.path));let r=new Map,i=!1;for(let a of u.xDataset)if(!r.has(a)){let l=window.moment("");if(-1===a)l=Ms(t,u);else{let r=u.queries[a];switch(r.getType()){case $l.Frontmatter:l=Ts(e,r,u);break;case $l.Tag:l=Es(n,r,u);break;case $l.Text:l=Fs(n,r,u);break;case $l.dvField:l=Ss(n,r,u);break;case $l.FileMeta:l=Bs(t,r,u);break;case $l.Task:case $l.TaskDone:case $l.TaskNotDone:l=Ns(n,r,u)}}l.isValid()?(null!==u.startDate&&l<u.startDate&&(i=!0,h.fileOutOfDateRange++),null!==u.endDate&&l>u.endDate&&(i=!0,h.fileOutOfDateRange++)):(i=!0,h.fileNotInFormat++),i||(h.gotAnyValidXValue||(h.gotAnyValidXValue=!0),r.set(a,po(l,u.dateFormat)),h.fileAvailable++,1==h.fileAvailable?(h.minDate=l.clone(),h.maxDate=l.clone()):(l<h.minDate&&(h.minDate=l.clone()),l>h.maxDate&&(h.maxDate=l.clone())))}if(i)return;const a=u.queries.filter((t=>t.getType()!==$l.Table&&!t.usedAsXDataset)).map((async i=>{if(e&&i.getType()===$l.Tag){let t=function(t,e,n,r,i){let a=t.frontmatter,l=[];if(a&&a.tags){let t=0,o=!1;if(Array.isArray(a.tags))l=l.concat(a.tags);else if("string"==typeof a.tags){let t=a.tags.split(e.getSeparator(!0));for(let e of t){let t=e.trim();""!==t&&l.push(t)}}for(let a of l){if(a===e.getTarget())t+=n.constValue[e.getId()],o=!0,e.addNumTargets();else{if(!a.startsWith(e.getTarget()+"/"))continue;t+=n.constValue[e.getId()],o=!0,e.addNumTargets()}let l=null;return o&&(l=t),Ls(r,i.get(n.xDataset[e.getId()]),e,l),!0}}return!1}(e,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=t)}if(e&&i.getType()===$l.Frontmatter&&"tags"!==i.getTarget()){let t=function(t,e,n,r,i){let a=t.frontmatter;if(a){let t=vo(a,e.getTarget());if(t){let a=wo(t,n.textValueMap);if(null===a.value&&("true"!==t&&"false"!==t||(a.type=Rl.Number,a.value="true"===t?1:0)),null!==a.value)return a.type===Rl.Time&&(e.valueType=Rl.Time),e.addNumTargets(),Ls(r,i.get(n.xDataset[e.getId()]),e,a.value),!0}else if(e.getParentTarget()&&vo(a,e.getParentTarget())){let t=vo(a,e.getParentTarget()),l=null;if(Array.isArray(t)?l=t.map((t=>t.toString())):"string"==typeof t&&(l=t.split(e.getSeparator())),l&&l.length>e.getAccessor()&&e.getAccessor()>=0){let t=wo(l[e.getAccessor()].trim(),n.textValueMap);if(null!==t.value)return t.type===Rl.Time&&(e.valueType=Rl.Time),e.addNumTargets(),Ls(r,i.get(n.xDataset[e.getId()]),e,t.value),!0}}}return!1}(e,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=t)}if(e&&(i.getType()===$l.Wiki||i.getType()===$l.WikiLink||i.getType()===$l.WikiDisplay)){let t=function(t,e,n,r,i){let a=t.links;if(!a)return!1;let l=e.getTarget(),o=e.getType(),s="",u=l;for(let t of a){if(!t)continue;let e="";o===$l.Wiki?e=t.displayText?t.displayText:t.link:o===$l.WikiLink?e=t.link:o===$l.WikiDisplay?t.displayText&&(e=t.displayText):e=t.displayText?t.displayText:t.link,e=e.trim(),s+=e+"\n"}return Is(s,u,e,r,i,n)}(e,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=t)}if(n&&i.getType()===$l.Tag){let t=function(t,e,n,r,i){let a=e.getTarget();return e.getParentTarget()&&(a=e.getParentTarget()),a.length>1&&a.startsWith("#")&&(a=a.substring(1)),Is(t,"(^|\\s)#"+a+"(\\/[\\w-]+)*(:(?<value>[\\d\\.\\/-]*)[a-zA-Z]*)?([\\.!,\\?;~-]*)?(\\s|$)",e,r,i,n)}(n,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=t)}if(n&&i.getType()===$l.Text){let t=function(t,e,n,r,i){return Is(t,e.getTarget(),e,r,i,n)}(n,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=t)}if(i.getType()===$l.FileMeta){let e=Os(t,n,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=e)}if(n&&i.getType()===$l.dvField){let t=function(t,e,n,r,i){let a=e.getTarget();return e.getParentTarget()&&(a=e.getParentTarget()),a=a.replace("-","[\\s\\-]"),Is(t,String.raw`(^| |\t|\|)(\[|\()?\*{0,2}`+a+String.raw`\*{0,2}(::[ |\t]*(?<value>[\p{ExtPict}\d\.\/\-\w,@; \t:`+"\\w$€£¥¢₹₨₱₩฿₫₪α-ωΑ-Ω©®℗™℠一-鿿㐀-䶿　、-〿"+String.raw`]*)(\]|\))?)`,e,r,i,n)}(n,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=t)}if(n&&(i.getType()===$l.Task||i.getType()===$l.TaskDone||i.getType()===$l.TaskNotDone)){let t=function(t,e,n,r,i){let a=e.getType(),l=e.getTarget();return l=a===$l.Task?"\\[[\\sx]\\]\\s"+l:a===$l.TaskDone?"\\[x\\]\\s"+l:a===$l.TaskNotDone?"\\[\\s\\]\\s"+l:"\\[[\\sx]\\]\\s"+l,Is(t,l,e,r,i,n)}(n,i,u,c,r);h.gotAnyValidYValue||(h.gotAnyValidYValue=t)}}));await Promise.all(a)}));if(await Promise.all(d),await this.collectDataFromTable(c,u,h),h.errorMessage)return this.renderErrorMessage(h.errorMessage,i,n);let g="";if(h.minDate.isValid()&&h.maxDate.isValid()&&0!==h.fileAvailable&&h.gotAnyValidXValue||(g="No valid date as X value found in notes",h.fileOutOfDateRange>0&&(g+=`\n${h.fileOutOfDateRange} files are out of the date range.`),h.fileNotInFormat&&(g+=`\n${h.fileNotInFormat} files are not in the right format.`)),null===u.startDate&&null===u.endDate?(u.startDate=h.minDate.clone(),u.endDate=h.maxDate.clone()):null!==u.startDate&&null===u.endDate?u.startDate<h.maxDate?u.endDate=h.maxDate.clone():g="Invalid date range":null!==u.endDate&&null===u.startDate?u.endDate>h.minDate?u.startDate=h.minDate.clone():g="Invalid date range":(u.startDate<h.minDate&&u.endDate<h.minDate||u.startDate>h.maxDate&&u.endDate>h.maxDate)&&(g="Invalid date range"),g)return this.renderErrorMessage(g,i,n);if(!h.gotAnyValidYValue)return this.renderErrorMessage("No valid Y value found in notes",i,n);let p=new Xl(u.startDate,u.endDate);for(let t of u.queries){let e=p.createDataset(t,u);e.addNumTargets(t.getNumTargets());for(let n=u.startDate.clone();n<=u.endDate;n.add(1,"days"))if(c.has(po(n,u.dateFormat))){let r=c.get(po(n,u.dateFormat)).filter((function(e){return e.query.equalTo(t)}));if(r.length>0){let t=null;for(let e=0;e<r.length;e++){let n=r[e].value;Number.isNumber(n)&&!Number.isNaN(n)&&(null===t?t=n:t+=n)}null!==t&&e.setValue(n,t)}}}u.datasets=p;let y=function(t,e){for(let t of e.datasets){if(t.getQuery().usedAsXDataset)continue;let n=e.valueShift[t.getId()];null!==n&&0!==n&&t.shift(n,e.shiftOnlyValueLargerThan[t.getId()]),null!==e.penalty[t.getId()]&&t.setPenalty(e.penalty[t.getId()]),e.accum[t.getId()]&&t.accumulateValues()}if(e.stack){let t=null;for(let n of e.datasets)n.getQuery().usedAsXDataset||(t&&n.shiftByDataset(t),t=n)}for(let n of e.line){let r=cs(t,e,n);if("string"==typeof r)return r}for(let n of e.bar){let r=hs(t,e,n);if("string"==typeof r)return r}for(let n of e.pie){let r=Uo(t,e,n);if("string"==typeof r)return r}for(let n of e.summary){let r=Ho(t,e,n);if("string"==typeof r)return r}for(let n of e.bullet){let r=es(t,e,n);if("string"==typeof r)return r}for(let n of e.month){let r=Jo(t,e,n);if("string"==typeof r)return r}for(let t of e.heatmap){let t=ts(0,e);if("string"==typeof t)return t}}(i,u);if("string"==typeof y)return this.renderErrorMessage(y,i,n);n.appendChild(i)}async collectDataFromTable(e,n,r){let i=n.queries.filter((t=>t.getType()===$l.Table)),a=[],l=!1;for(let e of i){let n=e.getParentTarget(),r=this.app.vault.getAbstractFileByPath(t.normalizePath(n+".md"));if(!(r&&r instanceof t.TFile)){l=!0;break}let i=e.getAccessor(),o=e.usedAsXDataset,s=a.find((t=>t.filePath===n&&t.tableIndex===i));if(s)o?s.xDataset=e:s.yDatasets.push(e);else{let t=new lo(n,i);o?t.xDataset=e:t.yDatasets.push(e),a.push(t)}}if(l)r.errorMessage="File containing tables not found";else for(let i of a){let a=i.xDataset;if(!a)continue;let l=i.yDatasets,o=a.getParentTarget(),s=a.getAccessor(),u="";o+=".md";let f=this.app.vault.getAbstractFileByPath(t.normalizePath(o));if(!(f&&f instanceof t.TFile))continue;{r.fileAvailable++;let t,e=await this.app.vault.adapter.read(f.path),n=new RegExp("((\\r?\\n){2}|^)([^\\r\\n]*\\|[^\\r\\n]*(\\r?\\n)?)+(?=(\\r?\\n){2}|$)","gm"),i=0;for(;t=n.exec(e);){if(i===s){u=t[0];break}i++}}let c=u.split(/\r?\n/);c=c.filter((t=>""!==t));let h=0,d=0;if(c.length>=2){let t=c.shift().trim();t=xo(t,"|"),h=t.split("|").length;let e=c.shift().trim();e=xo(e,"|");let n=e.split("|");for(let t of n)if(!t.includes("-"))break;d=c.length}if(0==d)continue;let g=a.getAccessor(1);if(g>=h)continue;let p=[];for(let t of c){let e=xo(t.trim(),"|").split("|");if(g<e.length){let t=fo(e[g].trim(),n.dateFormat);t.isValid()?(p.push(t),r.minDate.isValid()||r.maxDate.isValid()?(t<r.minDate&&(r.minDate=t.clone()),t>r.maxDate&&(r.maxDate=t.clone())):(r.minDate=t.clone(),r.maxDate=t.clone())):p.push(null)}else p.push(null)}if(p.every((t=>null===t)))return void(r.errorMessage="No valid date as X value found in table");r.gotAnyValidXValue||(r.gotAnyValidXValue=!0);for(let t of l){let i=t.getAccessor(1);if(i>=h)continue;let a=0;for(let l of c){let o=xo(l.trim(),"|").split("|");if(i<o.length){let l=o[i].trim().split(t.getSeparator());if(!l)continue;if(1===l.length){let i=wo(l[0],n.textValueMap);if(null!==i.value){i.type===Rl.Time&&(t.valueType=Rl.Time);let l=i.value;a<p.length&&p[a]&&(r.gotAnyValidYValue||(r.gotAnyValidYValue=!0),Ls(e,po(p[a],n.dateFormat),t,l))}}else if(l.length>t.getAccessor(2)&&t.getAccessor(2)>=0){let i=null,o=wo(l[t.getAccessor(2)].trim(),n.textValueMap);null!==o.value&&(o.type===Rl.Time&&(t.valueType=Rl.Time),i=o.value,a<p.length&&p[a]&&(r.gotAnyValidYValue||(r.gotAnyValidYValue=!0),Ls(e,po(p[a],n.dateFormat),t,i)))}}a++}}}}getEditor(){return this.app.workspace.getActiveViewOfType(t.MarkdownView).editor}addCodeBlock(e){if(!(this.app.workspace.activeLeaf.view instanceof t.MarkdownView))return;let n="";switch(e){case Yl.Line:n='``` tracker\nsearchType: tag\nsearchTarget: tagName\nfolder: /\nstartDate:\nendDate:\nline:\n    title: "Line Chart"\n    xAxisLabel: Date\n    yAxisLabel: Value\n```';break;case Yl.Bar:n='``` tracker\nsearchType: tag\nsearchTarget: tagName\nfolder: /\nstartDate:\nendDate:\nbar:\n    title: "Bar Chart"\n    xAxisLabel: Date\n    yAxisLabel: Value\n```';break;case Yl.Summary:n='``` tracker\nsearchType: tag\nsearchTarget: tagName\nfolder: /\nstartDate:\nendDate:\nsummary:\n    template: "Average value of tagName is {{average()}}"\n    style: "color:white;"\n```'}""!==n&&this.insertToNextLine(n)}insertToNextLine(t){let e=this.getEditor();if(e){let n=e.getCursor(),r=n.line,i=e.getLine(r);return n.ch=i.length,e.setSelection(n),e.replaceSelection("\n"+t),!0}return!1}}module.exports=$s;


/* nosourcemap */