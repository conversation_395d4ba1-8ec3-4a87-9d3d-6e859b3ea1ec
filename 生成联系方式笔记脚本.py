#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成联系方式笔记脚本
从联系方式数据生成每个单独的联系方式笔记
"""

import os
from pathlib import Path

# 联系方式数据
contact_data = [
    # 直辖市
    {
        "name": "北京市互联网信息办公室",
        "address": "北京市朝阳区华威南路弘善家园413号",
        "phone": "010-67676912",
        "level": "直辖市",
        "region": "北京市",
        "province": "北京"
    },
    {
        "name": "天津市互联网信息办公室",
        "address": "天津市河西区梅江道20号",
        "phone": "022-88355322",
        "level": "直辖市",
        "region": "天津市",
        "province": "天津"
    },
    {
        "name": "上海市互联网信息办公室",
        "address": "上海市徐汇区宛平路315号",
        "phone": "021-64743030-2711",
        "level": "直辖市",
        "region": "上海市",
        "province": "上海"
    },
    {
        "name": "重庆市互联网信息办公室",
        "address": "重庆市渝北区青竹东路6号",
        "phone": "023-63151805",
        "level": "直辖市",
        "region": "重庆市",
        "province": "重庆"
    },
    # 省级
    {
        "name": "河北省互联网信息办公室",
        "address": "河北省石家庄市桥西区维明南大街79号",
        "phone": "0311-87909716",
        "level": "省级",
        "region": "河北省",
        "province": "河北"
    },
    {
        "name": "河南省互联网信息办公室",
        "address": "河南省郑州市金水区金水路16号",
        "phone": "0371-65901067",
        "level": "省级",
        "region": "河南省",
        "province": "河南"
    },
    {
        "name": "浙江省互联网信息办公室",
        "address": "浙江省杭州市西湖区省府路29号",
        "phone": "0571-81051250",
        "level": "省级",
        "region": "浙江省",
        "province": "浙江"
    },
    {
        "name": "江苏省互联网信息办公室",
        "address": "江苏省南京市建邺区白龙江东街8号",
        "phone": "025-63090194",
        "level": "省级",
        "region": "江苏省",
        "province": "江苏"
    },
    {
        "name": "福建省互联网信息办公室",
        "address": "福建省福州市鼓楼区北大路133号",
        "phone": "0591-86300613",
        "level": "省级",
        "region": "福建省",
        "province": "福建"
    },
    {
        "name": "安徽省互联网信息办公室",
        "address": "安徽省合肥市包河区中山路1号",
        "phone": "0551-62606014",
        "level": "省级",
        "region": "安徽省",
        "province": "安徽"
    },
    {
        "name": "贵州省互联网信息办公室",
        "address": "贵州省贵阳市云岩区宝山北路39号",
        "phone": "0851-82995001 / 0851-82995061",
        "level": "省级",
        "region": "贵州省",
        "province": "贵州"
    },
    {
        "name": "山东省互联网信息办公室",
        "address": "山东省济南市市中区经十路20637号",
        "phone": "0531-51773249 / 0531-51771297",
        "level": "省级",
        "region": "山东省",
        "province": "山东"
    },
    {
        "name": "广东省互联网信息办公室",
        "address": "广东省广州市越秀区中山一路104号",
        "phone": "020-87100794 / 020-87100793",
        "level": "省级",
        "region": "广东省",
        "province": "广东"
    },
    {
        "name": "陕西省互联网信息办公室",
        "address": "陕西省西安市雁塔区雁塔路南段10号",
        "phone": "029-63907136",
        "level": "省级",
        "region": "陕西省",
        "province": "陕西"
    },
    {
        "name": "甘肃省互联网信息办公室",
        "address": "甘肃省兰州市城关区南昌路1648号",
        "phone": "0931-8928721",
        "level": "省级",
        "region": "甘肃省",
        "province": "甘肃"
    },
    {
        "name": "山西省互联网信息办公室",
        "address": "山西省太原市迎泽区五一路36号",
        "phone": "0351-5236020",
        "level": "省级",
        "region": "山西省",
        "province": "山西"
    },
    {
        "name": "江西省互联网信息办公室",
        "address": "江西省南昌市红谷滩区卧龙路999号",
        "phone": "0791-88912737",
        "level": "省级",
        "region": "江西省",
        "province": "江西"
    },
    {
        "name": "云南省互联网信息办公室",
        "address": "云南省昆明市西山区日新中路516号",
        "phone": "0871-63902424",
        "level": "省级",
        "region": "云南省",
        "province": "云南"
    },
    {
        "name": "湖北省互联网信息办公室",
        "address": "湖北省武汉市武昌区水果湖路268号",
        "phone": "027-87231397",
        "level": "省级",
        "region": "湖北省",
        "province": "湖北"
    },
    {
        "name": "湖南省互联网信息办公室",
        "address": "湖南省长沙市芙蓉区韶山北路1号",
        "phone": "0731-81121089",
        "level": "省级",
        "region": "湖南省",
        "province": "湖南"
    },
    {
        "name": "青海省互联网信息办公室",
        "address": "青海省西宁市海湖新区文景街32号",
        "phone": "0971-8485510",
        "level": "省级",
        "region": "青海省",
        "province": "青海"
    },
    {
        "name": "辽宁省互联网信息办公室",
        "address": "辽宁省沈阳市和平区光荣街26号甲",
        "phone": "024-81680082",
        "level": "省级",
        "region": "辽宁省",
        "province": "辽宁"
    },
    {
        "name": "吉林省互联网信息办公室",
        "address": "吉林省长春市朝阳区新发路666号",
        "phone": "0431-82761087",
        "level": "省级",
        "region": "吉林省",
        "province": "吉林"
    },
    {
        "name": "黑龙江省互联网信息办公室",
        "address": "黑龙江省哈尔滨市南岗区华山路12号",
        "phone": "0451-58685723",
        "level": "省级",
        "region": "黑龙江省",
        "province": "黑龙江"
    },
    {
        "name": "海南省互联网信息办公室",
        "address": "海南省海口市国兴大道69号",
        "phone": "0898-65380723",
        "level": "省级",
        "region": "海南省",
        "province": "海南"
    },
    {
        "name": "四川省互联网信息办公室",
        "address": "四川省成都市青羊区桂花巷21号",
        "phone": "028-86601862",
        "level": "省级",
        "region": "四川省",
        "province": "四川"
    },
    # 自治区
    {
        "name": "广西壮族自治区互联网信息办公室",
        "address": "广西壮族自治区南宁市青秀区民族大道112号",
        "phone": "0771-2093017 / 0771-2093049",
        "level": "自治区",
        "region": "广西壮族自治区",
        "province": "广西"
    },
    {
        "name": "宁夏回族自治区互联网信息办公室",
        "address": "宁夏回族自治区银川市金风区康平路1号",
        "phone": "0951-6668938",
        "level": "自治区",
        "region": "宁夏回族自治区",
        "province": "宁夏"
    },
    {
        "name": "西藏自治区互联网信息办公室",
        "address": "西藏自治区拉萨市城关区农科路7号",
        "phone": "0891-6591509",
        "level": "自治区",
        "region": "西藏自治区",
        "province": "西藏"
    },
    {
        "name": "内蒙古自治区互联网信息办公室",
        "address": "内蒙古自治区呼和浩特市赛罕区银河南街8号",
        "phone": "0471-4821277",
        "level": "自治区",
        "region": "内蒙古自治区",
        "province": "内蒙古"
    },
    {
        "name": "新疆维吾尔自治区互联网信息办公室",
        "address": "新疆维吾尔自治区乌鲁木齐市新市区西环北路2221号",
        "phone": "0991-2384855",
        "level": "自治区",
        "region": "新疆维吾尔自治区",
        "province": "新疆"
    },
    {
        "name": "新疆生产建设兵团互联网信息办公室",
        "address": "新疆维吾尔自治区乌鲁木齐市天山区中山路462号",
        "phone": "0991-2899091",
        "level": "兵团",
        "region": "新疆生产建设兵团",
        "province": "新疆"
    }
]

def generate_contact_note(contact):
    """生成单个联系方式笔记"""
    
    # 生成文件名
    filename = f"联系方式_{contact['province']}_{contact['name'].replace('互联网信息办公室', '网信办')}.md"
    
    # 生成标签
    tags = [contact['province'], '网信办', '数据出境', '联系方式']
    if contact['level'] == '直辖市':
        tags.append('直辖市')
    elif contact['level'] == '省级':
        tags.append('省级')
    elif contact['level'] == '自治区':
        tags.append('自治区')
    elif contact['level'] == '兵团':
        tags.append('兵团')
    
    # 生成内容
    content = f"""# {contact['name']}

---
tags: {tags}
source: 《数据出境合规实务50问》（2024版）
category: 联系方式
type: {contact['level']}部门
level: {contact['level']}
region: {contact['region']}
province: {contact['province']}
department: {contact['name']}
---

## 基本信息
- **单位名称**: {contact['name']}
- **办公地址**: {contact['address']}
- **联系电话**: {contact['phone']}
- **管辖范围**: {contact['region']}
- **行政级别**: {contact['level']}

## 主要职能
1. 负责本{contact['region']}数据出境安全评估的初审工作
2. 协助企业进行个人信息出境标准合同备案
3. 监督本{contact['region']}数据跨境流动合规情况
4. 配合国家网信办开展数据安全监管工作

## 适用场景
- 本{contact['region']}企业申报数据出境安全评估
- 本{contact['region']}企业备案个人信息出境标准合同
- 数据跨境流动相关政策咨询
- 数据安全事件报告和处理

## 申报流程
1. 企业准备申报材料
2. 向省级网信办提交申报
3. 省级网信办进行完备性查验（5个工作日内）
4. 通过查验后提交国家网信办处理
5. 国家网信办确定是否受理（7个工作日内）

## 注意事项
- 建议在工作时间内联系
- 重要事项建议提前预约
- 准备完整的申报材料以提高效率
- 关注最新政策变化和要求更新"""

    return filename, content

def main():
    """主函数"""
    print("开始生成联系方式笔记...")
    
    # 创建输出目录
    output_dir = Path('数据出境合规实务50问汇总')
    
    # 生成每个联系方式笔记
    for contact in contact_data:
        filename, content = generate_contact_note(contact)
        file_path = output_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"创建文件: {filename}")
    
    print(f"\n联系方式笔记生成完成！")
    print(f"共生成 {len(contact_data)} 个联系方式笔记")

if __name__ == "__main__":
    main()
